# -*- mode: python ; coding: utf-8 -*-
import pypylon
import pathlib
pypylon_dir = pathlib.Path(pypylon.__file__).parent
pylon_dlls = [(str(dll), ".") for dll in pypylon_dir.glob("*.so")]

a = Analysis(
    ['backend/app_stk.py'],
    pathex=["."],
    binaries=pylon_dlls,
    datas=[],
    hiddenimports=['engineio', 'engineio.async_drivers.threading', 'socketio', 'flask_socketio', 'threading', 'time', 'queue','eventlet','gevent','uwsgi','six','urllib3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='app_stk',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='app_stk',
)
