import os
import numpy as np
from collections import deque
import datetime
import threading
import time
import sys
import ctypes
from ctypes import cdll
import cv2
import signal
sys.path.append('/opt/MVS/Samples/64/Python/MvImport')
from MvCameraControl_class import (
    MvCamera,
    MV_CC_DEVICE_INFO_LIST,
    MV_GIGE_DEVICE,
    MV_FRAME_OUT_INFO_EX,
    PixelType_Gvsp_RGB8_Packed,
)
# Directory where result images will be saved
# "/mnt/dataset/test_output"
# "/shared/projects\defis_cizeta\04_software"
OUTPUT_DIR = "/mnt/dataset/test_output"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# List of GigE camera serial numbers (line-scan)
TARGET_SERIALS = ["40621531", "40396672"]

# Human-readable location names for each camera serial
CAMERA_LOCATIONS = {
    "40621531": "basler-top",
    "40396672": "basler-bottom",
}

# Number of lines to save before and after an event
PRE_LINES = 100
POST_LINES = 120

# Desired line-scan rate in Hz
DESIRED_LINE_RATE = 4860.0

# Exposure time in microseconds (µs)
# shorter exposure → faster line read
EXPOSURE_TIME = 200   

# Manual white balance ratios (must be within supported range) Min: 1, Max: 16 376  16376   1218  1371
# BGR not RGB
WHITE_MODE = 0  # 0 = Off, 1 = Once, 2 = Auto
RED_RATIO = 1
GREEN_RATIO = 0.4
BLUE_RATIO = 1

# Threshold for sum of grayscale differences on a single line
MIN_DIFF = 100000 
# Buffer size for grabbing
BUF_SIZE = 50 * 1024 * 1024

# -------------------------------------------------------------------------------
# C types structures used by the MVS SDK binding
# -------------------------------------------------------------------------------
class MVCC_ENUMVALUE(ctypes.Structure):
    _fields_ = [
        ("nCurValue", ctypes.c_uint),
        ("nSupportedNum", ctypes.c_uint),
        ("nSupportValue", ctypes.c_uint * 16),
    ]

class MVCC_INTVALUE(ctypes.Structure):
    _fields_ = [
        ("nCurValue", ctypes.c_uint),
        ("nMax", ctypes.c_uint),
        ("nMin", ctypes.c_uint),
        ("nInc", ctypes.c_uint),
    ]

class MVCC_FLOATVALUE(ctypes.Structure):
    _fields_ = [
        ("fCurValue", ctypes.c_float),
        ("fMax", ctypes.c_float),
        ("fMin", ctypes.c_float),
        ("fInc", ctypes.c_float),
    ]

# -------------------------------------------------------------------------------
# Focus metric (variance of Laplacian) for object sharpness only after event
# -------------------------------------------------------------------------------
def compute_focus_metric(frame):
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    lap = cv2.Laplacian(gray, cv2.CV_64F)
    return float(lap.var())

# -------------------------------------------------------------------------------
# Helper to grab one full frame from the camera
# -------------------------------------------------------------------------------
def grab_full_frame(cam):
    info = MV_FRAME_OUT_INFO_EX()
    buf = (ctypes.c_ubyte * BUF_SIZE)()
    ret = cam.MV_CC_GetOneFrameTimeout(buf, BUF_SIZE, info, 1000)
    if ret != 0:
        return None, None

    h, w = info.nHeight, info.nWidth
    arr = np.frombuffer(buf, dtype=np.uint8)[:info.nFrameLen]

    try:
        frame = arr.reshape((h, w, 3))
        # Swap channels from RGB to BGR for correct OpenCV processing
        frame = frame[..., ::-1]
        return frame, h
    except Exception as e:
        print(f"Error reshaping frame: {e}")
        return None, None
# -------------------------------------------------------------------------------
# Import Python wrapper for the MVS SDK
# -------------------------------------------------------------------------------
sys.path.append('/opt/MVS/Samples/64/Python/MvImport')


from MvCameraControl_class import (
    MvCamera,
    MV_CC_DEVICE_INFO_LIST,
    MV_GIGE_DEVICE,
    MV_FRAME_OUT_INFO_EX,
    PixelType_Gvsp_RGB8_Packed,
)

running = True

def shutdown(signum, frame):
    global running
    print("\n🛑 Signal received, shutting down cameras…")
    running = False

signal.signal(signal.SIGINT, shutdown)
signal.signal(signal.SIGTERM, shutdown)
signal.signal(signal.SIGTSTP, shutdown)

def open_camera_by_serial(serial):
    name = CAMERA_LOCATIONS.get(serial, serial)
    sdk = cdll.LoadLibrary("libMvCameraControl.so")
    dev_list = MV_CC_DEVICE_INFO_LIST()
    sdk.MV_CC_EnumDevices(MV_GIGE_DEVICE, ctypes.byref(dev_list))

    print("Connected serials:")
    for i in range(dev_list.nDeviceNum):
        info = dev_list.pDeviceInfo[i].contents
        if info.nTLayerType != MV_GIGE_DEVICE:
            continue
        sn = bytes(info.SpecialInfo.stGigEInfo.chSerialNumber).decode().strip('\0')
        print(f" - {sn}")
        if sn != serial:
            continue

        cam = MvCamera()
        if cam.MV_CC_CreateHandle(info) != 0:
            raise Exception(f"[{name}] CreateHandle failed")
        if cam.MV_CC_OpenDevice() != 0:
            raise Exception(f"[{name}] OpenDevice failed")
       
        # Basic configuration
        cam.MV_CC_SetEnumValue("PixelFormat", PixelType_Gvsp_RGB8_Packed)
        cam.MV_CC_SetEnumValue("TriggerMode", 0)
        cam.MV_CC_SetFloatValue("ExposureTime", EXPOSURE_TIME)
        cam.MV_CC_SetEnumValue("AcquisitionFrameRateEnable", 1)
        cam.MV_CC_SetFloatValue("AcquisitionFrameRate", DESIRED_LINE_RATE)
        # cam.MV_CC_SetEnumValue("BalanceWhiteAuto", 2)
        # -------------------------------------------------------------------
        # Manual white balance calibration via camera registers
        # -------------------------------------------------------------------
        # 1) Disable auto white balance
        ret_awb = cam.MV_CC_SetEnumValue("BalanceWhiteAuto", WHITE_MODE) # 0 = Off, 1 = once, 2 = auto
        print(f"[{name}] AutoWB off, ret={ret_awb}")
        # 2) Set manual balance ratios BGR (selector: 0=Red,1=Green,2=Blue)
        for selector, ratio in ((0, BLUE_RATIO),  # Red channel index 2 in BGR
                                (1, GREEN_RATIO),  # Green
                                (2, RED_RATIO)): # Blue index 0
            cam.MV_CC_SetEnumValue("BalanceRatioSelector", selector)
            ret_ratio = cam.MV_CC_SetIntValue("BalanceRatio", int(ratio))
            print(f"[{name}] Set BalanceRatioSelector={selector}, BalanceRatio={float(ratio)}, ret={ret_ratio}")
        # 3) Read back to verify
        for selector, cname in ((0, "Blue"), (1, "Green"), (2, "Red")):
            cam.MV_CC_SetEnumValue("BalanceRatioSelector", selector)
            iv = MVCC_INTVALUE()
            cam.MV_CC_GetIntValue("BalanceRatio", iv)
            print(f"[{name}] {cname} ratio readback: {iv.nCurValue}")
                
        time.sleep(0.1)

        w, h = MVCC_INTVALUE(), MVCC_INTVALUE()
        ex = MVCC_FLOATVALUE()
        fr = MVCC_FLOATVALUE()
        cam.MV_CC_GetIntValue("Width", w)
        cam.MV_CC_GetIntValue("Height", h)
        cam.MV_CC_GetFloatValue("ExposureTime", ex)
        cam.MV_CC_GetFloatValue("AcquisitionFrameRate", fr)
        print(f"[{name}] Config: W={w.nCurValue}, H={h.nCurValue}, Exp={ex.fCurValue:.1f}µs, Rate={fr.fCurValue:.1f}Hz")
        
        return cam

    raise Exception(f"Camera {serial} not found")

def close_camera(cam):
    for fn in ("MV_CC_StopGrabbing", "MV_CC_CloseDevice", "MV_CC_DestroyHandle"):
        try:
            getattr(cam, fn)()
        except:
            pass

def start_grab_loop(cam, serial):
    name = CAMERA_LOCATIONS.get(serial, serial)
    if cam.MV_CC_StartGrabbing() != 0:
        print(f"[{name}] Failed to start grabbing")
        return

    print(f"🎥 Capturing from camera {name} line-by-line…")
    buffer_lines = deque(maxlen=PRE_LINES)
    saved_lines = []
    recording = False
    post_count = 0
    event_idx = 0

    # Initialize frame counter and heartbeat timer
    frame_counter = 0
    last_heartbeat_time = time.time()

    while running:
        frame, h = grab_full_frame(cam)
        if frame is None:
            time.sleep(0.005)
            continue
        # Calculate the difference in pixel values for the current frame
        frame_counter += 1
        now = time.time()
        # Impresión de heartbeat cada X segundos
        if now - last_heartbeat_time >= 2.0:
            print(f"[{serial}] ◼︎ Recibidos {frame_counter} cuadros. Último diff={diff:.0f}")
            last_heartbeat_time = now

        # line-scan event detection
        for i in range(h):
            line = frame[i:i+1]
            gray = cv2.cvtColor(line, cv2.COLOR_BGR2GRAY)
            diff = float(np.sum(np.abs(gray - gray.mean())))
            buffer_lines.append(line.copy())

            if diff > MIN_DIFF:
                if not recording:
                    print(f"[{name}] ➡️ Event start at line {i}")
                    saved_lines = list(buffer_lines)
                    recording = True
                    post_count = 0
                saved_lines.append(line.copy())
            elif recording:
                saved_lines.append(line.copy())
                post_count += 1
                if post_count >= POST_LINES:
                    print(f"[{name}] ✅ Event end, computing sharpness & saving image")
                    full_img = np.vstack(saved_lines)
                    sharpness = compute_focus_metric(full_img)
                    print(f"[{name}] 📝 Average sharpness over event: {sharpness:.1f}")
                    length_px = full_img.shape[0]
                    ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    fname = f"{name}_event_{ts}_len{length_px}px_{sharpness}.jpg"
                    cv2.imwrite(os.path.join(OUTPUT_DIR, fname), full_img)
                    event_idx += 1
                    recording = False
                    saved_lines = []

    print(f"[{name}] Stopping capture loop…")
    close_camera(cam)

def main():
    cams = {}
    threads = []

    for serial in TARGET_SERIALS:
        name = CAMERA_LOCATIONS.get(serial, serial)
        print(f"🔌 Initializing camera {name}")
        try:
            cams[serial] = open_camera_by_serial(serial)
        except Exception as e:
            print(f"❌ {name} init error: {e}")

    if not cams:
        print("No cameras opened, exiting.")
        return

    for serial, cam in cams.items():
        t = threading.Thread(target=start_grab_loop, args=(cam, serial))
        t.daemon = True
        t.start()
        threads.append(t)

    try:
        while running:
            time.sleep(1)
    finally:
        print("\n🔒 Shutting down cameras…")
        for serial, cam in cams.items():
            print(f"🔧 Closing {CAMERA_LOCATIONS.get(serial, serial)}")
            close_camera(cam)
        print("✅ All cameras closed cleanly.")

if __name__ == "__main__":
    main()



