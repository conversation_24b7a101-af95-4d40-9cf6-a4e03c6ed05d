"""
### File: app_stk.py
### Author: <PERSON> & <PERSON>
### Email: <EMAIL>
### Copyright: Desion GmbH
### Description:
### Changelog:
"""

import base64
import copy
import cv2
import hashlib
import json
import logging
import os
import platform
import shutil
import signal
import struct
import sys
import threading
import time
import traceback
import uuid
from concurrent import futures
from datetime import datetime
from functools import partial
from logging.handlers import TimedRotatingFileHandler
from queue import Empty, Full, Queue, SimpleQueue
from typing import Dict, List, Optional, Union

import cv2
import numpy as np
import pyudev
import sqlalchemy
#from Crypto.Cipher import AES  # only if needed

from flask import (
    Flask,
    Response,
    jsonify,
    redirect,
    render_template,
    request,
    send_from_directory,
    url_for,
)
from flask_cors import CORS
from flask_login import UserMixin, login_required, login_user, logout_user
from sqlalchemy import select, text

from backend.src.setup_stk import *
from backend.src.camera_manager.camera_manager_setup import CameraSetupManager
from backend.src.license.license import *
from backend.src.stockings_analysis.stocking_analysys import *
from backend.src.stockings_analysis.stocking_image_processor import *
from backend.src.utils.files_manager import *
from backend.src.utils.logger_setup import *
from pathlib import Path

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "http://localhost:3000"}})

@app.route('/get_status', methods=['GET'])
def get_status():
    status = {
        "detection": [
            {
                "type": "button",
                "label": "Detection",
                "options": [
                    "true",
                    "scan",
                    ""
                ]
            }
        ],
        "front_camera_1": [
            {
                "type": "button",
                "label": "Cameras",
                "options": [
                    "true",
                    "camera",
                    ""
                ]
            }
        ],
        "server": [
            {
                "type": "button",
                "label": "Server",
                "options": [
                    "true",
                    "server",
                    ""
                ]
            }
        ]
    }
    return jsonify(status)

@app.route('/get_item_info', methods=['GET'])
def get_results():
    frontend_base_url = "http://10.10.4.146:3000/static/output"
    base_disk_path = "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/output"

    stocking_result = request.args.get('stocking_result')

    if stocking_result:
        try:
            stocking_result = json.loads(stocking_result)

            def map_image_path(image_path):
                if not image_path:
                    return f"{frontend_base_url}/fallback.jpg"
                
                # Si chemin absolu
                if image_path.startswith(base_disk_path):
                    relative = image_path.replace(base_disk_path, "").lstrip("/")
                    return f"{frontend_base_url}/{relative}"
                
                # Si déjà relatif
                if "static/output/" in image_path:
                    relative = image_path.split("static/output/")[-1]
                    return f"{frontend_base_url}/{relative}"

                # Cas invalide → fallback
                print(f"[WARN] Unhandled image path format: {image_path}")
                return f"{frontend_base_url}/fallback.jpg"

            if "images" in stocking_result:
                for key in ["top", "bottom"]:
                    stocking_result["images"][key] = map_image_path(stocking_result["images"].get(key))

            return jsonify(stocking_result)

        except json.JSONDecodeError:
            return jsonify({"error": "Invalid JSON format in stocking_result"}), 400

    # Default fallback if no data passed
    default_image = "desion_logo.jpg"
    return jsonify({
        "info": {
            "request_id": "None",
            "product": "STOCKING",
            "date": time.strftime("%d-%m-%Y %H:%M:%S"),
            "color": "#26C1E7"
        },
        "product_info": {
            "Leg Length": "-- cm",
            "Ankle Width": "-- cm",
            "Panty Length": "-- cm"
        },
        "defects": [],
        "decision": "OK",
        "images": {
            "top": f"{frontend_base_url}/{default_image}",
            "bottom": f"{frontend_base_url}/{default_image}"
        }
    })





@app.route('/get_individual_frames', methods=['GET'])
def get_individual_frames():
    """Get all individual frames from specific cameras"""
    try:
        camera_id = request.args.get('camera_id')
        if not camera_id:
            camera_dirs = [d for d in os.listdir("./individual_frames") if os.path.isdir(os.path.join("./individual_frames", d))]
            return jsonify({"status": "success", "cameras": camera_dirs})
        
        frames_dir = f"./individual_frames/{camera_id}"
        if not os.path.exists(frames_dir):
            return jsonify({"status": "error", "message": f"No frames available for camera {camera_id}"})
        
        images = []
        full_stocking_images = []
        for filename in os.listdir(frames_dir):
            filepath = os.path.join(frames_dir, filename)
            if os.path.isfile(filepath) and filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                relative_path = f"individual_frames/{camera_id}/{filename}"
                if filename.startswith('full_stocking'):
                    full_stocking_images.append(relative_path)
                else:
                    images.append(relative_path)
        
        images.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]) if '_' in x else 0)
        full_stocking_images.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]) if '_' in x and x.split('_')[-1].split('.')[0].isdigit() else 0)
        
        return jsonify({
            "status": "success",
            "camera_id": camera_id,
            "individual_frames": images,
            "full_stocking_images": full_stocking_images
        })
    except Exception as e:
        logger.error(f"Error serving individual frames: {str(e)}")
        return jsonify({"status": "error", "message": str(e)})


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=3007, debug=True, use_reloader=False, threaded=True)
