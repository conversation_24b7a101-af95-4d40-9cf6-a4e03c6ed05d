import os
import torch
from ultralytics import YOL<PERSON>
from pathlib import Path
from typing import Optional

def train_model(model_path, results_dir, config, fold_folder, yaml_file, device_id, resume_checkpoint: Optional[Path] = None) -> None:
    # If a resume checkpoint is provided, initialize model with it
    weights = resume_checkpoint if resume_checkpoint is not None else model_path
    model = YOLO(weights)
    
    model(save=True)
    print(f"\nTraining on {fold_folder} using configuration from {yaml_file} on GPU {device_id}")
    os.environ["CUDA_VISIBLE_DEVICES"] = device_id
    
    # Set resume flag based on whether a checkpoint is provided
    resume_flag = True if resume_checkpoint is not None else False

    model.train(
        data=yaml_file,
        epochs=config["epochs"],
        lr0=config["learning_rate"],
        batch=config["batch"],
        project=Path(results_dir) / 'training_results',
        name=f'train_{fold_folder}',
        optimizer=config["optimizer"],
        patience=config["patience"],
        save=config["save"],
        save_period=config["save_period"],
        device=0,
        resume=resume_flag  # Enable resume if checkpoint is provided
    )

    del model
    torch.cuda.empty_cache()
    torch.cuda.ipc_collect()

def resume_model_training(weights) -> None:
    # If a resume checkpoint is provided, initialize model with it
    model = YOLO(weights)
    print(f"\nTraining on {fold_folder} using configuration from {yaml_file} on GPU {device_id}")
    model.train(
        resume=True  # Enable resume if checkpoint is provided
    )

if __name__ == "__main__":
    model_path = Path("/mnt/data/projects/cleanlab/static/models/yolo11x-seg.pt")
    results_dir = Path("/mnt/data/projects/cleanlab/static/results/")
    fold_folder = "YOLO"
    yaml_file = Path("/mnt/data/projects/cleanlab/static/datasets/NIPS_Dataset/YOLO_Dataset/dataset.yaml")
    config = {
        "epochs": 100,
        "learning_rate": 0.001,
        "batch": 16,
        "optimizer": "AdamW",
        "patience": 5,
        "save": True,
        "save_period": 1,
    }
    device_id = "0"
    
    # To start training normally:
    train_model(model_path, results_dir, config, fold_folder, yaml_file, device_id)
    
    # To resume training from a checkpoint (uncomment and set the correct path):
    # resume_epoch_checkpoint = Path("/mnt/data/projects/cleanlab/static/results/yolo11x-seg_results/training_results/train_YOLO5/weights/best.pt")
    # resume_model_training(resume_epoch_checkpoint)