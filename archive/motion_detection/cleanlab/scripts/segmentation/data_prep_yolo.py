"""
Script name: Prepare Dataset for YOLO Segmentation
File name: prepare_dataset.py
Author: <PERSON>
Date: 2025-02-13

Purpose:
    This script processes a dataset to prepare it for YOLO segmentation training. It:
    - Organizes associated and unassociated image-label pairs.
    - Moves unassociated files to a separate folder.
    - Checks for an existing YOLO-formatted dataset or generates one using `labelme2yolo.py` with segmentation support.
    - Consolidates training and validation sets into a single YOLO dataset.
    - Identifies and resolves mismatches between images and labels.
    - Generates a YAML configuration file for training.

Usage:
    python prepare_dataset.py <dataset_path> <destination_path>
"""

import os
import shutil
import subprocess
import argparse
import sys
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from helper.yaml_file_manager import create_config_file
from pathlib import Path

def prepare_dataset(dataset_path: str, destination_path: str) -> Path:
    """
    Prepares a dataset for YOLO segmentation training by verifying, organizing, and converting files.

    :param dataset_path: Path to the dataset directory containing images and labels.
    :param destination_path: Path to store the YOLO-compatible dataset.
    
    :return: Path to the YOLO dataset directory.
    
    :raises FileNotFoundError: If the dataset directory does not exist.
    :raises Exception: For any unexpected errors in file processing.
    """
    dataset_path = Path(dataset_path)
    destination_path = Path(destination_path)
    destination_path.mkdir(exist_ok=True)
    # Directories for organizing files
    yolo_dir = destination_path / "YOLODataset-seg"

    # Check for YOLODataset in destination or associated folder
    potential_yolo_in_destination = yolo_dir
    potential_yolo_in_associated = dataset_path / "YOLODataset_seg"

    if potential_yolo_in_destination.exists():
        print(f"YOLODataset already exists in destination: {yolo_dir}. Skipping creation.")
    elif potential_yolo_in_associated.exists():
        print(f"Found YOLODataset in associated. Moving it to {yolo_dir}.")
        shutil.move(str(potential_yolo_in_associated), str(yolo_dir))
    else:
        print("YOLODataset does not exist. Executing labelme2yolo...")
        labelme2yolo_script = os.path.abspath(
                        os.path.join(os.path.dirname(__file__), '../../utils/labelme2yolo.py')
                    )
        if Path(labelme2yolo_script).exists():
            subprocess.run([
                "python",
                str(labelme2yolo_script),
                "--json_dir", str(dataset_path),
                "--val_size", "0.15",
                "--seg",
                "--config", "./config/default_config.yaml"
            ])
            shutil.move(str(potential_yolo_in_associated), str(yolo_dir))
        else:
            print(f"Error: labelme2yolo.py not found at {labelme2yolo_script}")
            return


    # Consolidate YOLODataset
    for subdir in ["images/train", "images/val"]:
        train_val_dir = yolo_dir / subdir
        if train_val_dir.exists():
            print(f"Consolidating images from {train_val_dir}")
            for file in train_val_dir.iterdir():
                shutil.move(str(file), str(yolo_dir / "images" / file.name))
            shutil.rmtree(train_val_dir)

    for subdir in ["labels/train", "labels/val"]:
        train_val_dir = yolo_dir / subdir
        if train_val_dir.exists():
            print(f"Consolidating labels from {train_val_dir}")
            for file in train_val_dir.iterdir():
                shutil.move(str(file), str(yolo_dir / "labels" / file.name))
            shutil.rmtree(train_val_dir)

    # Verify counts and handle mismatches
    images_count = len(list((yolo_dir / "images").glob("*.jpg")))
    labels_count = len(list((yolo_dir / "labels").glob("*.txt")))

    print(f"Total images: {images_count}, Total labels: {labels_count}")

    if images_count != labels_count:
        print("Mismatch detected between images and labels. Resolving...")
        mismatch_dir = yolo_dir / "mismatched"
        mismatch_dir.mkdir(exist_ok=True)

        image_files = set(f.stem for f in (yolo_dir / "images").glob("*.jpg"))
        label_files = set(f.stem for f in (yolo_dir / "labels").glob("*.txt"))

        unmatched_images = image_files - label_files
        unmatched_labels = label_files - image_files

        for unmatched in unmatched_images:
            print(f"Moving unmatched image: {unmatched}.jpg")
            if (yolo_dir / "images" / (unmatched + ".jpg")).exists():
                shutil.move(str(yolo_dir / "images" / (unmatched + ".jpg")), str(mismatch_dir))

        for unmatched in unmatched_labels:
            print(f"Moving unmatched label: {unmatched}.txt")
            if (yolo_dir / "labels" / (unmatched + ".txt")).exists():
                shutil.move(str(yolo_dir / "labels" / (unmatched + ".txt")), str(mismatch_dir))

        print(f"Moved unmatched files to {mismatch_dir}")
    return Path(yolo_dir)

def process_multiple_datasets(dataset_dir: list[str], destination_path: Path) -> Path:
    """
    Processes multiple dataset directories and consolidates them into one YOLO dataset for segmentation.

    :param dataset_paths: List of dataset directories containing images and labels.
    :param destination_path: Destination path where the consolidated YOLO dataset will be stored.
    :return: Path to the consolidated YOLO dataset.
    """
    destination_path= Path(destination_path)
    destination_path.mkdir(parents=True, exist_ok=True)
    results = []
    dataset_paths = [str(subdir) for subdir in dataset_dir.iterdir() if subdir.is_dir()]
    for ds in dataset_paths:
        sub_dest = destination_path / f"{Path(ds).stem}_yolo"
        sub_dest.mkdir(parents=True, exist_ok=True)
        print(f"Processing dataset: {ds}")
        # Process each dataset into its own subfolder (e.g. destination/A1_yolo, destination/A2_yolo, etc.)
        ds_yolo = prepare_dataset(ds, str(sub_dest))
        results.append(ds_yolo)
    print(f"Processed {len(results)} datasets. Results stored in individual subfolders.")

def remove_YOLO_folder(destination_path: Path) -> None:
    for subfolder in destination_path.glob("*/*"):
        if subfolder.is_dir():
            print(f"Processing folder: {subfolder}")
            for item in list(subfolder.iterdir()):
                destination_paths = subfolder.parent / item.name
                shutil.move(str(item), str(destination_paths))
            print(f"Removing empty directory: {subfolder}")
            subfolder.rmdir()

def delete_yaml(destination_path: Path) -> None:
    destination_path = Path(destination_path)
    for yaml_file in destination_path.rglob("*.yaml"):
        try:
            if yaml_file.is_file():
                print(f"Found YAML file: {yaml_file}")
                yaml_file.unlink()
        except Exception as e:
            print(f"Failed to process {yaml_file}: {e}")



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepare dataset for YOLO training.")
    parser.add_argument("mode", type=str, help="Choose single or multiple folder mode", default="single")
    parser.add_argument("dataset_path", type=str, help="Path to the dataset.")
    parser.add_argument("destination_path", type=str, help="Destination path for YOLODataset.")
    args = parser.parse_args()

    config_folder_path = os.path.join(os.path.dirname(__file__),'../../config')

    if args.mode.lower() == "multiple":
        dataset_dir = Path(args.dataset_path)
        print(f"Processing multiple dataset folders: {dataset_dir}")
        
        #Convert multiple folders into yolo format
        consolidated_path = process_multiple_datasets(dataset_dir, args.destination_path)

        #Reorganize the folder, bcs the structured obtained is:
        #folder_yolo/YOLO_Dataset-seg/images and labels
        #This move everything to yolo_folder
        remove_YOLO_folder(Path(args.destination_path))
        #Deletes all the yaml generated by the labelme2yolo script
        delete_yaml(Path(args.destination_path))
        
    elif args.mode.lower() == "single":
        yolo_dir = prepare_dataset(args.dataset_path, args.destination_path)
        class_source_yaml = yolo_dir / "dataset.yaml"
        #This is assuming that the folder project structure has been touched
        create_config_file(class_source_yaml, config_folder_path, "default_config", dataset_name="default")