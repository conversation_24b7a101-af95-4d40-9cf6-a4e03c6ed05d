"""
Script name: Generate Predictions
File name: generate_prediction.py
Author: <PERSON>

This script evaluates a trained YOLO model's predictions and identifies potentially mislabeled data
using the cleanlab library. It processes validation data fold by fold, generates quality scores for labels,
and provides tools for manual review of suspicious cases.
"""
import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__),'../../'))
os.environ["QT_QPA_PLATFORM"] = "offscreen"
import matplotlib
matplotlib.use("Agg")

from pathlib import Path

from helper.yaml_file_manager import read_yaml_config
from helper.file_management import * 
from src.classes.cleanlab_seg_cls import CleanLab_SEG 
from concurrent.futures import ThreadPoolExecutor, as_completed

def initialize_cleanlab_data(
    dataset_path: Path,
    output_directory: Path,
    class_names: dict[str, str],
    class_array
) -> None:
    folders = []
    class_array_collection = []

    def process_fold(data_folder: Path):
        logs = [f"\n=== Processing: {data_folder} ==="]
        print(output_directory)
        fold_cleaner = CleanLab_SEG(data_folder, dataset_path, output_directory, class_names, class_array)
        fold_cleaner.init_yolo_model(output_directory)
        logs.append("YOLO model initialized")
        fold_cleaner.init_cleanlab_os_data(batch_size=2)
        logs.append("Cleanlab data initialized")
        result = fold_cleaner
        fold_cleaner.generate_scores_histogram(desired_bins_display=20, threshold=1)
        logs.append("Graph generated")
        #fold_cleaner.restore_default_values()
        logs.append("Restored default values")
        return data_folder, logs, result

    results = []
    with ThreadPoolExecutor(max_workers=1) as executor:
        futures = [executor.submit(process_fold, data_folder.name) for data_folder in dataset_path.iterdir()]
        
        #futures = [executor.submit(process_fold, tn) for tn in ["train_fold_1", "train_fold_4"]]
        for future in as_completed(futures):
            try:
                results.append(future.result())
            except Exception as e:
                print(f"Error processing a fold: {e}")

    # Print logs in order (sorted by folder name)
    for data_folder, logs, init_class in sorted(results, key=lambda x: x[0]):
        for log in logs:
            print(log)
        class_array_collection.append(init_class)

if __name__ == '__main__':

    #Extract the working config on the config mananager
    config_manager_path = os.path.join(os.path.dirname(__file__), "../../config/config_manager.yaml") 
    current_active_config = read_yaml_config(config_manager_path)
    #Extract config specified in the config manager
    config_path = os.path.join(os.path.dirname(__file__), f"../../config/{current_active_config['active_config']}")
    config = read_yaml_config(config_path)

    #Are arguments needed are loaded
    dataset_path = Path(config['paths']['package_path'])
    model_path = Path(config['paths']['models_path'])
    output_directory = Path(config['paths']['results_dir'])
    data_dir = Path(config['paths']['data_dir'])
    class_names = config["class_names"]
    class_array = config.get('class_name_array', [])
    
    #Data in initialized
    initialize_cleanlab_data(dataset_path, output_directory, class_names, class_array)