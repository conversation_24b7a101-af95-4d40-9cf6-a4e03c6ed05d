"""
Script name: Training on k-folds
File name: training_on_folds.py
Author: <PERSON>ga
Date: 2025-02-13

Purpose:
    This script trains a YOLO model on multiple k-fold partitions created using `prepare_k_folds.py`.
    The training process is parallelized over multiple GPUs, ensuring efficient use of computational resources.
    Each fold is trained separately, and results are saved to the specified results directory.
"""
import os
import sys
import json
import subprocess
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from helper.yaml_file_manager import read_yaml_config

def training_on_k_folds(
    dataset_path: Path,
    model_path: Path,
    results_dir: Path,
    config: dict = {}
) -> None:
    """
    Trains a YOLO model on each k-fold partition, using multiple GPUs in parallel.

    :param dataset_path: Path to the dataset containing k-fold subdirectories.
    :param model_path: Path to the pretrained YOLO model (.pt file).
    :param results_dir: Directory where training results will be stored.
    :param config: Dictionary containing additional YOLO training parameters.
    
    :raises FileNotFoundError: If the dataset directory does not exist.
    :raises ValueError: If no fold directories are found in the dataset path.
    """
    fold_folders = [f for f in dataset_path.iterdir() if f.is_dir() and f.name.startswith("fold_")]
    #In case of unexpected interruption proceed as follows:
    #1. Comment line 39
    #2. Uncomment lien 45
    #3. Add the missing folders to the arrray
    #4. Rerun the script
    #fold_folders = ["fold_1", "fold_2", "fold_3", ...] 
    
    n_gpus = 2
    current_process = {gpu: None for gpu in range(n_gpus)}

    for i, fold_folder in enumerate(fold_folders):
        yaml_file = os.path.join(dataset_path, fold_folder.name, f"{fold_folder.name}.yaml")
        device_id = i % n_gpus

        # Wait for the GPU to be free if there's an ongoing process.
        if current_process[device_id] is not None:
            current_process[device_id].wait()

        cmd = [
            "python", "./utils/train_script.py",
            "--model_path", str(model_path),
            "--results_dir", str(results_dir),
            "--fold_folder", fold_folder.name,
            "--yaml_file", yaml_file,
            "--device", str(device_id),
            "--config", json.dumps(config)
        ]

        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = str(device_id)

        print(f"Starting training for {fold_folder.name} on GPU {device_id}")
        process = subprocess.Popen(cmd, env=env)
        current_process[device_id] = process

    # Wait for both trainings to finish.
    for process in current_process.values():
        if process is not None:
            process.wait()
        
if __name__ == '__main__':

    config_manager_path = os.path.join(os.path.dirname(__file__), "../../config/config_manager.yaml")
    current_active_config = read_yaml_config(config_manager_path)
    config_path = os.path.join(os.path.dirname(__file__), f"../../config/{current_active_config['active_config']}")
    config = read_yaml_config(config_path)
    
    dataset_path = Path(config['paths']['folds_path'])
    model_path = Path(config['paths']['models_path'])
    results_dir = Path(config['paths']['results_dir'])
    model_config = config['model_config']
    training_on_k_folds(dataset_path, model_path, results_dir, model_config)



