"""
Script name: Preparing k-folds
File name: prepare_k_folds.py
Author: <PERSON>
Date: 2025-02-13
Purpose: 
    This script creates k-fold cross-validation partitions for a YOLO dataset. It ensures that images and 
    labels are correctly paired, splits them into k folds, and creates symbolic links to optimize storage 
    efficiency. The script reads configuration settings from a YAML file and updates the dataset paths accordingly.
"""
import os
import sys
import random
import argparse
from pathlib import Path
import tqdm

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from sklearn.model_selection import KFold
from helper.yaml_file_manager import file_creation, read_yaml_config, add_path_to_yaml
def create_k_fold_splits(dataset_path: str, output_dir: str, k: int) -> None:
    """
    Splits the dataset into k-folds for cross-validation using symbolic links to save storage.

    The dataset should have the following structure:
    dataset/
    ├── images/
    │   ├── image1.jpg
    │   ├── image2.jpg
    │   ├── ...
    │
    └── labels/
        ├── label1.txt
        ├── label2.txt
        ├── ...

    Labels should be in YOLO format.

    :param dataset_path: Path to the dataset containing 'images' and 'labels' folders.
    :param output_dir: Path where the k-fold partitions will be created.
    :param k: Number of folds for cross-validation.
    
    :raises FileNotFoundError: If the image or label directory does not exist.
    :raises FileExistsError: If the output directory already exists.
    :raises ValueError: If there is a mismatch between images and labels.
    """
    # Ensure directories exist

    image_dir = Path(dataset_path) / 'images'
    label_dir = Path(dataset_path) / 'labels'
    output_dir = Path(output_dir).resolve()  # Get absolute path

    if not image_dir.exists() or not label_dir.exists():
        raise FileNotFoundError("Image or label directory does not exist.")

    # Verify output directory doesn't already exist, then create it
    if output_dir.exists():
        raise FileExistsError(f"Output directory {output_dir} already exists.")
    output_dir.mkdir(parents=True)

    # Collect all images and labels into two different sorted arrays
    image_files = sorted([f for f in image_dir.glob("*.jpg")])  # Assumes images have .png extension
    label_files = sorted([f for f in label_dir.glob("*.txt")])  # Assumes labels have .txt extension

    # Ensure that each image has a corresponding label
    image_names = {f.stem for f in image_files}
    label_names = {f.stem for f in label_files}
    if image_names != label_names:
        missing_images = label_names - image_names
        missing_labels = image_names - label_names
        raise ValueError(
            f"Mismatch between images and labels:\nMissing images: {missing_images}\nMissing labels: {missing_labels}")

    # Pair image and label paths for shuffling and splitting
    data_pairs = list(zip(image_files, label_files))
    random.shuffle(data_pairs)

    # Initialize KFold splitting
    kf = KFold(n_splits=k, shuffle=True, random_state=42)

    # Create K splits
    for fold_idx, (train_indices, val_indices) in enumerate(kf.split(data_pairs), start=1):
        print(f"Processing fold {fold_idx}/{k}...")
        # Create directories for each fold
        fold_dir = output_dir / f"fold_{fold_idx}"
        fold_dir_train_images = fold_dir / "train" / "images"
        fold_dir_train_labels = fold_dir / "train" / "labels"
        fold_dir_val_images = fold_dir / "val" / "images"
        fold_dir_val_labels = fold_dir / "val" / "labels"

        for dir_path in [fold_dir_train_images, fold_dir_train_labels, fold_dir_val_images, fold_dir_val_labels]:
            dir_path.mkdir(parents=True)

        # Create symbolic links for training and validation sets using absolute paths
        # Add progress bar for each fold
        with tqdm.tqdm(total=len(data_pairs), desc=f"Creating links for fold {fold_idx}") as pbar:
            for idx, (img_path, lbl_path) in enumerate(data_pairs):
                if idx in train_indices:
                    subset_dir_images, subset_dir_labels = fold_dir_train_images, fold_dir_train_labels
                else:
                    subset_dir_images, subset_dir_labels = fold_dir_val_images, fold_dir_val_labels

                os.symlink(img_path.resolve(), subset_dir_images / img_path.name)  # Absolute path to the image
                os.symlink(lbl_path.resolve(), subset_dir_labels / lbl_path.name)  # Absolute path to the label
                pbar.update(1)

    print(f"K-Fold dataset with {k} splits created in '{output_dir}'")


if __name__ == "__main__":
    config_manager_path = os.path.join(os.path.dirname(__file__), "../../config/config_manager.yaml")
    current_active_config = read_yaml_config(config_manager_path)
    config_path = os.path.join(os.path.dirname(__file__), f"../../config/{current_active_config['active_config']}")
    config = read_yaml_config(config_path)
    #Path to static/datasets
    data_dir = Path(config['paths']['data_dir'])    
    #Initialization of class array in config
    class_array = config.get('class_names', [])
    parser = argparse.ArgumentParser(description="Create K-fold cross-validation dataset with symbolic links.")
    parser.add_argument("--n_folds", type=int, help="Number of folds.", required=True)
    parser.add_argument("--yolo_dset_path", type=str, help="Path to the YOLO dataset.", required=True)
    args = parser.parse_args()

    # Define paths
    folds_base_name = f"{args.n_folds}_fold_partition"
    folds_path = data_dir / folds_base_name
    counter = 2
    while folds_path.exists():
        folds_path = data_dir / f"{folds_base_name}_{counter}"
        counter += 1

    # Call functions with corrected arguments
    add_path_to_yaml(config_path, "folds_path", str(folds_path))
    add_path_to_yaml(config_path, "models_path", str(data_dir / "models" / "yolo11n-seg.pt"))
    create_k_fold_splits(args.yolo_dset_path, folds_path,  args.n_folds)
    
    file_creation(folds_path, class_array)
