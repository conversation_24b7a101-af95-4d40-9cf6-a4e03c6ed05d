"""
Script name: Prepare Dataset for YOLO Detection
File name: prepare_dataset.py
Author: <PERSON>
Date: 2025-02-13

Purpose:
    This script processes a dataset to prepare it for YOLO detection training. It:
    - Organizes associated and unassociated image-label pairs.
    - Moves unassociated files to a separate folder.
    - Checks for an existing YOLO-formatted dataset or generates one using `labelme2yolo.py` with detection support..
    - Consolidates training and validation sets into a single YOLO dataset.
    - Identifies and resolves mismatches between images and labels.
    - Generates a YAML configuration file for training.

Usage:
    python prepare_dataset.py <dataset_path> <destination_path>
"""

import os
import shutil
import subprocess
import argparse
import sys
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from helper.yaml_file_manager import create_config_file


def prepare_dataset(dataset_path: str, destination_path: str) -> Path:
    """
    Prepares a dataset for YOLO training by verifying, organizing, and converting files.

    :param dataset_path: Path to the dataset directory containing images and labels.
    :param destination_path: Path to store the YOLO-compatible dataset.
    
    :return: Path to the YOLO dataset directory.
    
    :raises FileNotFoundError: If the dataset directory does not exist.
    :raises Exception: For any unexpected errors in file processing.
    """
    dataset_path = Path(dataset_path)
    destination_path = Path(destination_path)
    destination_path.mkdir(exist_ok=True)
    # Directories for organizing files
    unassociated_dir = dataset_path / "unassociated"
    yolo_dir = destination_path / "YOLODataset_odet"

    # Ensure directories exist
    unassociated_dir.mkdir(exist_ok=True)

    # Check for and organize associated and unassociated files
    unassociated_count = 0
    associated_count = 0

    for file in dataset_path.iterdir():
        try:
            if file.suffix == ".jpg":
                json_file = dataset_path / (file.stem + ".json")

                if not json_file.exists():
                    print(f"Unassociated file found: {file.name}")
                    if file.exists():
                        shutil.move(file, unassociated_dir / file.name)
                        unassociated_count += 1
                else:
                    print(f"Associated pair found: {file.name} and {json_file.name}")
                    associated_count += 1

            elif file.suffix == ".json":
                jpg_file = dataset_path / (file.stem + ".jpg")
                if not jpg_file.exists():
                    print(f"Unassociated file found: {file.name}")
                    if file.exists():
                        shutil.move(file, unassociated_dir / file.name)
                        unassociated_count += 1
        except Exception as e:
            print(f"Error processing file {file.name}: {e}")

    print(f"Total unassociated files: {unassociated_count}")
    print(f"Total associated files: {associated_count}")

    # Check for YOLODataset in destination or associated folder
    potential_yolo_in_destination = yolo_dir
    potential_yolo_in_associated = dataset_path / "YOLODataset"

    if potential_yolo_in_destination.exists():
        print(f"YOLODataset already exists in destination: {yolo_dir}. Skipping creation.")
    elif potential_yolo_in_associated.exists():
        print(f"Found YOLODataset in associated. Moving it to {yolo_dir}.")
        shutil.move(str(potential_yolo_in_associated), str(yolo_dir))
    else:
        print("YOLODataset does not exist. Executing labelme2yolo...")
        labelme2yolo_script = "/mnt/data/projects/K-fold-data_prep/utils/labelme2yolo.py"
        if Path(labelme2yolo_script).exists():
            subprocess.run([
                "python",
                str(labelme2yolo_script),
                "--json_dir", str(dataset_path),
                "--val_size", "0.15"

            ])
            shutil.move(str(potential_yolo_in_associated), str(yolo_dir))
        else:
            print(f"Error: labelme2yolo.py not found at {labelme2yolo_script}")
            return


    # Consolidate YOLODataset
    for subdir in ["images/train", "images/val"]:
        train_val_dir = yolo_dir / subdir
        if train_val_dir.exists():
            print(f"Consolidating images from {train_val_dir}")
            for file in train_val_dir.iterdir():
                shutil.move(str(file), str(yolo_dir / "images" / file.name))
            shutil.rmtree(train_val_dir)

    for subdir in ["labels/train", "labels/val"]:
        train_val_dir = yolo_dir / subdir
        if train_val_dir.exists():
            print(f"Consolidating labels from {train_val_dir}")
            for file in train_val_dir.iterdir():
                shutil.move(str(file), str(yolo_dir / "labels" / file.name))
            shutil.rmtree(train_val_dir)

    # Verify counts and handle mismatches
    images_count = len(list((yolo_dir / "images").glob("*.jpg")))
    labels_count = len(list((yolo_dir / "labels").glob("*.txt")))

    print(f"Total images: {images_count}, Total labels: {labels_count}")

    if images_count != labels_count:
        print("Mismatch detected between images and labels. Resolving...")
        mismatch_dir = yolo_dir / "mismatched"
        mismatch_dir.mkdir(exist_ok=True)

        image_files = set(f.stem for f in (yolo_dir / "images").glob("*.jpg"))
        label_files = set(f.stem for f in (yolo_dir / "labels").glob("*.txt"))

        unmatched_images = image_files - label_files
        unmatched_labels = label_files - image_files

        for unmatched in unmatched_images:
            print(f"Moving unmatched image: {unmatched}.jpg")
            if (yolo_dir / "images" / (unmatched + ".jpg")).exists():
                shutil.move(str(yolo_dir / "images" / (unmatched + ".jpg")), str(mismatch_dir))

        for unmatched in unmatched_labels:
            print(f"Moving unmatched label: {unmatched}.txt")
            if (yolo_dir / "labels" / (unmatched + ".txt")).exists():
                shutil.move(str(yolo_dir / "labels" / (unmatched + ".txt")), str(mismatch_dir))

        print(f"Moved unmatched files to {mismatch_dir}")
    return Path(yolo_dir)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepare dataset for YOLO training.")
    parser.add_argument("dataset_path", type=str, help="Path to the dataset.")
    parser.add_argument("destination_path", type=str, help="Destination path for YOLODataset.")
    args = parser.parse_args()

    config_folder_path = os.path.join(os.path.dirname(__file__),'../../config')
    yolo_dir = prepare_dataset(args.dataset_path, args.destination_path)
    class_source_yaml = yolo_dir / "dataset.yaml"
    #This is assuming that the folder project structure has been touched
    create_config_file(class_source_yaml, config_folder_path, "default_config", dataset_name="default")