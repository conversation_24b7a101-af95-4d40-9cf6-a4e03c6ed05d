"""
Class name: CleanLab Object Segmentation
File name: cleanlab_seg_cls.py
Author: <PERSON>
Date: 2023-02-13
Purpose: 
    This script defines the CleanLab_SEG class, which facilitates evaluating and analyzing
    segmentation model predictions using CleanLab. It processes model outputs, identifies 
    low-confidence predictions, generates label quality histograms, and provides tools for
    visualizing problematic labels, creating bin studies in Excel, and calculating precision 
    metrics with confidence intervals for dataset refinement and analysis.
"""
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Tuple

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

import numpy as np
from ultralytics import YOLO

from src.cleanlab_lib_segmentation import *
from src.cleanlab_lib_common import *
from utils.general_statistics import *
from helper.menu_display import menu_number_defect_bins


"""
OBJECT SEGMENTATION CLASS
"""
class CleanLab_SEG:
    bins_set: Optional[List[Tuple[float, float]]] = None
    extracted_bins_idx: Optional[List[int]] = None
    splited_dataset_created: Any = None
    def __init__(
        self,
        folder_name: Path,
        dataset_path: Path,
        output_directory: Path,
        class_names: Dict[str, str],
        class_array: List[Any]
    ) -> None:
        """
        Initializes the CleanLab_SEG class with paths and class mappings.

        :param training_path: Path to the directory containing training artifacts.
        :param train_name: Path to the specific training folder.
        :param dataset_path: Path to the dataset used for training and evaluation.
        :param output_directory: Path where output files and results will be saved.
        :param class_names: Dictionary mapping class IDs to human-readable names.
        :param class_array: List or array of class identifiers.
        """
        self.dataset_path: Path = dataset_path
        self.output_directory: Path = output_directory
        self.class_names: Dict[str, str] = class_names
        self.class_array: List[Any] = class_array
        self.folder_name: Path = folder_name

        self.model: Optional[Any] = None
        self.scores: Optional[List[float]] = None
        self.images_paths: Optional[List[Any]] = None
        self.low_scores: Optional[List[List[int]]] = None
        self.bins: Optional[np.ndarray] = None
        self.bins_folders: Optional[Path] = None
        self.bins_frequencies: List[Any] = []
        
    def init_yolo_model(self, output_path: Path) -> Any:
        """
        Loads the trained YOLO model for a specific training fold.

        :param train_name: Path to the training folder containing the model weights.
        :return: Loaded YOLO model.
        :raises FileNotFoundError: If the specified model file does not exist.
        """
        # Search for best.pt recursively in the results_path directory
        training_directory = output_path / 'training_results'
        model_candidates = list(training_directory.rglob("best.pt"))
        if model_candidates:
            model_path = model_candidates[0]
        else:
            model_path = training_directory / 'weights' / 'best.pt'
        if not model_path.exists():
            print(f"Error: The model file at '{model_path}' does not exist!")
            raise FileNotFoundError(f"Model file at {model_path} not found.")
        
        self.model = YOLO(model_path)
        print(f"Successfully loaded YOLO model from {model_path}.")
        return self.model

    def init_cleanlab_os_data(self, batch_size) -> Tuple[List[float], List[Any]]:
        """
        Initializes predictions and labels using CleanLab's utility functions.

        :return: Tuple containing processed scores and image paths.
        :raises AttributeError: If the YOLO model is not initialized.
        :raises ValueError: If class names are missing.
        """
        if not hasattr(self, 'model'):
            print("Error: YOLO model is not initialized. Please call 'init_yolo_model' first.")
            raise AttributeError("YOLO model has not been initialized.")
        
        if not self.class_names:
            print("Error: 'class_names' is missing! Please provide class names before processing data.")
            raise ValueError("'class_names' is required for processing labels and predictions.")
    
        scores, images_paths = process_segmentation_scores(self.folder_name, self.dataset_path, self.output_directory, self.class_names, self.model, batch_size=batch_size)
        self.scores = scores
        self.images_paths = images_paths
        print(f"Predictions and labels initialized for fold {self.folder_name}.")
        return scores
    
    def generate_scores_histogram(
        self,
        desired_bins_display: Union[int, List[float]],
        threshold: float
    ) -> None:
        """
        Generates a histogram of label quality scores after eliminating outliers and normalizing scores.

        :param desired_bins_display: Number of bins or list of bin edges for histogram display.
        :param threshold: Maximum score threshold for filtering out outliers.
        """

        def elimininate_outliers_and_normalize(
            scores: List[float],
            images_paths: List[Any],
            threshold: float
        ) -> Tuple[List[Any], List[float]]:
            
            aux_scores: List[float] = []
            aux_images_paths: List[Any] = []
            
            for idx, score in enumerate(scores):
                if score <= threshold:
                    aux_scores.append(score)
                    aux_images_paths.append(images_paths[idx])
            
            arr = np.array(aux_scores)
            normalized_arr = arr / np.max(arr)
            
            #return aux_images_paths, list(normalized_arr)
            return aux_images_paths, aux_scores
        
        zoom_imgs_paths, zoom_score,  = elimininate_outliers_and_normalize(self.scores, self.images_paths, threshold)
        self.scores = zoom_score
        self.images_paths = zoom_imgs_paths

        bins = generate_cleanlab_histogram(self.output_directory, self.scores, self.folder_name, desired_bins_display)
        self.bins = bins

    def extract_defect_bins(self) -> List[Tuple[float, float]]:
        """
        Extracts bins of low-scoring predictions for defect analysis.

        :param n_bins: Number of bins to inspect.
        :return: The selected set of bins as a list of intervals.
        """
        # Collect indices of low-scoring predictions
        if CleanLab_SEG.bins_set == None:
            n_bins = menu_number_defect_bins(self.bins)
            CleanLab_SEG.bins_set = choose_inspected_bins(self.bins, n_bins)    
        low_scores = extract_desired_low_scores(CleanLab_SEG.bins_set, self.scores)
        self.low_scores = low_scores
        return CleanLab_SEG.bins_set

    def restore_default_values(self) -> None:
        """
        Restores all instance attributes to their default (unset) values.
        """
        self.images_paths = None
        self.bins = None
        self.model = None
        self.labels = None
        self.predictions = None
        self.low_scores = None
        self.bins_frequencies = []
        print("Attributes have been reset to default values.")

    def visualize_defect_bins(self) -> None:
        """
        Visualizes mislabeled data in defect bins.
        """
        yolo_visualize_misslabeled_data_SEG(self.images_paths, self.output_directory, self.folder_name, self.sampled_low_scores, self.bins_set, self.model, self.class_array)


    def create_bin_study_excels(self, sample_proportion: float) -> None:
        """
        Creates Excel files for studying bins based on a given sample proportion.

        :param sample_proportion: Proportion of samples to include in the bin study.
        """
        dataframes, choosen_lowscores = create_dataframe(self.low_scores, sample_proportion)
        self.sampled_low_scores = choosen_lowscores if choosen_lowscores is not None else self.low_scores
        bins_folders = create_study_excel(dataframes, self.output_directory, self.folder_name, CleanLab_SEG.bins_set)
        self.bins_folders = Path(bins_folders)
    
    def get_presicion_and_ci(self) -> None:
        excels_path = os.path.join(self.output_directory, 'statistics_folder', f'Excel Bin Study: fold_{self.folder_name}')
        excel_files = sorted([f for f in os.listdir(excels_path) if f.endswith(".xlsx")])
        stats = []  # List to hold tuples: (file, precision, margin, lower, upper)

        for file in excel_files:
            file_path = os.path.join(excels_path, file)
            # Validate GT column
            validate_gt_column(file_path=file_path)

            # Read the Excel file to get sample size
            df = pd.read_excel(file_path, sheet_name="Sheet1")
            col = df["GT: ¿Is the original image misslabeled?"]
            sample_size = col.size
            
            # Calculate precision and its confidence interval
            precision_val = calc_single_bin_precision(file_path)
            p, margin, lower, upper = ci_precision(precision_val, sample_size)
            stats.append((file, p, margin, lower, upper))

        # Print report header and explanation
        print(f"\nFOLD {self.folder_name}:")
        print("---POST ANALYSIS STATS ---")
        header = f"{'No.':<4} {'File Name':<30} {'Precision':<12} {'Confidence interval'}"
        print(header)
        
        # Print each file's stats
        for i, (file, p, margin, lower, upper) in enumerate(stats, start=1):
            ci_str = f"({lower:.3f}, {upper:.3f})"
            print(f"{i:<4} {file:<30} {p:.3f}      {ci_str}")




    def get_predictions_frequencies(self) -> List[Any]:
        """
        Calculates the frequencies of the predicted classes in the chosen bins.

        :return: List of frequency distributions for predicted classes in each bin.
        """

        bins_predictions = []
        bins_confusion_matrices = []
        for bin_low_scores in self.low_scores:
            for index in bin_low_scores:
               bins_predictions.append(self.predictions[index])
            bins_confusion_matrices.append(bins_predictions)
            bins_predictions = []

        labels_frequency = []
        image_freq = []
        bins_freq = []
        for bin in bins_confusion_matrices:
            for image in bin:
                for clas in image:
                    image_freq.append((len(clas)))
                labels_frequency.append(np.array(image_freq))
                image_freq = []            
            bins_freq.append(np.sum(labels_frequency, axis=0))
            labels_frequency=[]
        return bins_freq

        
    def get_labels_frequencies(self) -> None:
        """
        Calculates the frequency of labels (0-19) in the bins from `self.low_scores`
        using ground truth data from `self.labels` and stores the results in `self.bins_frequencies`.
        """
        bins_frequencies = []  # To store label frequency dictionaries for each bin

        for bin_low_scores in self.low_scores:

            for index in bin_low_scores:
                labels = self.labels[index]['labels']
                if isinstance(labels, np.ndarray):
                    labels = labels.tolist()
                bins_predictions.extend(labels)
            
            # Count occurrences of each label for the current bin
            label_counts = Counter(bins_predictions)
            bins_predictions = []
            # Fill missing numbers with frequency 0 and ensure range goes up to 19
            full_range = range(0, 20)  # Explicitly include 0 to 19
            sorted_label_counts = {num: label_counts.get(num, 0) for num in full_range}
            
            bins_frequencies.append(sorted_label_counts)

        # Save the frequencies for each bin as an attribute
        self.bins_frequencies.append(bins_frequencies)

    def select_bins_to_remove(self):
        if CleanLab_SEG.extracted_bins_idx == None:
            CleanLab_SEG.extracted_bins_idx = select_intervals(CleanLab_SEG.bins_set)
        return CleanLab_SEG.extracted_bins_idx


