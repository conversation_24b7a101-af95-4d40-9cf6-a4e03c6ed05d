"""
Class name: CleanLab_DET (Object Detection)
File name: cleanlab_det_cls.py
Date: 2023-06-15
Purpose: 
    This script defines the CleanLab_DET class, which facilitates evaluating and analyzing
    object detection model predictions using CleanLab. It loads YOLO models, processes predictions
    and ground truth labels, calculates quality scores, extracts low-scoring predictions into bins,
    generates visualizations, and provides tools for dataset analysis through Excel reports
    and precision metrics.
"""
import os
import sys
from pathlib import Path

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from typing import Any, Tuple, List, Dict

import numpy as np

from ultralytics import YOLO

from cleanlab.object_detection.rank import get_label_quality_scores

from utils.general_statistics import *
from src.cleanlab_lib_detection import *
from src.cleanlab_lib_common import *

class CleanLab_DET:
    bins_set = None
    extracted_bins_idx = None
    splited_dataset_created = None
    def __init__(self, training_path: Path, train_name: Path, dataset_path: Path, output_directory: Path, class_names: Dict[str, str]):
        """
        Initializes the CleanLab class with paths and class mappings.

        :param training_path: Path to the directory containing training artifacts.
        :param train_name: Path to the specific training folder.
        :param dataset_path: Path to the dataset used for training and evaluation.
        :param output_directory: Path where output files and results will be saved.
        :param class_names: Dictionary mapping class IDs to human-readable names.

        Attributes:
        - self.training_path: Stores the base path for training artifacts.
        - self.train_name: Stores the name of the training folder.
        - self.dataset_path: Stores the path to the dataset.
        - self.output_directory: Directory for storing results.
        - self.class_names: Mapping of class IDs to names.
        - self.idx: Associated fold index, determined from the training path and folder.
        - self.model: Placeholder for the YOLO model instance (initialized later).
        - self.labels: Placeholder for processed ground truth labels.
        - self.predictions: Placeholder for processed predictions.
        - self.scores: Placeholder for label quality scores.
        - self.low_scores: Placeholder for indices of low-scoring predictions.
        - self.bins_folders: Placeholder for the path to bin study folders.
        - self.bins_frequencies: List to store frequency distributions for all bins.
        """
        self.training_path = training_path
        self.dataset_path = dataset_path
        self.output_directory = output_directory
        self.class_names = class_names
        self.train_name = train_name
        self.idx =  get_fold_associated_with_model(self.training_path, self.train_name) 
        self.model = None
        self.labels = None
        self.predictions = None
        self.scores = None
        self.low_scores = None
        self.bins_folders = None
        self.bins_frequencies = []
        
    def init_yolo_model(self, train_name: Path) -> YOLO:
        """
        Loads the trained YOLO model for a specific training fold.

        :param train_name: Path to the training folder containing the model weights.
        :return: Loaded YOLO model.

        :param self.training_path: Base path where training folders are stored.
        :param self.model: Loaded YOLO model instance.
        :raises FileNotFoundError: If the specified model file does not exist.
        """
        model_path = self.training_path / train_name / 'weights' / 'best.pt'
        if not model_path.exists():
            print(f"Error: The model file at '{model_path}' does not exist!")
            raise FileNotFoundError(f"Model file at {model_path} not found.")
        
        self.model = YOLO(model_path)
        print(f"Successfully loaded YOLO model from {model_path}.")
        return self.model

    def init_cleanlab_od_data(self) -> Tuple[List, List]:
        """
        Initializes predictions and labels using CleanLab's utility functions.

        :return: Tuple containing processed predictions and labels.

        :param self.model: YOLO model used for generating predictions. Must be initialized before calling this function.
        :param self.class_names: List of class names required for processing labels and predictions.
        :param self.idx: Identifier for the current fold or analysis.
        :param self.dataset_path: Path to the dataset used for generating predictions.
        :param self.output_directory: Directory where results and intermediate files are stored.
        :param self.predictions: Processed predictions generated by CleanLab.
        :param self.labels: Processed ground truth labels generated by CleanLab.
        :raises AttributeError: If the YOLO model is not initialized.
        :raises ValueError: If class names are missing.
        """
        if not hasattr(self, 'model'):
            print("Error: YOLO model is not initialized. Please call 'init_yolo_model' first.")
            raise AttributeError("YOLO model has not been initialized.")
        
        if not self.class_names:
            print("Error: 'class_names' is missing! Please provide class names before processing data.")
            raise ValueError("'class_names' is required for processing labels and predictions.")

        predictions, labels = process_labels_and_predictions_odet(self.idx, self.dataset_path, self.output_directory, self.class_names, self.model)
        self.predictions = predictions
        self.labels = labels
        print(f"Predictions and labels initialized for fold {self.idx}.")
        return predictions, labels

    def generate_scores_histogram(self, desired_bins_display):
        bins = generate_cleanlab_histogram(self.output_directory, self.scores, self.idx, desired_bins_display)
        self.bins = bins


    def extract_defect_bins(self, n_bins, desired_bins_display):  
        """
        Extracts bins of low-scoring predictions for defect analysis.

        :param n_bins: Number of bins to inspect.
        :param self.labels: Ground truth labels for the dataset.
        :param self.predictions: Predicted labels for the dataset.
        :param self.output_directory: Directory to save histogram and related outputs.
        :param self.idx: Identifier for the current process.
        :param self.scores: Quality scores of predictions, computed using CleanLab.
        :param self.low_scores: Indices of low-scoring predictions for inspection.
        :param CleanLab.bins_set: Set of bins selected for inspection (initialized if None).
        :return: Tuple of low-scoring indices and the selected bins set.
        """
        scores = get_label_quality_scores(self.labels, self.predictions)
        self.scores = scores
        # Collect indices of low-scoring predictions
        print(f"The bins are: {self.bins}")
        CleanLab_DET.bins_set = choose_inspected_bins(self.bins, n_bins)    
        low_scores = extract_desired_low_scores(CleanLab_DET.bins_set, scores)
        self.low_scores = low_scores
        return low_scores, CleanLab_DET.bins_set

    def restore_default_values(self):
        self.labels = None
        self.bins = None
        self.model = None
        self.labels = None
        self.predictions = None
        self.idx = None
        self.low_scores = None
        self.bins_frequencies = []
        print("Attributes have been reset to default values.")

    def visualize_defect_bins(self):
        """
        Visualizes mislabeled data in defect bins.

        :param self.labels: Ground truth labels for the dataset.
        :param self.predictions: Predicted labels for the dataset.
        :param self.class_names: List of class names for visualization.
        :param self.output_directory: Directory where visualizations will be saved.
        :param self.idx: Identifier for the current visualization process.
        :param self.low_scores: List of bins containing low-confidence predictions.
        :param self.bins_set: Set of bins used for visualization.
        """
        yolo_visualize_misslabeled_data_DET(self.labels, self.predictions, self.class_names, self.output_directory, self.idx, self.low_scores, self.bins_set)

    def create_bin_study_excels(self, sample_proportion):
        """
        Creates Excel files for studying bins based on a given sample proportion.

        :param sample_proportion: Proportion of samples to include in the bin study.
        :param self.low_scores: List of bins with indices to be analyzed.
        :param self.output_directory: Directory where the Excel files will be created and saved.
        :param self.idx: Identifier for the current study or process.
        :param self.bins_folders: Path object to the directory containing the created Excel files.
        """
        dataframes = create_dataframe(self.low_scores, sample_proportion)
        bins_folders = create_study_excel(dataframes, self.output_directory, self.idx,)
        self.bins_folders = Path(bins_folders)

    def get_bins_precision_dataframe(self):
        """
        Computes precision for bins and generates a precision DataFrame.

        :param self.bins_folders: Directory containing bin files.
        :param self.idx: Identifier for the current process or analysis.
        :param self.output_directory: Directory to save the resulting DataFrame.
        :param self.low_scores: List of bins with indices used for additional precision calculations.
        """
        precision_array = []
        for bin_file_path in self.bins_folders.iterdir():
            presicion = calc_single_bin_precision(bin_file_path)  # Assuming this returns precision value
            precision_array.append(presicion)
        generate_precision_dataframe(self.idx, self.output_directory, self.bins_folders, precision_array)
        low_scores_lens = [len(i) for i in self.low_scores]
        generate_precision_dataframe(self.idx, self.output_directory, self.bins_folders, low_scores_lens)

    
    def get_predictions_statistics(self):
        """
        Calculates the frequencies of the predicted classes in the chosen bins.

        :param self.low_scores: List of bins with indices.
        :param self.predictions: List of predicted classes for each index.
        :return: List of frequency distributions for predicted classes in each bin.
        """

        bins_predictions = []
        bins_confusion_matrices = []
        for bin_low_scores in self.low_scores:
            for index in bin_low_scores:
               bins_predictions.append(self.predictions[index])
            bins_confusion_matrices.append(bins_predictions)
            bins_predictions = []

        labels_frequency = []
        image_freq = []
        bins_freq = []
        for bin in bins_confusion_matrices:
            for image in bin:
                for clas in image:
                    image_freq.append((len(clas)))
                labels_frequency.append(np.array(image_freq))
                image_freq = []            
            bins_freq.append(np.sum(labels_frequency, axis=0))
            labels_frequency=[]
        return bins_freq

        
    def get_labels_statistics(self):
        """
        Calculates the frequency of labels (0-19) in the bins from `self.low_scores` 
        using ground truth data from `self.labels` and stores the results in `self.bins_frequencies`.

        :param self.low_scores: List of bins with indices.
        :param self.labels: List of dictionaries containing ground truth labels.
        """
        bins_frequencies = []  # To store label frequency dictionaries for each bin

        for bin_low_scores in self.low_scores:

            for index in bin_low_scores:
                labels = self.labels[index]['labels']
                if isinstance(labels, np.ndarray):
                    labels = labels.tolist()
                bins_predictions.extend(labels)
            
            # Count occurrences of each label for the current bin
            label_counts = Counter(bins_predictions)
            bins_predictions = []
            # Fill missing numbers with frequency 0 and ensure range goes up to 19
            full_range = range(0, 20)  # Explicitly include 0 to 19
            sorted_label_counts = {num: label_counts.get(num, 0) for num in full_range}
            
            bins_frequencies.append(sorted_label_counts)

        # Save the frequencies for each bin as an attribute
        self.bins_frequencies.append(bins_frequencies)

    def select_bins_to_remove(self):
        self.extracted_bins_idx = select_intervals(CleanLab_DET.bins_set)
        return None
