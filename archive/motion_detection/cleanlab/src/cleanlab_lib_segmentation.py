"""
Script name: Segmentation Library
File Name: cleanlab_lib_segmentation.py
Author: <PERSON>
Date: 2023-02-13
Purpose: 
    This script provides functionality for semantic segmentation using YOLO models.
    It handles extraction of segmentation predictions and ground truth labels,
    quality score calculations, visualization of mislabeled data, and various
    utilities for analyzing and organizing segmentation results using cleanlab.
    Includes functions for histogram generation, bin selection, and sample creation
    for manual validation of potential labeling issues.
"""
import logging
import os
import pickle
import gc
import torch
from pathlib import Path
from typing import Any, Tuple, List, Dict, Optional, Union

import cv2
import yaml
import pandas as pd
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt
from skimage.draw import polygon

from cleanlab.object_detection.summary import visualize
from cleanlab.segmentation.summary import display_issues
from cleanlab.segmentation.rank import get_label_quality_scores as get_label_quality_scores_seg
from cleanlab.segmentation.rank import issues_from_scores
from src.cleanlab_lib_common import  *

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
"""
- - - SEGMENTATION - - - 

"""
def get_pred_probs_and_gt_label(
        label_path: str, 
        image_path: str, 
        model, 
        num_classes: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Computes pred_probs and gt_label for a given image and its label path.

    :param label_path: Path to the label file.
    :param image_path: Path to the image file.
    :param model: YOLO segmentation model instance used for predictions.
    :param num_classes: Total number of classes in the dataset.

    :return: A tuple containing pred_probs (predicted probabilities) and gt_label (ground truth labels).
    """
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"Could not read image: {image_path}")
    clear_memory()

    img_height, img_width, _ = img.shape
    background_class = num_classes
    # Initialize ground truth label mask
    gt_label = np.full((img_height, img_width), fill_value=background_class, dtype=np.int32)

    # Parse ground truth polygons from TXT file
    with open(label_path, 'r') as f:
        for line in f:
            parts = line.strip().split()
            cls_id = int(parts[0])
            coords = np.array(parts[1:], dtype=float).reshape(-1, 2)

            # Denormalize coordinates to pixel values
            coords[:, 0] *= img_width
            coords[:, 1] *= img_height

            # Validate and create a mask for the polygon
            try:
                rr, cc = polygon(coords[:, 1], coords[:, 0], gt_label.shape)
                gt_label[rr, cc] = cls_id
            except ValueError as ve:
                logging.warning(f"Polygon creation failed for {label_path}: {ve}")
                continue
    # Run model inference to get predicted probabilities
    clear_memory()
    results = model(img)[0]  # Using Ultralytics YOLOv8's __call__ method

    # Check if results.masks is None
    if results.masks is None:
        print("RESULT MASK NONE, RESULTS MASK NONE")
        pred_probs = np.full((num_classes + 1, img_height, img_width), fill_value=background_class, dtype=np.float32)
        pred_probs[background_class] = 1.0  # Everything is background
        return pred_probs, gt_label
    #    raise ValueError(f"No masks found for image: {image_path}")

    pred_masks = results.masks.data.cpu().numpy()  # Move masks to CPU
    pred_classes = results.boxes.cls.cpu().numpy().astype(int)  # Move class indices to CPU

    # Free CUDA memory explicitly after moving data to CPU
    clear_memory()

    # Resize predicted masks to match the image dimensions on CPU
    pred_probs = np.zeros((num_classes + 1, img_height, img_width), dtype=np.float32)

    for mask, cls in zip(pred_masks, pred_classes):
        resized_mask = cv2.resize(mask, (img_width, img_height), interpolation=cv2.INTER_LINEAR)
        pred_probs[cls] = np.maximum(pred_probs[cls], resized_mask)

    pred_probs[background_class] = 1 - np.sum(pred_probs[:num_classes], axis=0)
    pred_probs[background_class] = np.clip(pred_probs[background_class], 0, 1)

    total = np.sum(pred_probs, axis=0, keepdims=True)
    total[total == 0] = 1  # Avoid division by zero
    pred_probs = pred_probs / total

    del results, pred_masks, pred_classes, total
    clear_memory()

    return pred_probs, gt_label


def yolo_segmentation_extract_scores(
    model, 
    fold_name: int, 
    folders_path: str, 
    num_classes: int,
    batch_size: int = 2,
    max_images: int = None
) -> Tuple[List[Dict[str, str]], List[Any]]:
    """
    Extract segmentation scores and corresponding image names using a YOLO segmentation model.

    :param model: YOLO segmentation model.
    :param fold_index: Index of the fold to process (used for cross-validation).
    :param folders_path: Base directory containing fold directories.
    :param num_classes: Number of segmentation classes.
    :param batch_size: Number of images to process per batch (default is 16).
    :param max_images: Maximum number of images to process; if None, all images are processed.
    :return: A tuple containing:
        - A list of segmentation scores.
        - A list of dictionaries with image file names.
    :raises Exception: If an error occurs during extraction.
    """
    try:
        base_dir = Path(folders_path) 
        data_package_dir = base_dir / f'{fold_name}'
        image_dir = data_package_dir / 'images'
        label_dir = data_package_dir / 'labels'
        images_paths = sorted(image_dir.glob('*.jpg'))
        labels_paths = sorted(label_dir.glob('*.txt'))
        clear_memory()
        if max_images is not None:
            images_paths = images_paths[:max_images]
            labels_paths = labels_paths[:max_images]

        if len(images_paths) != len(labels_paths):
            logging.error(f"Number of images ({len(images_paths)}) and labels ({len(labels_paths)}) do not match.")
            return [], []

        image_paths_list = []
        scores = []

        for batch_start in range(0, len(images_paths), batch_size):
            batch_images_paths = images_paths[batch_start:batch_start + batch_size]
            batch_labels_paths = labels_paths[batch_start:batch_start + batch_size]

            batch_labels = []
            batch_probs = []

            for img_path, label_path in zip(batch_images_paths, batch_labels_paths):
                pred_probs, gt_label = get_pred_probs_and_gt_label(str(label_path), str(img_path), model, num_classes)
                batch_labels.append(gt_label)
                batch_probs.append(pred_probs)

            if batch_labels and batch_probs:
                # Resize all label masks to a common shape using the first label's shape as target.
                target_shape = batch_labels[0].shape
                resized_batch_labels = []
                for lbl in batch_labels:
                    if lbl.shape != target_shape:
                        resized_lbl = cv2.resize(lbl, (target_shape[1], target_shape[0]), interpolation=cv2.INTER_NEAREST)
                    else:
                        resized_lbl = lbl
                    resized_batch_labels.append(resized_lbl)
                batch_labels = np.stack(resized_batch_labels, axis=0)

                # Ensure predicted probability arrays have consistent spatial dimensions.
                target_shape_prob = batch_probs[0].shape[1:]  # (height, width)
                resized_batch_probs = []
                for prob in batch_probs:
                    if prob.shape[1:] != target_shape_prob:
                        num_channels = prob.shape[0]
                        resized_prob = np.zeros((num_channels, target_shape_prob[0], target_shape_prob[1]), dtype=prob.dtype)
                        for ch in range(num_channels):
                            resized_prob[ch] = cv2.resize(prob[ch], (target_shape_prob[1], target_shape_prob[0]), interpolation=cv2.INTER_LINEAR)
                        resized_batch_probs.append(resized_prob)
                    else:
                        resized_batch_probs.append(prob)
                batch_probs = np.stack(resized_batch_probs, axis=0)
                    
                batch_scores, batch_pixel_scores = get_label_quality_scores_seg(batch_labels, batch_probs)
                for img_path in batch_images_paths:
                    image_paths_list.append({"image_name": str(img_path)})
                scores.extend(batch_scores)

                del batch_labels, batch_probs, batch_pixel_scores, batch_scores
                clear_memory()
        clear_memory()
        return scores, image_paths_list

    except Exception as e:
        logging.error(f"Error extracting labels and probabilities: {str(e)}")
        raise


def yolo_visualize_misslabeled_data_SEG(
    image_paths: List[Dict[str, str]],
    output_directory: str,
    folder_name: int,
    issue_labels: List[List[int]],
    bins: List[Tuple[float, float]],
    model: Any,
    classes: List[str]
) -> None:
    """
    Visualize and save misslabeled segmentation data using YOLO.

    :param image_paths: List[Dict[str, str]] - List of dictionaries containing image paths under the key "image_name".
    :param output_directory: str - Base directory to save visualization outputs.
    :param fold_idx: int - Index of the current fold for organizing output folders.
    :param issue_labels: List[List[int]] - Lists of image indexes with labeling issues for each bin.
    :param bins: List[Tuple[float, float]] - List of bins defined by (low, high) score ranges.
    :param model: Model instance - YOLO segmentation model for predictions.
    :param classes: List[str] - Class names for segmentation tasks.
    """

    for n, bin in enumerate(bins):    
        for i in issue_labels[n]:
            if i < 0 or i >= len(image_paths):
                logging.warning(f"Index {i} is out of bounds for image paths array.")
                continue
            cleanlab_path = os.path.join(output_directory,'cleanlabs',f'Bins: {folder_name}' , f'Bin: ({round(bin[0],4)}, {round(bin[1],4)})')        
            if not os.path.exists(cleanlab_path):
                os.makedirs(cleanlab_path, exist_ok=False)
                print(f'cleanlab folder created in {output_directory}')
            else:
                print(f'folder "cleanlabs" already exists in {output_directory}')

            image_path = image_paths[i]["image_name"]
            image_name = os.path.basename(image_path) 
            
            save_path = os.path.join(cleanlab_path, f'{i}_{image_name}')  



            # Construct the label path by replacing 'images' with 'labels' and '.jpg' with '.txt'
            label_path = image_path.replace("/images/", "/labels/").replace(".jpg", ".txt")

            pred_prob, gt_label = get_pred_probs_and_gt_label(label_path, image_path,  model, len(classes)-1)
            #Adding one dimension so it fits the requirements, dp1 (dimension plus 1)
            pred_prob_dp1 = np.expand_dims(pred_prob, axis=0)
            gt_label_dp1 = np.expand_dims(gt_label, axis=0)

            image_score, image_pixel_score = get_label_quality_scores_seg(gt_label_dp1, pred_prob_dp1)
            #issues_from_score = find_label_issues(labels=gt_label_dp1, pred_probs= pred_prob_dp1, downsample = 16, n_jobs=None, batch_size=100000)
            issues_from_score = issues_from_scores(image_score, image_pixel_score, threshold=0.1)
            display_issues(issues_from_score, labels=gt_label_dp1, pred_probs=pred_prob_dp1, class_names=classes ,save_path= save_path, custom_image_path=image_path)
            print(f'SAVE PATH:',save_path)
            plt.close('all')

def process_segmentation_scores(
    folder_name: int,
    dataset_path: Path,  # Root path to dataset containing image folds
    output_directory: Path,  # Where to save analysis results
    class_names: dict[str, str],  # Maps class IDs to human-readable names
    model: YOLO,
    batch_size: int
) -> None:
    """
    Process predictions and labels for a specific fold index.
    If a corresponding .pkl file exists, load it; otherwise, generate predictions,
    save them to a .pkl file, and return the results.

    :param idx: Fold index to process.
    :param dataset_path: Path to the folder where all k-fold splits are stored.
    :param output_directory: Directory path where results will be saved.
    :param class_names: Mapping of class IDs to human-readable names.
    :param model: YOLO model used for feature extraction and predictions.
    :return: A tuple containing the images scores and their corresponding image paths.
    """
    folders_path = output_directory / 'labels_and_predictions'
    fold_file_path = folders_path / f'{folder_name}_lnp.pkl'
    """
    This function is created because some scores exceed 1 idk why, so in order to just keep the values between 0 and 1 we just remove them from the scores
    We also adjust image_paths
    """
    """    
    def elimininate_outliers_and_normalize(scores, images_paths, threshold):
        aux_scores = []
        aux_images_paths = []
        
        for idx, score in enumerate(scores):
            if score <= threshold:
                aux_scores.append(score)
                aux_images_paths.append(images_paths[idx])
        
        arr = np.array(aux_scores)
        normalized_arr = arr / np.max(arr)
        
        return aux_images_paths, list(normalized_arr)
    """
        
        #return aux_images_paths, aux_scores
    # Ensure the output directory exists
    if not folders_path.exists():
        print(f"- The directory '{folders_path}' does not exist. Creating it.")
        folders_path.mkdir(parents=True, exist_ok=True)

    # Check if the specific .pkl file for the current index exists
    if fold_file_path.exists():
        print(f"- Found file '{fold_file_path}'. Loading predictions and labels.")
        images_paths_raw, scores_raw = load_labels_and_predictions(fold_file_path)
        #images_paths, scores = elimininate_outliers_and_normalize(scores_raw, images_paths_raw, threshold)
    else:
        print(f"- File '{fold_file_path}' is missing. Generating predictions and labels.")
        scores_raw, images_paths_raw = yolo_segmentation_extract_scores(model, folder_name, dataset_path, len(class_names), batch_size=batch_size)
        save_labels_and_predictions(scores_raw, images_paths_raw, fold_file_path)
        #images_paths, scores = elimininate_outliers_and_normalize(scores_raw, images_paths_raw, threshold) 
        print(f"- Predictions and labels saved to '{fold_file_path}'.")

    return scores_raw, images_paths_raw

"""
- - - COMMON FUNCTIONALITIES - - - 

"""
def save_labels_and_predictions(labels: Any, predictions: Any, output_path: str) -> None:
    """
    Save model predictions and ground truth labels to a pickle file.

    This function can be applied to any kind of labels and predictions.

    :param labels: Ground truth label data to save.
    :param predictions: Model prediction data to save.
    :param output_path: Path where the pickle file will be saved.
    """
    try:
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'wb') as file:
            pickle.dump((labels, predictions), file)
        print(f"Successfully saved labels and predictions to {output_path}")
    except Exception as e:
        print(f"Error saving pickle file: {str(e)}")
    
def load_labels_and_predictions(output_path: str) -> Tuple[Any, Any]:
    """
    Load model predictions and ground truth labels from a pickle file.

    :param output_path: Path to the pickle file containing saved data.
    :return: A tuple containing predictions and labels.
    """
    with open(output_path, 'rb') as file:
        labels, predictions = pickle.load(file)
    return predictions, labels

def extract_desired_low_scores(
    bins_set: List[Tuple[float, float]],
    scores: List[float]
) -> List[List[int]]:
    """
    Extract indices of scores that fall within each specified interval.

    :param bins_set: List of tuples, where each tuple defines an interval (lower_bound, upper_bound).
    :param scores: List of scores from the entire prediction set.
    :return: A list of lists, where each sublist contains indices of scores that fall within the corresponding interval.
    """
    low_scores = []
    bin_low_scores = []
    for bin in bins_set:
        for index, score in enumerate(scores):
            upper_bin = bin[1]
            lower_bin = bin[0]
            if score <= upper_bin and score > lower_bin:
                bin_low_scores.append(index)

        low_scores.append(bin_low_scores)
        bin_low_scores = []
    return low_scores


def load_yolo_model(training_path: Path, train_name: Path) -> Any:
    """
    Load the trained YOLO model for a specific training fold.

    :param training_path: Path to the training directory.
    :param train_name: Name of the training folder.
    :return: An instance of the YOLO model.
    """
    model_path = training_path / train_name / 'weights' / 'best.pt'
    return YOLO(model_path)


def choose_inspected_bins(bins: List[float], n: int) -> List[Tuple[float, float]]:
    """
    Display a menu of intervals derived from a sorted list of bin boundaries and allow the user to select 'n' intervals.

    :param bins: A sorted list of bin boundaries.
    :param n: The number of intervals the user wants to select.
    :return: A list of tuples, each representing a selected interval.
    """
    # Display available intervals
    print("Available intervals:")
    for i in range(len(bins) - 1):
        print(f"[{i}] Interval: [{round(bins[i], 6)}, {round(bins[i + 1], 6)})")
    
    selected_bins = []
    
    while len(selected_bins) < n:
        try:
            choice = int(input(f"Select the index of interval {len(selected_bins) + 1} to use as a threshold: "))
            if 0 <= choice < len(bins) - 1:
                # Get the selected interval
                selected_interval = (bins[choice], bins[choice + 1])
                if selected_interval not in selected_bins:
                    selected_bins.append(selected_interval)
                else:
                    print("You already selected this interval. Please select a different one.")
            else:
                print("Invalid choice. Please select a valid index.")
        except ValueError:
            print("Please enter a valid number.")
    
    # Ensure intervals are returned in sorted order (although it should already be sorted)
    selected_bins.sort(key=lambda x: x[0])  # Sort by the lower value of the interval
    return selected_bins

def select_intervals(bins_set: List[Tuple[float, float]]) -> List[int]:
    """
    Displays a menu for the user to select intervals to remove from analysis.

    :param bins_set: A list of score intervals (each a tuple of lower and upper bounds).
    :return: A list of selected interval indices.
    """
    print("\n" + "=" * 50)
    print("         INTERVAL SELECTION MENU")
    print("=" * 50)
    
    print("Available intervals:")
    for i, (low, high) in enumerate(bins_set):
        print(f"  {i}. ({low:.4f}, {high:.4f})")
    print("=" * 50)
    
    # Prompt for the number of intervals to remove
    while True:
        try:
            n_choices = int(input("Enter the number of intervals to remove: ").strip())
            if 0 < n_choices <= len(bins_set):
                break
            else:
                print(f"Please enter a number between 1 and {len(bins_set)}.")
        except ValueError:
            print("Invalid input! Please enter a valid integer.")
    
    selected_indices = []
    print("\nSelect the intervals by their index:")
    for i in range(1, n_choices + 1):
        while True:
            try:
                index = int(input(f"Choice {i} of {n_choices}: ").strip())
                if 0 <= index < len(bins_set) and index not in selected_indices:
                    selected_indices.append(index)
                    low, high = bins_set[index]
                    print(f"Added interval {index}: ({low:.4f}, {high:.4f})")
                    break
                else:
                    print("Invalid index or already selected. Please try again.")
            except ValueError:
                print("Invalid input! Please enter a valid integer.")
    
    print("\nSelected intervals:")
    for idx in selected_indices:
        low, high = bins_set[idx]
        print(f"  {idx}. ({low:.4f}, {high:.4f})")
    print("=" * 50 + "\n")
    
    return selected_indices

def create_dataframe(populations, sample_desired_proportion):
    """
    Generates a random sample from a population and creates an Excel file for tracking.
    
    :param populations: List of indices to sample from
    :param sample_desired_proportion: Fraction of population to sample, if the proportion is 1 it just takes the whole population

    Returns:
        Tuple containing:
        - np.array: Sorted array of sampled indices
        - pd.DataFrame: DataFrame containing sampled indices
    """
    
    frames = []
    all_samples = []
    if sample_desired_proportion < 1:
        for population in populations:
            sample_size = int(len(population) * sample_desired_proportion)
            sample = np.random.choice(population, size = sample_size, replace =False)
            sorted_sample = np.sort(sample).tolist()
            df = pd.DataFrame(sorted_sample, columns = ['Index'])
            df['GT: ¿Is the original image misslabeled?'] = np.nan
            frames.append(df)
            all_samples.append(sorted_sample)
        return frames, all_samples
    else:
        for population in populations:
            sorted_population = np.sort(population)
            df = pd.DataFrame(sorted_population, columns=['Index']) 
            df['GT: ¿Is the original image misslabeled?'] = np.nan
            frames.append(df)
    return frames, None

