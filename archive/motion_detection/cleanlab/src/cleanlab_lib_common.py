"""
Script name: Common Library for Cleanlab Analysis
File Name: cleanlab_lib_common.py
Author: <PERSON>
Date: 2025-02-13
Purpose: 
    This script provides common utility functions for analyzing dataset quality with Cleanlab.
    It includes tools for memory management, loading/saving predictions and labels, extracting fold
    information, generating histograms of quality scores, selecting problematic data samples,
    and creating dataframes for manual inspection of potentially mislabeled data.
"""
import logging
import os
import pickle
import gc
import torch
from pathlib import Path
from typing import Any, Tuple, List, Dict, Optional, Union

import cv2
import yaml
import pandas as pd
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt
from skimage.draw import polygon

from cleanlab.object_detection.summary import visualize
from cleanlab.segmentation.summary import display_issues
from cleanlab.segmentation.rank import get_label_quality_scores as get_label_quality_scores_seg
from cleanlab.segmentation.rank import issues_from_scores

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

"""
- - - COMMON FUNCTIONALITIES - - - 

"""
def clear_memory():
    """Free up memory by garbage collection, clearing CUDA cache, and releasing system buffers"""
    # Basic garbage collection
    gc.collect()
    
    # Clear CUDA cache if available
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        
    # Release OpenCV windows and buffers
    cv2.destroyAllWindows()
    
    # Clear large NumPy arrays from the current scope
    for name in list(globals().keys()):
        if name.startswith('__'):
            continue
        obj = globals()[name]
        if isinstance(obj, np.ndarray) and obj.nbytes > 100000000:  # 100MB
            del globals()[name]
            
    # Sync filesystem buffers (Linux)
    try:
        os.system("sync")
    except Exception:
        pass

    
def save_labels_and_predictions(labels: Any, predictions: Any, output_path: str) -> None:
    """
    Save model predictions and ground truth labels to a pickle file.

    This function can be applied to any kind of labels and predictions.

    :param labels: Ground truth label data to save.
    :param predictions: Model prediction data to save.
    :param output_path: Path where the pickle file will be saved.
    """
    try:
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'wb') as file:
            pickle.dump((labels, predictions), file)
        print(f"Successfully saved labels and predictions to {output_path}")
    except Exception as e:
        print(f"Error saving pickle file: {str(e)}")
    
def load_labels_and_predictions(output_path: str) -> Tuple[Any, Any]:
    """
    Load model predictions and ground truth labels from a pickle file.

    :param output_path: Path to the pickle file containing saved data.
    :return: A tuple containing predictions and labels.
    """
    with open(output_path, 'rb') as file:
        labels, predictions = pickle.load(file)
    return predictions, labels

def get_folder_name(packages_path: Path) -> Optional[str]:
    """
    Extract the folder name from the given path.

    :param packages_path: Path to the folder where all packages are located.
    :return: The name of the folder if found; otherwise, None.
    """
    # Get the last part of the path
    folder_name = os.path.basename(os.path.normpath(packages_path))
    if folder_name:
        return folder_name
    else:
        print("No folder name found.")
        return None

def get_fold_associated_with_model(training_path: Path, train_name: str) -> Optional[str]:
    """
    Extract the fold index associated with a given model training.

    This function retrieves the fold index from the training configuration.

    :param training_path: Path to the folder where all trainings are located.
    :param train_name: Name of the training folder to search for.
    :return: The fold index as a string if found; otherwise, None.
    """
    yaml_file_path = training_path / train_name / 'args.yaml'

    # Load training configuration
    with open(yaml_file_path, 'r') as file:
        data = yaml.safe_load(file)
    data_label = data.get('data', '')

    if data_label:
        # Extract fold name from path
        fold_name = os.path.dirname(data_label)
        print(f"Base path without fold_x.yaml: {fold_name}")
        idx = os.path.basename(fold_name).split('_')[-1]
        return idx
    else:
        print("No data label found.")

def generate_cleanlab_histogram(
    output_directory: str,
    scores: List[float],
    folder_name: int,
    desired_bins: int
) -> np.ndarray:
    """
    Generate and save a histogram of label quality scores.

    This function creates a histogram from the provided scores, overlays a line graph,
    and saves the plot as a PNG image.

    :param output_directory: Directory path where the histogram image will be saved.
    :param scores: A list of label quality scores.
    :param idx: Fold index used for naming the saved image.
    :param desired_bins: The number of bins or a list of bin edges for the histogram.
    :return: Sorted array of bin edges.
    """


    statistics_folder = os.path.join(output_directory, "statistics_folder")
    os.makedirs(statistics_folder, exist_ok=True)

    plt.figure(figsize=(10, 7))
    plt.style.use('default')

    # Draw histogram
    hist_values, bin_edges, _ = plt.hist(
        scores, bins=desired_bins, color='#2ecc71', alpha=0.9,
        edgecolor='black', linewidth=1.2
    )

    # Compute bin centers
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

    # Draw line graph on top of the histogram
    plt.plot(bin_centers, hist_values, color='red', marker='o', linestyle='-', linewidth=2)

    plt.title('Distribution of Label Quality Scores', fontsize=14, pad=20, fontweight='bold')
    plt.xlabel('Label Quality Score', fontsize=12, labelpad=12)
    plt.ylabel('Frequency', fontsize=12, labelpad=12)
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.axvline(np.mean(scores), color='#e74c3c', linestyle='--', linewidth=2,
                label=f'Mean: {np.mean(scores):.2f}')
    plt.axvline(np.median(scores), color='#3498db', linestyle='--', linewidth=2,
                label=f'Median: {np.median(scores):.2f}')
    plt.legend(fontsize=10)

    for i, center in enumerate(bin_centers):
        count = int(hist_values[i])
        plt.text(center, hist_values[i] + max(hist_values)*0.05, f"{count}",
                 ha='center', fontsize=10, color='black')
        plt.text(center, hist_values[i] / 2, f"{i}", ha='center', va='center',
                 fontsize=10, color='black')

    plot_path = os.path.join(statistics_folder, f"lnq_{folder_name}.jpg")
    plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    return np.sort(bin_edges)

def extract_desired_low_scores(
    bins_set: List[Tuple[float, float]],
    scores: List[float]
) -> List[List[int]]:
    """
    Extract indices of scores that fall within each specified interval.

    :param bins_set: List of tuples, where each tuple defines an interval (lower_bound, upper_bound).
    :param scores: List of scores from the entire prediction set.
    :return: A list of lists, where each sublist contains indices of scores that fall within the corresponding interval.
    """
    low_scores = []
    bin_low_scores = []
    for bin in bins_set:
        for index, score in enumerate(scores):
            upper_bin = bin[1]
            lower_bin = bin[0]
            if score <= upper_bin and score > lower_bin:
                bin_low_scores.append(index)

        low_scores.append(bin_low_scores)
        bin_low_scores = []
    return low_scores


def load_yolo_model(training_path: Path, train_name: Path) -> Any:
    """
    Load the trained YOLO model for a specific training fold.

    :param training_path: Path to the training directory.
    :param train_name: Name of the training folder.
    :return: An instance of the YOLO model.
    """
    model_path = training_path / train_name / 'weights' / 'best.pt'
    return YOLO(model_path)


def choose_inspected_bins(bins: List[float], n: int) -> List[Tuple[float, float]]:
    """
    Display a menu of intervals derived from a sorted list of bin boundaries and allow the user to select 'n' intervals.

    :param bins: A sorted list of bin boundaries.
    :param n: The number of intervals the user wants to select.
    :return: A list of tuples, each representing a selected interval.
    """
    # Display available intervals
    print("Available intervals:")
    for i in range(len(bins) - 1):
        print(f"[{i}] Interval: [{round(bins[i], 6)}, {round(bins[i + 1], 6)})")
    
    selected_bins = []
    
    while len(selected_bins) < n:
        try:
            choice = int(input(f"Select the index of interval {len(selected_bins) + 1} to use as a threshold: "))
            if 0 <= choice < len(bins) - 1:
                # Get the selected interval
                selected_interval = (bins[choice], bins[choice + 1])
                if selected_interval not in selected_bins:
                    selected_bins.append(selected_interval)
                else:
                    print("You already selected this interval. Please select a different one.")
            else:
                print("Invalid choice. Please select a valid index.")
        except ValueError:
            print("Please enter a valid number.")
    
    # Ensure intervals are returned in sorted order (although it should already be sorted)
    selected_bins.sort(key=lambda x: x[0])  # Sort by the lower value of the interval
    return selected_bins

def select_intervals(bins_set: List[Tuple[float, float]]) -> List[int]:
    """
    Displays a menu for the user to select intervals to remove from analysis.

    :param bins_set: A list of score intervals (each a tuple of lower and upper bounds).
    :return: A list of selected interval indices.
    """
    print("\n" + "=" * 50)
    print("         INTERVAL SELECTION MENU")
    print("=" * 50)
    
    print("Available intervals:")
    for i, (low, high) in enumerate(bins_set):
        print(f"  {i}. ({low:.4f}, {high:.4f})")
    print("=" * 50)
    
    # Prompt for the number of intervals to remove
    while True:
        try:
            n_choices = int(input("Enter the number of intervals to remove: ").strip())
            if 0 < n_choices <= len(bins_set):
                break
            else:
                print(f"Please enter a number between 1 and {len(bins_set)}.")
        except ValueError:
            print("Invalid input! Please enter a valid integer.")
    
    selected_indices = []
    print("\nSelect the intervals by their index:")
    for i in range(1, n_choices + 1):
        while True:
            try:
                index = int(input(f"Choice {i} of {n_choices}: ").strip())
                if 0 <= index < len(bins_set) and index not in selected_indices:
                    selected_indices.append(index)
                    low, high = bins_set[index]
                    print(f"Added interval {index}: ({low:.4f}, {high:.4f})")
                    break
                else:
                    print("Invalid index or already selected. Please try again.")
            except ValueError:
                print("Invalid input! Please enter a valid integer.")
    
    print("\nSelected intervals:")
    for idx in selected_indices:
        low, high = bins_set[idx]
        print(f"  {idx}. ({low:.4f}, {high:.4f})")
    print("=" * 50 + "\n")
    
    return selected_indices

def create_dataframe(populations, sample_desired_proportion):
    """
    Generates a random sample from a population and creates an Excel file for tracking.
    
    :param populations: List of indices to sample from
    :param sample_desired_proportion: Fraction of population to sample, if the proportion is 1 it just takes the whole population

    Returns:
        Tuple containing:
        - np.array: Sorted array of sampled indices
        - pd.DataFrame: DataFrame containing sampled indices
    """
    
    frames = []
    all_samples = []
    if sample_desired_proportion < 1:
        for population in populations:
            sample_size = int(len(population) * sample_desired_proportion)
            sample = np.random.choice(population, size = sample_size, replace =False)
            sorted_sample = np.sort(sample).tolist()
            df = pd.DataFrame(sorted_sample, columns = ['Index'])
            df['GT: ¿Is the original image misslabeled?'] = np.nan
            frames.append(df)
            all_samples.append(sorted_sample)
        return frames, all_samples
    else:
        for population in populations:
            sorted_population = np.sort(population)
            df = pd.DataFrame(sorted_population, columns=['Index']) 
            df['GT: ¿Is the original image misslabeled?'] = np.nan
            frames.append(df)
    return frames, None

def create_study_excel(dataframes, output_path, folder_name, bins):
    """
    Create the study excel to analyze manually one bin
    :param dataframes:
    :param output_path:
    :param fold_idx:
    Returns:
        Tuple containing:
        - str: string containing the path to the excel folder
    """

    excel_folder_path = os.path.join(output_path, 'statistics_folder', f'Excel Study: {folder_name}')
    os.makedirs(excel_folder_path, exist_ok=True)  # Create the folder
    print(f"Created statistics directory: {excel_folder_path}")

    for i, df in enumerate(dataframes):
        excel_file_path = os.path.join(excel_folder_path, f'Bin: ({round(bins[i][0], 4)}, {round(bins[i][1], 4)}).xlsx')

        if os.path.exists(excel_file_path):
            print(f"The file '{excel_file_path}' already exists. Skipping the creation of this file.")
            continue  # Skip to the next iteration

        df.to_excel(excel_file_path, index=True)
    return excel_folder_path
