"""
Script name: CleanLab Library for Detection
File Name: cleanlab_lib_detection.py
Author: <PERSON>
Date: 2023-02-13
Purpose: 
    This script provides functionality for object detection using YOLO models.
    It handles data extraction, feature extraction, label processing, 
    and visualization of potentially mislabeled data in detection tasks.
    The script interfaces with the CleanLab library to identify and 
    analyze possible labeling issues in object detection datasets.
"""
import logging
import os
import pickle
import gc
import torch
from pathlib import Path
from typing import Any, Tuple, List, Dict, Optional, Union

import cv2
import yaml
import pandas as pd
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt
from skimage.draw import polygon

from cleanlab.object_detection.summary import visualize
from cleanlab.segmentation.summary import display_issues
from cleanlab.segmentation.rank import get_label_quality_scores as get_label_quality_scores_seg
from cleanlab.segmentation.rank import issues_from_scores
from cleanlab_lib_common import  *

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

"""
- - - DETECTION - - - 

"""
def yolo_detection_extract_features_and_probs(
    model: YOLO,
    fold_index: int, 
    folders_path: str,
    num_classes: int
) -> tuple[np.ndarray, list]:
    """
    Extracts features and probabilities from model predictions for a given fold.
    This function processes images and their corresponding labels from a validation set,
    runs model predictions, and formats the data for cleanlab analysis.
    

    :param model: YOLO model instance used for making predictions
    :param fold_index: Index identifying which fold to process
    :param folders_path: Base directory path containing the fold data
    :param num_classes: Total number of object classes in the dataset
        
    Returns:
        Tuple containing:
        - np.ndarray: Array of model predictions/features
        - list: List of ground truth labels with bounding boxes and image names
    """
    try:
        features = []
        labels = []
        base_dir = Path(folders_path)

        # Define directories for the current fold
        image_dir = base_dir / f'fold_{fold_index}' / 'val' / 'images'
        label_dir = base_dir / f'fold_{fold_index}' / 'val' / 'labels'

        # Collect image and label paths
        images_paths = sorted(image_dir.glob('*.jpg'))
        labels_paths = sorted(label_dir.glob('*.txt'))

        # Check that there are the same number of images and labels
        if len(images_paths) != len(labels_paths):
            print(f"Error: Number of images ({len(images_paths)}) and labels ({len(labels_paths)}) do not match.")
            return [], []
        for img_path in images_paths:
            label_path = label_dir / (img_path.stem + '.txt')
            if not label_path.exists():
                print(f"Label file not found for image: {img_path}")
                continue
            # Load image and get predictions
            img = cv2.imread(str(img_path))
            if img is None:
                print(f"Error reading image: {img_path}")
                continue

            pred = model(img)[0]
            pred_boxes = pred.boxes.xyxy.cpu().numpy()
            pred_conf = pred.boxes.conf.cpu().numpy()
            pred_cls = pred.boxes.cls.cpu().numpy().astype(int)

            # Initialize predictions list
            pred_list = [np.empty((0, 5)) for _ in range(num_classes)]
            for box, conf, cls in zip(pred_boxes, pred_conf, pred_cls):
                pred_list[cls] = np.vstack([pred_list[cls], np.append(box, conf)])

            # Extract ground truth labels
            bboxes = []
            gt_labels = []
            with open(label_path, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    cls_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])

                    img_height, img_width, _ = img.shape
                    x1 = int((x_center - width / 2) * img_width)
                    y1 = int((y_center - height / 2) * img_height)
                    x2 = int((x_center + width / 2) * img_width)
                    y2 = int((y_center + height / 2) * img_height)

                    bboxes.append(np.array([x1, y1, x2, y2]))
                    gt_labels.append(cls_id)

            bboxes = np.array(bboxes)
            gt_labels = np.array(gt_labels)

            labels.append({'bboxes': bboxes, 'labels': gt_labels, 'image_name': str(img_path)})
            features.append(pred_list)


        aux_features = []
        aux_labels = []
        for idx, image in enumerate(labels):
            if len(image['bboxes']) > 0 and len(image['labels']) > 0:
                aux_labels.append(image)
                aux_features.append(features[idx])
        return np.array(aux_features, dtype=object), aux_labels
    except Exception as e:
        logging.error(f"Error extracting features: {str(e)}")
        raise

def process_labels_and_predictions_odet(
    idx: int,
    dataset_path: Path,  # Root path to dataset containing image folds
    output_directory: Path,  # Where to save analysis results
    class_names: dict[str, str],  # Maps class IDs to human-readable names
    model: YOLO
) -> None:
    """
    Process predictions and labels for a specific fold.
    If the corresponding .pkl file exists, load it; otherwise, compute, save, and return it.

    :param idx: Fold index to process.
    :param dataset_path: Path to the dataset containing fold splits.
    :param output_directory: Directory to save predictions and labels.
    :param class_names: Mapping of class IDs to human-readable names.
    :param model: YOLO model for feature extraction and predictions.
    :return: Tuple containing predictions and labels.
    """
    folders_path = output_directory / 'labels_and_predictions'
    fold_file_path = folders_path / f'fold_{idx}_labels_and_predictions.pkl'

    # Ensure the output directory exists
    if not folders_path.exists():
        print(f"- The directory '{folders_path}' does not exist. Creating it.")
        folders_path.mkdir(parents=True, exist_ok=True)

    # Check if the specific .pkl file for the current index exists
    if fold_file_path.exists():
        print(f"- Found file '{fold_file_path}'. Loading predictions and labels.")
        predictions, labels = load_labels_and_predictions(fold_file_path)
    else:
        print(f"- File '{fold_file_path}' is missing. Generating predictions and labels.")
        predictions, labels = yolo_detection_extract_features_and_probs(model, idx, dataset_path, len(class_names))
        save_labels_and_predictions(labels, predictions, fold_file_path)
        print(f"- Predictions and labels saved to '{fold_file_path}'.")

    return predictions, labels

def yolo_visualize_misslabeled_data_DET(
    labels: List[Dict[str, Any]],
    predictions: List[Dict[str, Any]],
    class_name: Dict[int, str],
    output_directory: str,
    fold_idx: int,
    issue_labels: List[List[int]],
    bins: List[Tuple[float, float]]
) -> None:
    """
    Creates visualization plots comparing model predictions with ground truth labels
    for potentially mislabeled images.
    
    :param labels: Ground truth label information
    :param predictions: Model predictions
    :param class_name: Dictionary mapping class IDs to class names
    :param output_directory: Directory to save visualization plots
    :param fold_idx: Index of current fold
    :param issue_labels: Indices of images identified as potentially mislabeled
    """

    for n, bin in enumerate(bins):
        for i in issue_labels[n]:
            image = labels[i]
            pred = predictions[i]
            image_path = labels[i]['image_name']
            cleanlab_path = os.path.join(output_directory,'cleanlabs',f'Bins study: fold_{fold_idx}' , f'Bin: ({round(bin[0],2)}, {round(bin[1],2)})')

            if not os.path.exists(cleanlab_path):
                os.makedirs(cleanlab_path, exist_ok=False)
                print(f'cleanlab folder created in {output_directory}')
            else:
                print(f'folder "cleanlabs" already exists in {output_directory}')

            image_name = os.path.basename(labels[i]['image_name'])
            save_path = os.path.join(cleanlab_path, f'{i}_{image_name}')
            print(f'SAVE PATH:',save_path)

            visualize(image_path,
                        label=image,
                        prediction=pred,
                        class_names=class_name,
                        overlay=False,
                        save_path=save_path,
                        figsize=(48,36))


            print('Prediction/Labeling comparison SAVED')
            print(f'{image_path} looks like it has been MISSLABELED')

            plt.close('all')
