#!/usr/bin/env python3
"""
Script name: Main Pipeline
File name: main.py
Author: <PERSON>
Date: 2023-02-13
Purpose:
    Evaluates trained segmentation models across multiple folds and identifies potentially 
    mislabeled data. Processes validation data interactively, generates quality scores,
    and creates a cleaned dataset by removing problematic samples based on user decisions.
    The pipeline includes threshold finding, histogram visualization, and creating 
    comparison datasets for training.
"""

import os
import sys
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

# Allow imports from the parent directory
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from helper.yaml_file_manager import read_yaml_config
from helper.file_management import *
from classes.cleanlab_det_cls import CleanLab_DET
from classes.cleanlab_seg_cls import CleanLab_SEG
from helper.threshold_finder import threshold_finder
from helper.yaml_file_manager import file_creation
from helper.menu_display import process_fold_interactive, apply_decisions_to_folds

def main(dataset_path: Path,
         training_path: Path,
         output_directory: Path,
         class_names: dict[str, str],
         class_array,
         data_dir: Path,
         original_dataset: Path) -> None:
    """
    Main pipeline for evaluating model predictions and identifying mislabeled data.
    
    Args:
        dataset_path: Path to dataset organized in folds.
        training_path: Path containing trained model artifacts.
        output_directory: Where to save analysis outputs.
        class_names: Mapping of numeric class IDs to human-readable names.
        class_array: List of class names.
        data_dir: Directory for data storage.
        original_dataset: Path to the original dataset.
    """
    folders = []
    fold_cleaners = []

    # Initialize fold cleaners for each training folder
    for train_name in training_path.iterdir():
        print(f"\n=== Processing: {train_name} ===")
        fold = CleanLab_SEG(training_path, train_name, dataset_path, output_directory, class_names, class_array)
        fold.init_yolo_model(train_name)
        fold.init_cleanlab_os_data(batch_size=2)
        fold_cleaners.append(fold)

    # Process each fold interactively
    first_fold = random.choice(fold_cleaners)
    threshold = threshold_finder(first_fold.scores, initial_threshold=1, initial_bins=30)
    first_fold.generate_scores_histogram(desired_bins_display=55, threshold=threshold)
    first_fold_desicion =  process_fold_interactive(fold_cleaner=first_fold, sample_proportion=0.2)
    folders.append({f'{first_fold.idx}': first_fold_desicion}) 
    fold_cleaners.remove(first_fold)

    for fold_cleaner in fold_cleaners:
        remaining_desicion = apply_decisions_to_folds(fold_cleaner)
        folders.append({f'{fold_cleaner.idx}': remaining_desicion})

    print("\n=== Starting cleaned dataset creation ===")
    backup = make_backup_folder(dataset_path)
    #backup = Path("/mnt/data/projects/K-fold-data_prep/static/datasets/ptp_dataset/4_fold_partition_copy")
    raw_dset = None

    for folder in folders:
        for idx, data in folder.items():
            remove_desired_bins(idx, backup, data['bins_to_remove'], data['bins'],
                                data['low_scores'], data['images_paths'])
            raw_dset = cleaned_dataset_creation(idx, backup)


    new_dataset_path, reduced_dataset_path = comparison_dataset_creation(raw_dset, original_dataset, data_dir, use_common_for_test=True)
    delete_backup_folder(backup)
    delete_backup_folder(raw_dset)
    file_creation(new_dataset_path, class_names)
    base_dataset_creation(reduced_dataset_path, original_dataset, data_dir/'cleaned_database')

if __name__ == '__main__':
    # Load configuration
    config_manager_path = os.path.join(os.path.dirname(__file__), "../config/config_manager.yaml")
    current_active_config = read_yaml_config(config_manager_path)
    config_path = os.path.join(os.path.dirname(__file__), f"../config/{current_active_config['active_config']}")
    config = read_yaml_config(config_path)

    # Define necessary paths and parameters
    dataset_path = Path(config['paths']['folds_path'])
    print("DATASET_PATH:", dataset_path)
    output_directory = Path(config['paths']['results_dir'])
    data_dir = Path(config['paths']['data_dir'])
    original_dataset = Path(config['paths']['original_dataset'])
    training_path = output_directory / 'training_results'
    class_names = config["class_names"]
    class_array = config.get('class_name_array', [])

    main(dataset_path, training_path, output_directory, class_names, class_array, data_dir, original_dataset)