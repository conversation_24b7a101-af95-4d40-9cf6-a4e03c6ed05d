"""
Module: Interactive Fold Processing
Author: <PERSON>
Date: 2025-02-13

This module provides functions to interactively process a data fold and apply decisions across folds.
It is used in the context of evaluating model predictions to identify potentially mislabeled data.

Functions:
    - process_fold_interactive: Allows a user to interactively decide which bins to remove from a fold.
    - apply_decisions_to_folds: Automatically recalculates dynamic values for a fold and applies predetermined decisions.
    - menu_number_defect_bins: Displays a menu for users to select a custom number of bins for defect analysis.
"""

import os
import sys
from typing import Any, Dict, List
from helper.threshold_finder import threshold_finder

def process_fold_interactive(fold_cleaner: Any, sample_proportion) -> Dict[str, Any]:
    """
    Interactively process a fold and return the user's decisions.

    :param fold_cleaner: CleanLab_SEG. An instance representing a fold's data and processing methods.
    :param bins_taken: int. Number of bins to extract for inspection.
    :return: dict. A dictionary containing:
        - 'bins': The extracted bins.
        - 'bins_to_remove': List of bin indices selected for removal.
        - 'low_scores': Array of low score values.
        - 'images_paths': List of image paths corresponding to the scores.
    """
    decision: Dict[str, Any] = {'bins': [], 'bins_to_remove': []}

    while True:
        print(f"\nImages scores histogram for fold_{fold_cleaner.folder_name} has been created.")
        print(f"\n=== FOLD_{fold_cleaner.folder_name} Analysis Menu ===")
        print("1. Extract desired bins for inspection")
        print("2. Visualize Defect Bins")
        print("3. Choose bins to remove from the dataset")
        print("4. Generate statistics after studying all the bins")
        print("5. Finish analysis, remove bins selected from all the folds, and create new dataset")

        choices = input("Select actions (comma-separated, e.g., 1,3,4): ").strip().split(',')
        selected = [choice.strip() for choice in choices]

        if "1" in selected:
            decision['bins'] = fold_cleaner.extract_defect_bins()
            fold_cleaner.create_bin_study_excels(sample_proportion)
            print("Defect bins extracted.")
        if "2" in selected:
            fold_cleaner.visualize_defect_bins()
            print("Defect bins visualization completed.")
        if "3" in selected:
            fold_cleaner.select_bins_to_remove()
            decision['bins_to_remove'] = fold_cleaner.extracted_bins_idx
        if "4" in selected:
            fold_cleaner.get_presicion_and_ci()
        if "5" in selected:
            decision['low_scores'] = fold_cleaner.low_scores
            decision['images_paths'] = fold_cleaner.images_paths
            break

        print("\nActions completed. You may choose additional actions or move to the next fold.")

    return decision

def apply_decisions_to_folds(fold_cleaner: Any) -> Dict[str, Any]:
    """
    Recalculate dynamic values for a fold and apply the bins-to-remove decision.

    :param fold_cleaner: CleanLab_SEG. An instance representing a fold's data and processing methods.
    :return: dict. A dictionary containing:
        - 'bins': The recalculated bins.
        - 'bins_to_remove': The automatically selected bins to remove.
        - 'low_scores': The recalculated low score values.
        - 'images_paths': The recalculated image paths.
    """
    bins = fold_cleaner.extract_defect_bins()
    low_scores = fold_cleaner.low_scores
    images_paths = fold_cleaner.images_paths
    bins_to_remove = fold_cleaner.select_bins_to_remove()
    decision: Dict[str, Any] = {
        'bins': bins,
        'bins_to_remove': bins_to_remove,
        'low_scores': low_scores,
        'images_paths': images_paths
    }
    fold_cleaner.restore_default_values()
    return decision

def menu_number_defect_bins(bins) -> int:
    """
    Displays a formatted menu to prompt the user for a custom number of bins for defect analysis.
    It shows the available bins from the CleanLab_SEG object and returns the chosen number.

    :param cleanlab_seg_obj: An instance of CleanLab_SEG.
    :return: The custom number of bins to inspect.
    """
    print("\n-------------------------------------------------")
    print("         Defect Bins Selection Menu")
    print("-------------------------------------------------")
    print(f"Available bins: {len(bins)-1}")
    print("-------------------------------------------------\n")
    
    while True:
        try:
            n_bins = int(input("Enter the number of bins you want to inspect: "))
            if n_bins > 0:
                print(f"\nYou have selected {n_bins} bin(s) for inspection.\n")
                return n_bins
            else:
                print("Error: Please enter a positive integer.\n")
        except ValueError:
            print("Error: Invalid input. Please enter a positive integer.\n")
