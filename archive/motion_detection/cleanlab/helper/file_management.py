"""
Script Name: Dataset Management Utilities
File Name: file_management.py
Author: <PERSON>

This script provides utility functions for managing dataset files, including:
- Extracting filenames from data bins based on score ranges
- Copying and moving files using symbolic links
- Creating and managing backup folders with symlinks
- Removing selected bins from dataset folds
- Creating cleaned datasets from existing data
- Managing train/validation splits for dataset comparison
- Creating datasets by matching files between source and reference structures

These utilities support dataset organization for machine learning workflows,
particularly focusing on handling symbolic links and maintaining consistent
validation sets across different dataset versions.
"""

import os
import shutil
import random
import subprocess
from pathlib import Path
from typing import List, Tuple, Set, Optional
from tqdm import tqdm

def extract_file_names(
    low_scores: List[List[int]], 
    labels: List[dict], 
    text_file_extension: str, 
    bin_selected: int
) -> Tuple[Set[str], Set[str]]:
    """
    Extracts filenames of images and labels located in a selected bin.
    
    :param low_scores: Indices of images in specific intervals.
    :param labels: List of dictionaries containing image metadata.
    :param text_file_extension: File extension for label files.
    :param bin_selected: Index of the selected bin.
    :return: A tuple of sets containing image and label filenames.
    """
    image_set = set()
    text_set = set()
    #[[bin_1 low_scores],[ bin_2 low_scores],[bin_3 low_scores], ...]
    for low_score in low_scores[bin_selected]:
        image_name = Path(labels[low_score]["image_name"]).name
        image_set.add(image_name)
        txt_name = f"{str(image_name).rsplit('.', 1)[0]}.{text_file_extension}"
        text_set.add(txt_name)
    return image_set, text_set

def copy_files(directory: str, target_set: Set[str], destination: str) -> None:
    """
    Copies symbolic links of specified files from a directory to a destination.
    
    :param directory: Directory to search for files.
    :param target_set: Set of filenames to copy.
    :param destination: Directory to copy the symbolic links to.
    """
    if not os.path.exists(destination):
        os.makedirs(destination)
        print(f"Created destination folder: {destination}")
    else:
        print(f"Destination folder exists: {destination}")

    count = 0
    print(f"Starting copy from '{directory}' for {len(target_set)} target file(s).")

    for root, _, files in os.walk(directory):
        for file in files:
            if file in target_set:
                file_path = os.path.join(root, file)
                destination_path = os.path.join(destination, file)

                if os.path.exists(destination_path):
                    continue

                if os.path.islink(file_path):
                    link_target = os.readlink(file_path)
                    os.symlink(link_target, destination_path)
                else:
                    os.symlink(file_path, destination_path)

                count += 1

    print(f"Finished copying. Total files copied: {count}")

    # Output counts based on the directory type
    if "labels" in directory:
        print(f"Total symbolic links .txt copied: {count}")
    elif "images" in directory:
        print(f"Total symbolic links .jpg copied: {count}")

def move_files(directory: str, target_set: Set[str], destination: str) -> None:
    """
    Moves symbolic links of specified files from a directory to a destination.
    
    :param directory: Directory to search for files.
    :param target_set: Set of filenames to move.
    :param destination: Directory to move the symbolic links to.
    """
    if not os.path.exists(destination):
        os.makedirs(destination)

    count = 0

    for root, _, files in os.walk(directory):  # Traverse directory and subdirectories
        for file in files:
            if file in target_set:  # Check if the file matches any in the target list
                file_path = os.path.join(root, file)
                destination_path = os.path.join(destination, file)
                if os.path.islink(file_path):  # Check if the file is a symbolic link
                    link_target = os.readlink(file_path)
                    os.symlink(link_target, destination_path)
                    os.unlink(file_path)  # Remove the original symbolic link
                    count += 1
    if "labels" in directory:
        print(f"Total symbolic links .txt moved: {count}")
    elif "images" in directory:
        print(f"Total symbolic links .jpg moved: {count}")


def make_backup_folder(source_folder: str) -> Optional[Path]:
    """
    Creates a backup folder with symbolic links to the original dataset files.
    
    This function scans the source folder recursively, creates a corresponding
    backup folder (removing any existing one), and then creates symbolic links 
    for all files in the source folder while preserving the directory structure. 
    A progress bar is displayed during the linking process.
    
    :param source_folder: str. Path to the dataset folder.
    :return: Optional[Path]. Path to the newly created backup folder, or None if an error occurs.
    """
    source_path = Path(source_folder)
    if not source_path.exists() or not source_path.is_dir():
        print(f"Error: Source folder '{source_folder}' does not exist or is not a directory.")
        return None

    # Define the backup folder path (same parent, with '_copy' appended)
    destination_folder = source_path.parent / f"{source_path.name}_copy"

    try:
        print(f"Preparing to create backup for '{source_path}'...")
        
        # Remove existing backup folder if it exists
        if destination_folder.exists():
            print(f"Removing existing backup folder: '{destination_folder}'")
            shutil.rmtree(destination_folder)

        # Gather all files in the source folder recursively
        files = [p for p in source_path.rglob("*") if p.is_file()]
        total_files = len(files)
        
        print(f"Found {total_files} files in '{source_folder}'.")
        print(f"Creating backup folder structure at '{destination_folder}'...")

        # Create symbolic links for each file with a progress bar
        for file in tqdm(files, total=total_files, desc="Creating symlinks", unit="file"):
            # Determine the relative path and corresponding destination path
            relative_path = file.relative_to(source_path)
            dest_file = destination_folder / relative_path

            # Ensure the destination directory exists
            dest_file.parent.mkdir(parents=True, exist_ok=True)

            # Create the symbolic link if it doesn't already exist
            if not dest_file.exists():
                os.symlink(file, dest_file)

        print(f"Successfully created backup '{destination_folder}' with symbolic links.")
        return destination_folder

    except Exception as e:
        print(f"Error creating backup folder: {e}")
        return None
    

def delete_backup_folder(folder_path: str) -> None:
    """
    Deletes a backup folder and its contents.
    
    :param folder_path: Path to the folder to delete.
    """
    if not os.path.exists(folder_path):
        print(f"Error: Folder '{folder_path}' does not exist.")
        return

    try:
        shutil.rmtree(folder_path)  # Deletes the folder and all its contents
        print(f"Successfully deleted '{folder_path}'.")
    except Exception as e:
        print(f"Error deleting folder: {e}")


def remove_desired_bins(
    folder_idx: int, 
    directory: str, 
    indexes_to_extract: List[int], 
    bins: List[Tuple[float, float]],
    low_scores: List[List[int]],
    images_paths: List[dict]
) -> None:
    """
    Removes files from specified bins in dataset folds.
    
    :param folder_idx: Fold index.
    :param directory: Base directory containing dataset folds.
    :param indexes_to_extract: Indices of bins to remove.
    :param bins: List of bins with score ranges.
    :param low_scores: List of low-score indices.
    :param images_paths: List of dictionaries containing image metadata.
    """
    for index_bin_selected in indexes_to_extract:
        bin_range = bins[index_bin_selected]
        print(f'FOLD {folder_idx}:')
        print(f"Processing bin {index_bin_selected} with range ({round(bin_range[0], 7)}, {round(bin_range[1], 7)})...")
        
        image_set, text_set = extract_file_names(low_scores, images_paths, "txt", index_bin_selected)
        
        images_directory = os.path.join(directory, f'fold_{folder_idx}', "val", "images")
        images_dest = os.path.join(directory, "removed_files", 
                                   f'Bin: ({round(bin_range[0], 5)}, {round(bin_range[1], 5)})', 
                                   f'fold_{folder_idx}', 'images')
        
        labels_directory = os.path.join(directory, f'fold_{folder_idx}', "val", "labels")    
        labels_dest = os.path.join(directory, "removed_files", 
                                   f'Bin: ({round(bin_range[0], 5)}, {round(bin_range[1], 5)})', 
                                   f'fold_{folder_idx}', "labels")
        
        print(f"Moving image files from '{images_directory}' to '{images_dest}'...")
        image_count = move_files(images_directory, image_set, images_dest)
        print(f"Moved {image_count} image files.")
        
        print(f"Moving label files from '{labels_directory}' to '{labels_dest}'...")
        labels_count = move_files(labels_directory, text_set, labels_dest)
        print(f"Moved {labels_count} label files.\n")
        
        # Clear sets to free up memory
        image_set = set()
        text_set = set()

    print("Finished processing selected bins.")

def cleaned_dataset_creation(fold_idx: int, dataset_path: Path) -> Path:
    """
    Creates a cleaned dataset with symbolic links.
    
    :param fold_idx: Fold index to process.
    :param dataset_path: Path to dataset directory.
    :return: Path to the cleaned dataset.
    """
    destination_folder = dataset_path.parent / "DATASET_RAW"
    print(f"Creating cleaned dataset at '{destination_folder}'...")
    os.makedirs(os.path.join(destination_folder, "images"), exist_ok=True)
    os.makedirs(os.path.join(destination_folder, "labels"), exist_ok=True)

    for split in ["val"]:
        # Copy images
        images_folder = os.path.join(dataset_path, f"fold_{fold_idx}", split, "images")
        images_destination = os.path.join(destination_folder, "images")
        image_files = {file for file in os.listdir(images_folder) if file.lower().endswith('.jpg')}
        print(f"Copying {len(image_files)} images from '{images_folder}' to '{images_destination}'...")
        copy_files(images_folder, image_files, images_destination)

        # Copy labels
        labels_folder = os.path.join(dataset_path, f"fold_{fold_idx}", split, "labels")
        labels_destination = os.path.join(destination_folder, "labels")
        label_files = {file for file in os.listdir(labels_folder) if file.lower().endswith('.txt')}
        print(f"Copying {len(label_files)} labels from '{labels_folder}' to '{labels_destination}'...")
        copy_files(labels_folder, label_files, labels_destination)

    print("Cleaned dataset creation complete.")
    return destination_folder

def comparison_dataset_creation(
    dataset1: Optional[str],
    dataset2: Optional[str],
    output_folder: str,
    val_ratio: float = 0.2,
    use_common_for_test: bool = False,
    seed: int = 42
) -> str:
    """
    Creates train/validation (or testing) splits.
    
    When both datasets are provided:
      - If use_common_for_test is True, a fixed test set is chosen from dataset2 using the seed.
        Both datasets then use the intersection of this fixed set with their images.
      - Otherwise, a common validation set is computed from the intersection of dataset1 and dataset2.
    
    This ensures that both datasets get identical validation sets.
    
    :param dataset1: Path to the first dataset ("reduced").
    :param dataset2: Path to the second dataset ("original").
    :param output_folder: Base folder for NEW_DATASET.
    :param val_ratio: Ratio of validation images.
    :param use_common_for_test: Flag to use a fixed test set from dataset2.
    :param seed: Seed for reproducibility.
    :return: Path to the NEW_DATASET folder.
    """
    def get_files(dataset, folder):
        path = os.path.join(dataset, folder)
        return {f for f in os.listdir(path) if f.lower().endswith('.jpg')} if dataset and os.path.exists(path) else set()

    if dataset1 and dataset2:
        if use_common_for_test:
            # Select a fixed set from dataset2 and force equality via intersection.
            ds2_images = list(get_files(dataset2, "images"))
            ds2_images.sort()
            random.seed(seed)
            random.shuffle(ds2_images)
            test_count = int(len(ds2_images) * val_ratio)
            fixed_test_set = set(ds2_images[:test_count])
            common_val_set = fixed_test_set.intersection(get_files(dataset1, "images"))
        else:
            # Compute a common set from the intersection of both datasets.
            common_images = list(get_files(dataset1, "images").intersection(get_files(dataset2, "images")))
            common_images.sort()
            random.seed(seed)
            random.shuffle(common_images)
            test_count = int(len(common_images) * val_ratio)
            common_val_set = set(common_images[:test_count])
    else:
        common_val_set = None

    for dataset, label in [(dataset1, "reduced"), (dataset2, "original")]:
        if not dataset:
            continue
        ds_images = get_files(dataset, "images")
        if common_val_set is not None:
            splits = {
                "train": ds_images - common_val_set,
                "val": ds_images.intersection(common_val_set)
            }
        else:
            imgs = list(ds_images)
            random.seed(seed)
            random.shuffle(imgs)
            split = int(len(imgs) * val_ratio)
            splits = {
                "train": set(imgs[split:]),
                "val": set(imgs[:split])
            }

        for folder, ext in [("images", ".jpg"), ("labels", ".txt")]:
            for split_name, files in splits.items():
                src_dir = os.path.join(dataset, folder)
                file_set = files if ext == ".jpg" else {f.replace(".jpg", ext) for f in files}
                dest_dir = os.path.join(output_folder, "NEW_DATASET", label, folder, split_name)
                copy_files(src_dir, file_set, dest_dir)

    return os.path.join(output_folder, "NEW_DATASET"), os.path.join(output_folder, "NEW_DATASET", "reduced")

def base_dataset_creation(source_dataset: str, reference_dataset: str, output_folder: str) -> str:
    """
    Creates a dataset by finding images in a source dataset (any folder structure),
    checking for corresponding images and annotations in a reference dataset (flat structure),
    and copying the actual files to an output folder.
    
    :param source_dataset: Path to source dataset with any folder structure
    :param reference_dataset: Path to reference dataset with flat images/labels structure
    :param output_folder: Path to the destination folder
    :return: Path to the created dataset
    """
    # Create output directory
    output_path = Path(output_folder)
    output_path.mkdir(exist_ok=True)
    
    print(f"\n===== Dataset Creation Process =====")
    print(f"Source dataset: {source_dataset}")
    print(f"Reference dataset: {reference_dataset}")
    print(f"Output folder: {output_folder}")
    
    # Find all JPG files recursively in source dataset at any depth
    print(f"\nScanning source dataset for images...")
    source_path = Path(source_dataset)
    jpg_files = []
    jpg_file_paths = {}  # Maps filename to full path
    
    # Use tqdm for scanning progress
    for jpg_file in tqdm(list(source_path.rglob("*.jpg")), desc="Scanning source files", unit="file"):
        jpg_filename = jpg_file.name
        jpg_files.append(jpg_filename)
        jpg_file_paths[jpg_filename] = jpg_file
    
    print(f"Found {len(jpg_files)} images in source dataset")
    
    # Reference dataset path (flat structure)
    ref_path = Path(reference_dataset)
    print(f"Checking for matches in reference dataset...")
    
    # Preparation phase - count matches for accurate progress bar
    matches = 0
    for jpg_file in tqdm(jpg_files, desc="Checking references", unit="file"):
        if (ref_path / jpg_file).exists():
            matches += 1
    
    print(f"Found {matches} matching images in reference dataset")
    
    # Copy images and corresponding json files
    images_copied = 0
    labels_copied = 0
    skipped = 0
    
    print(f"\nStarting file copying process...")
    
    for jpg_file in tqdm(jpg_files, desc="Copying files", unit="file"):
        # Check if image exists in reference dataset
        ref_image_path = ref_path / jpg_file
        if ref_image_path.exists():
            # Copy actual image from source
            source_image_path = jpg_file_paths[jpg_file]
            dest_image_path = output_path / jpg_file
            
            # Skip if already exists
            if dest_image_path.exists():
                skipped += 1
                continue
                
            shutil.copy2(source_image_path, dest_image_path)
            images_copied += 1
            
            # Check for corresponding JSON
            json_file = f"{Path(jpg_file).stem}.json"
            ref_json_path = ref_path / json_file
            if ref_json_path.exists():
                # Copy actual JSON file to same output folder
                dest_json_path = output_path / json_file
                shutil.copy2(ref_json_path, dest_json_path)
                labels_copied += 1
    
    print(f"\n===== Dataset Creation Summary =====")
    print(f"Total images found in source: {len(jpg_files)}")
    print(f"Images copied: {images_copied}")
    print(f"JSON files copied: {labels_copied}")
    print(f"Files skipped (already exist): {skipped}")
    print(f"Missing JSON files: {images_copied - labels_copied}")
    print(f"Dataset creation complete at: {output_path}")
    
    return str(output_path)


if __name__ == "__main__":
    # Test the utility functions
    source = "/mnt/data/projects/K-fold-data_prep/static/datasets/ptp_dataset/NEW_DATASET/reduced"
    reference = "/mnt/data/projects/K-fold-data_prep/static/datasets/ptp_dataset/old_ptp_dataset"
    output_folder = "/mnt/data/projects/K-fold-data_prep/static/datasets/ptp_dataset/old_ptp_dataset_cleaned"
    base_dataset_creation(source, reference, output_folder)
    print("Utility functions tested successfully.")