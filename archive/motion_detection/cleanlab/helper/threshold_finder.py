"""
Module: T<PERSON><PERSON>old Finder
Author: <PERSON>
Date: 2025-02-13

This module provides functions to compute frequency histograms of scores and iteratively adjust a
threshold until the histogram's initial bins show an increasing trend. It is used in the context of
evaluating model predictions to identify potentially mislabeled data.

Functions:
    - compute_frequencies(scores, threshold, n_bins): Computes frequency histogram of scores
    - find_first_peak(frequencies, min_slope): Determines if there's an increasing trend in the first bins
    - threshold_finder(scores, initial_threshold, initial_bins, min_slope, max_iterations): Iteratively
      adjusts threshold to find optimal binning
"""

import numpy as np
from numpy.typing import NDArray

def compute_frequencies(scores: NDArray[np.float_], threshold: float, n_bins: int) -> NDArray[np.int_]:
    bins = np.linspace(0, threshold, n_bins + 1)
    frequencies, _ = np.histogram(scores, bins=bins)
    return frequencies

def find_first_peak(frequencies: NDArray[np.int_], min_slope: float = 1.0) -> bool:
    """
    Fit a line to the first three bins and check if the slope exceeds a minimum value.
    
    :param frequencies: NDArray[np.int_]. Frequency counts.
    :param min_slope: float. Minimum slope required.
    :return: bool. True if the slope is above min_slope, False otherwise.
    """
    if len(frequencies) < 3:
        return False
    x = np.arange(3)
    slope, _ = np.polyfit(x, frequencies[:3], 1)
    if slope > min_slope:
        print(f"Peak found: slope={slope:.2f} exceeds min_slope={min_slope}")
        return True
    print(f"Insufficient slope: slope={slope:.2f} < min_slope={min_slope}")
    return False

def threshold_finder(
    scores: NDArray[np.float_],
    initial_threshold: float,
    initial_bins: int,
    min_slope: float = 1.0,
    max_iterations: int = 1000
) -> float:
    """
    Iteratively adjust the threshold and number of bins until the first three bins
    show an increasing trend based on a minimum slope criterion.
    
    :param scores: NDArray[np.float_]. Array of score values.
    :param initial_threshold: float. Starting threshold for binning.
    :param initial_bins: int. Initial number of bins.
    :param min_slope: float. Minimum required slope for the first three bins.
    :param max_iterations: int. Maximum iterations to avoid infinite loops.
    :return: float. Adjusted threshold value.
    """
    threshold = initial_threshold
    n_bins = initial_bins
    iteration = 0
    frequencies = compute_frequencies(scores, threshold, n_bins)
    print("Initial frequencies:", frequencies)

    while n_bins >= 18 and threshold > 0.0001 and iteration < max_iterations:
        iteration += 1
        if not find_first_peak(frequencies, min_slope):
            new_threshold = threshold * 0.9  # Reduce threshold by 10%
            print(f"Iteration {iteration}: Peak not found. Updating threshold from {threshold:.4f} to {new_threshold:.4f}")
            threshold = new_threshold
        else:
            n_bins -= 2
            print(f"Iteration {iteration}: Peak found. Decreasing number of bins to {n_bins}")
        frequencies = compute_frequencies(scores, threshold, n_bins)
        print("Frequencies:", frequencies)

    return threshold