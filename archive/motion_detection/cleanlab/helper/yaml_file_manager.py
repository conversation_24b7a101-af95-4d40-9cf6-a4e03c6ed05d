"""
Script name: Yaml File Creation
File name: yaml_file_creation.py
Autor: <PERSON>

This module provides utility functions for creating and managing YAML configuration files
for machine learning datasets, particularly for YOLO model training.

Key features:
- Creating YAML configuration files for k-fold validation datasets
- Reading YAML configuration files
- Finding project folders using marker files
- Creating standardized config files from existing YAML files
- Adding new path entries to existing YAML configurations

These functions help maintain consistent dataset organization and configuration 
across different folds and experiments in a machine learning project.
"""

import os
from pathlib import Path
import yaml

# Configurations
def file_creation(
    dataset_path: Path,
    class_names: dict[str, str]
) -> None:
    """
    This func creates a .yaml for a set of dataset located into a folder.
    The structure is the following
        names:
      0: class_name_1
      1: class_name_2
      2: class_name_3
      3: class_name_4
    path: /specified/dataset/path
    train: fold_name/train/images
    val: fold_name/val/images

    ARGUMENTS:
        - dataset_path: path to out folder containing different datasets
        - class_names: array containing all class names, e.g ['class_name_1', 'class_name_2', ...]
    """
    dataset_path = Path(dataset_path)
    if not dataset_path.exists():
        raise FileNotFoundError(f"Dataset path {dataset_path} not found")

    # List all fold folders in the dataset path
    fold_folders = [f for f in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, f)) ]
    
    
    #and f.startswith("fold_")
    
    
    # Process each fold folder
    for fold_folder in fold_folders:
        # Paths for train, val, and optional test images
        yaml_content = {
            "path": str(dataset_path),
            "train": os.path.join(fold_folder, "train", "images"),
            "val": os.path.join(fold_folder, "val", "images"),
            # "test": os.path.join(dataset_path, fold_folder, "test", "images"),  # Uncomment if using a test set
            "names": {int(k): v for k, v in class_names.items()}
        }

        # Save the YAML file within the current fold folder
        yaml_filename = os.path.join(dataset_path, fold_folder, f"{fold_folder}.yaml")
        with open(yaml_filename, "w") as file:
            yaml.dump(yaml_content, file, default_flow_style=False)

        print(f"YAML file for {fold_folder} created at {yaml_filename}")  # Base path where your fold folders are located

    # List all fold folders in the dataset path
    # Save the YAML file within the current fold folder
    yaml_filename = os.path.join(dataset_path, fold_folder, f"{fold_folder}.yaml")
    with open(yaml_filename, "w") as file:
        yaml.dump(yaml_content, file, default_flow_style=False)

def read_yaml_config(config_path):
    with open(config_path, "r") as file:
        config = yaml.safe_load(file)
    return config

import yaml

def find_folder_with_marker(marker_filename, start_file=__file__):
    """
    Traverse upward from start_file to find the first directory containing marker_filename.
    Returns the basename of that directory, or None if not found.
    """
    current = Path(start_file).resolve()
    for parent in current.parents:
        if (parent / marker_filename).exists():
            return

def create_config_file(input_yaml_path, destination_folder, config_file_name, dataset_name):
    project_name = find_folder_with_marker("requirements.txt")
    # Read the input YAML file
    with open(input_yaml_path, 'r') as file:
        data = yaml.safe_load(file)
    
    # Extract names
    class_names = data.get('names', [])
    
    # Create class mappings
    class_mappings = {str(i): name for i, name in enumerate(class_names)}
    class_array = class_names + ["background"]
    
    # Define output structure
    output_data = {
        "paths": {
            "yolo_dataset_path": f"/mnt/data/projects/{project_name}/static",
            "base_dir": f"/mnt/data/projects/{project_name}/static",
            "data_dir": f"/mnt/data/projects/{project_name}/static/datasets/{dataset_name}",
            "results_dir": f"/mnt/data/projects/{project_name}/static/results_default",
            "original_dataset": f"/mnt/data/projects/{project_name}/static/datasets/{dataset_name}/YOLODataset-seg"
        },
        "model_config": {
            "epochs": 100,
            "learning_rate": 0.0001,
            "optimizer": "AdamW",
            "patience": 3,
            "save_period": 1,
            "save": True,
            "batch": 16
        },
        "class_names": class_mappings,
        "class_name_array": class_array
    }
    
    # Construct output path
    output_yaml_path = f"{destination_folder}/{config_file_name}.yaml"

    # Write to output YAML file
    with open(output_yaml_path, 'w') as file:
        yaml.dump(output_data, file, default_flow_style=False, sort_keys=False)

def add_path_to_yaml(yaml_file: str, name: str, path: str) -> None:
    """
    Adds a new path entry to the 'paths' section in the given YAML file.

    Args:
        yaml_file (str): Path to the YAML file.
        name (str): Name of the new path key to add.
        path (str): Corresponding path value.

    """
    # Load the existing YAML content
    with open(yaml_file, 'r') as file:
        config = yaml.safe_load(file)

    # Ensure 'paths' section exists
    if 'paths' not in config:
        config['paths'] = {}

    # Add the new name-path pair
    config['paths'][name] = path

    # Save the updated YAML content
    with open(yaml_file, 'w') as file:
        yaml.dump(config, file, default_flow_style=False, sort_keys=False)

    print(f"Added {name}: {path} to {yaml_file}")