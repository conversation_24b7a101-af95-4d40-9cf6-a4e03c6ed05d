import os
import torch
from ultralytics import YOLO
from pathlib import Path

def train_model(model_path, results_dir, config, fold_folder, yaml_file, device_id) -> None:
    os.environ["CUDA_VISIBLE_DEVICES"] = device_id
   
    model = YOLO(model_path)
    model(save=True)
    print(f"\nTraining on {fold_folder} using configuration from {yaml_file} on GPU {device_id}")

    
    model.train(
        data=yaml_file,
        epochs=config["epochs"],
        lr0=config["learning_rate"],
        batch=config["batch"],
        project=Path(results_dir) / 'training_results',
        name=f'train_{fold_folder}',
        optimizer=config["optimizer"],
        patience=config["patience"],
        save=config["save"],
        save_period=config["save_period"],
    )

    del model
    torch.cuda.empty_cache()
    torch.cuda.ipc_collect()

def evaluate_model(model_path, data_yaml, results_dir, device_id) -> None:
    os.environ["CUDA_VISIBLE_DEVICES"] = device_id

    model = YOLO(model_path)
    print(f"\nEvaluating model using data from {data_yaml} on GPU {device_id}")

    results = model.val(
        data=data_yaml,
        project=Path(results_dir) / 'evaluation_results',
        name='eval_results'
    )

    print(f"Evaluation results: {results}")

    del model
    torch.cuda.empty_cache()
    torch.cuda.ipc_collect()

if __name__ == "__main__":
    model_path = Path("/mnt/data/projects/K-fold-data_prep/results_nano_training/training_results/train_reduced/weights/best.pt")
    results_dir = Path("/mnt/data/projects/K-fold-data_prep/results_nano_training")
    fold_folder = "reduced"
    yaml_file = Path("/mnt/data/projects/K-fold-data_prep/sensibility_analysis/5_bins_out_f4_0.0025/reduced/reduced.yaml")

    device_id = "0"
    
    evaluate_model(model_path, yaml_file, results_dir, device_id)