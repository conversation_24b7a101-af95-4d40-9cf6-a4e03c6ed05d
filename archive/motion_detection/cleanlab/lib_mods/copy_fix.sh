#!/bin/bash
#########################################################################
# Fixed File Copy Script for K-fold-data_prep
#
# This script replaces the default cleanlab segmentation summary module
# with a custom version. Used as part of the project's environment setup
# to apply modifications to the cleanlab library without altering the 
# source package directly.
#
# Usage:
#   ./copy_fix.sh <path_to_project>
#########################################################################

# Check if the project path argument is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <path_to_project>"
    exit 1
fi

# Define paths
PROJECT_PATH="$1"
FIX_FILE="$PROJECT_PATH/lib_mods/summary.py"
TARGET_FILE="$PROJECT_PATH/venv/lib/python3.10/site-packages/cleanlab/segmentation/summary.py"

# Move to the project directory
cd "$PROJECT_PATH" || { echo "Project path not found!"; exit 1; }

# Copy the fix file
if cp "$FIX_FILE" "$TARGET_FILE"; then
    echo "File copied successfully!"
else
    echo "Failed to copy file!"
    exit 1
fi