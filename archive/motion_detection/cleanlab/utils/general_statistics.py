"""
Module name: General Statistics
File name: general_statistics.py
Author: <PERSON>
Date: 2025-02-13

This module provides statistical utility functions for calculating confidence intervals
and precision metrics for binary classification tasks. It includes functions to:
- Calculate confidence intervals for precision metrics with optional finite population correction
- Compute precision from labeled data stored in Excel files
- Validate ground truth columns in Excel files to ensure they contain valid binary labels
These utilities are particularly useful for evaluating model performance and estimating the
reliability of precision metrics when dealing with limited sample sizes.
"""

import pandas as pd
import math
from scipy.stats import norm


def ci_precision(precision, sample_size, confidence=0.95, population_size=None):
    """
    Calculate the confidence interval and margin of error for a given precision,
    ensuring the interval remains within [0, 1].
    
    Includes finite population correction when population_size is provided.

    Parameters:
      precision (float): The measured precision (TP / (TP + FP)), value between 0 and 1.
      sample_size (int): Total number of predictions (TP + FP).
      confidence (float): Confidence level (default 0.95).
      population_size (int, optional): Size of the total population. If provided, 
                                      finite population correction will be applied.

    Returns:
      tuple: (precision, margin of error, lower bound, upper bound)
    """
    if not 0 <= precision <= 1:
        raise ValueError("Precision must be between 0 and 1.")
    if sample_size <= 0:
        raise ValueError("Sample size must be positive.")
    if population_size is not None and population_size < sample_size:
        raise ValueError("Population size must be greater than or equal to sample size.")

    z = norm.ppf(1 - (1 - confidence) / 2)
    
    # Apply finite population correction if population size is provided
    if population_size is not None and population_size > 1:
        # Standard formula with finite population correction
        fpc = math.sqrt((population_size - sample_size) / (population_size - 1))
        margin = z * math.sqrt(precision * (1 - precision) / sample_size) * fpc
    else:
        # Standard formula for infinite population
        margin = z * math.sqrt(precision * (1 - precision) / sample_size)
    
    lower = max(0, precision - margin)
    upper = min(1, precision + margin)

    return precision, margin, lower, upper

def calc_single_bin_precision(file_path: str) -> float:
    """
    Calculates precision (TP / (TP + FP)) from an Excel file.
    The Excel file must contain a sheet named "Sheet1" with a column
    "GT: ¿Is the original image misslabeled?" holding binary values (0 or 1).

    :param file_path: Path to the Excel file.
    :return: Precision as a float.
    """
    df = pd.read_excel(file_path, sheet_name='Sheet1')
    col = df["GT: ¿Is the original image misslabeled?"]
    return col.sum() / col.size


def validate_gt_column(file_path):
    col = "GT: ¿Is the original image misslabeled?"
    try:
        df = pd.read_excel(file_path)
    except Exception as e:
        return f"{file_path}: Failed to read file ({e})."
    
    if col not in df.columns:
        return f"{file_path}: Missing required column '{col}'."
    
    # Check for non 0/1 values or missing entries
    invalid = df[~df[col].isin([0, 1]) | df[col].isna()]
    if not invalid.empty:
        return f"{file_path}: GT column must contain only 0 and 1."
    
    return None  # Requirement met