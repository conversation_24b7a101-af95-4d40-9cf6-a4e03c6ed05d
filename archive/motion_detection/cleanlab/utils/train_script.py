"""
Script name: Training Script
File name: train_script.py
Author: <PERSON>ga
Date: 2025-02-13

Purpose:
    This script trains a YOLO model on a specific fold of a dataset.
    It is invoked by `training_on_folds.py`, with each fold being trained separately.
    The script sets up the model, loads training configurations, and initiates the training process.
"""

import os
import json
import argparse
from pathlib import Path

import torch
from ultralytics import <PERSON><PERSON><PERSON>


def train_model(
    model_path: Path,
    results_dir: Path,
    config: dict,
    fold_folder: str,
    yaml_file: Path,
    device_id: str
) -> None:
    """
    Trains a YOLO model on a specific dataset fold.

    :param model_path: Path to the pretrained YOLO model (.pt file).
    :param results_dir: Directory to save training results.
    :param config: Dictionary containing YOLO training parameters (epochs, batch size, optimizer, etc.).
    :param fold_folder: Name of the dataset fold being trained.
    :param yaml_file: Path to the dataset configuration YAML file.
    :param device_id: GPU device ID to use for training.
    """
    model = YOLO(model_path)  # Load YOLO model
    model(save=True)
    print(f"\nTraining on {fold_folder} using configuration from {yaml_file} on GPU {device_id}")
    os.environ["CUDA_VISIBLE_DEVICES"] = f"{device_id}"
    # Train the model
    model.train(data=yaml_file,
                epochs=config["epochs"],
                lr0=config["learning_rate"],
                batch=config["batch"],
                project=Path(results_dir) / 'training_results',
                name=f'train_{fold_folder}',
                optimizer=config["optimizer"],
                patience=config["patience"],
                save=config["save"],
                save_period=config["save_period"],
                device=0  # PyTorch will map this correctly
                )

    # Free GPU memory
    del model
    torch.cuda.empty_cache()
    torch.cuda.ipc_collect()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train YOLO model on a dataset fold")
    parser.add_argument("--model_path", type=Path, required=True, help="Path to YOLO model")
    parser.add_argument("--results_dir", type=Path, required=True, help="Path to results directory")
    parser.add_argument("--fold_folder", type=str, required=True, help="Name of the fold folder")
    parser.add_argument("--yaml_file", type=Path, required=True, help="Path to YAML configuration file")
    parser.add_argument("--device", type=str, required=True, help="GPU ID")
    parser.add_argument("--config", type=str, required=True, help="Config dictionaty")

    args = parser.parse_args()
    config = json.loads(args.config)
    # Call training function with parsed arguments
    train_model(
        model_path=args.model_path,
        results_dir=args.results_dir,
        config=config,  # Extract model configuration from YAML
        fold_folder=args.fold_folder,
        yaml_file=args.yaml_file,
        device_id=args.device
    )