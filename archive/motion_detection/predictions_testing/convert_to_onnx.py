import argparse
from ultralytics import YOLO

#!/usr/bin/env python

def convert_to_onnx(py_model_path):
    # Load the model from the provided .py file
    model = YOLO(py_model_path)
    
    # Export the model to ONNX format
    model.export(format='engine')

if __name__ == '__main__':
    py_model = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/results/training_results/train_fold_1/weights/best.pt"
    convert_to_onnx(py_model)