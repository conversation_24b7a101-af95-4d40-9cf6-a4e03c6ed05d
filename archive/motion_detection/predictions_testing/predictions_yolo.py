import os
import argparse
import glob
import cv2
from ultralytics import <PERSON><PERSON><PERSON>

#!/usr/bin/env python3
"""
This script uses a YOLO model to run predictions on all images in a specified folder.
It loads a YOLO model from a given weights file, performs detections on each image,
then saves an annotated output image, a previsualization image, and a text file with raw prediction data.

Usage:
    python predictions.py path/to/model.pt path/to/images_folder --output_dir results
"""

def run_predictions(model_path, image_paths, output_dir):
    # Load YOLO model
    model = YOLO(model_path)

    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    for image_path in image_paths:
        # Run prediction on the image
        results = model.predict(source=image_path, show=False)
        # Get the first result for the image
        result = results[0]
        annotated_image = result.plot()

        # Save the annotated image
        output_image_path = os.path.join(output_dir, os.path.basename(image_path))
        cv2.imwrite(output_image_path, annotated_image)
        print(f"Processed {image_path} -> {output_image_path}")

        # Save the previsualization of the prediction (with a prefix)
        previs_image_path = os.path.join(output_dir, "previsualization_" + os.path.basename(image_path))
        cv2.imwrite(previs_image_path, annotated_image)
        print(f"Saved previsualization to {previs_image_path}")

        # Save prediction data to a text file (if any predictions exist)
        pred_file = os.path.splitext(output_image_path)[0] + ".txt"
        if result.boxes is not None and result.boxes.data is not None:
            boxes = result.boxes.data.cpu().numpy()
            with open(pred_file, "w") as f:
                for box in boxes:
                    # Save each box as: x1 y1 x2 y2 confidence class
                    f.write(" ".join(map(str, box)) + "\n")
            print(f"Saved predictions to {pred_file}")
        else:
            print(f"No predictions for {image_path}")


def main():
    # Gather image paths from the provided folder
    image_patterns = ["*.jpg", "*.jpeg", "*.png"]
    model_path = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/results/training_results/train_fold_1/weights/best.pt"
    images_dir = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/datasets/default/6_fold_partition/fold_1/val/images"
    output_dir = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/results/predictions"
    image_paths = []
    for pattern in image_patterns:
        image_paths.extend(glob.glob(os.path.join(images_dir, pattern)))
    
    if not image_paths:
        print(f"No images found in {images_dir}.")
        return

    run_predictions(model_path, image_paths, output_dir)

if __name__ == "__main__":
    main()