import time
import cv2
from yoloseg import YOLOSeg
import os
import glob

# Initialize YOLOv5 Instance Segmentator
model_path = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/models/bestv8.onnx"
yoloseg = YOLOSeg(model_path, conf_thres=0.5, iou_thres=0.3)

# Directory paths
image_dir = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/datasets/default/6_fold_partition/fold_1/val/images"
output_dir = "/mnt/data/projects/defis-medical/motion_detection/cleanlab/static/results/predictions_onnx"
os.makedirs(output_dir, exist_ok=True)

# Get list of image files
image_paths = []    
for ext in ['*.jpg', '*.jpeg', '*.png']:
    image_paths.extend(glob.glob(os.path.join(image_dir, ext)))

for img_path in image_paths:
    print(f"Processing: {os.path.basename(img_path)}")
    img = cv2.imread(img_path)
    if img is None:
        print(f"Failed to load image: {img_path}")
        continue

    # Time inference
    start_time = time.perf_counter()
    boxes, scores, class_ids, masks = yoloseg(img)
    inference_time = (time.perf_counter() - start_time) * 1000
    print(f"Inference time: {inference_time:.2f} ms")

    # Draw detections
    combined_img = yoloseg.draw_masks(img)

    # Save the result
    output_path = os.path.join(output_dir, os.path.basename(img_path))
    cv2.imwrite(output_path, combined_img)
    print(f"Saved: {output_path}")
