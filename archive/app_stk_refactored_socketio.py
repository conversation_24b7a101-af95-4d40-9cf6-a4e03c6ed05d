"""
### File: app_stk.py
### Author: <PERSON> & <PERSON>
### Email: <EMAIL>
### Copyright: Desion GmbH
### Description:
### Changelog:
"""

import base64
import copy
import cv2
import hashlib
import json
import logging
import os
import platform
import shutil
import signal
import struct
import sys
import threading
import time
import traceback
import uuid
from concurrent import futures
from datetime import datetime
from functools import partial
from logging.handlers import TimedRotatingFileHandler
from queue import Empty, Full, Queue, SimpleQueue
from typing import Dict, List, Optional, Union

import cv2
import numpy as np
import pyudev
import sqlalchemy
#from Crypto.Cipher import AES  # only if needed

from flask import (
    Flask,
    Response,
    jsonify,
    redirect,
    render_template,
    request,
    send_from_directory,
    url_for,
)
from flask_cors import CORS
from flask_login import UserMixin, login_required, login_user, logout_user
from sqlalchemy import select, text

from backend.src.setup_stk import *
from backend.src.camera_manager.camera_manager_setup import CameraSetupManager
from backend.src.license.license import *
from backend.src.stockings_analysis.stocking_analysys import *
from backend.src.stockings_analysis.stocking_image_processor import *
from backend.src.utils.files_manager import *
from backend.src.utils.logger_setup import *
from pathlib import Path
from flask_socketio import SocketIO, emit

# Création de l'instance Flask (à ajuster si déjà crée dans setup_stk)
app = Flask(__name__)
# Activation de CORS : ici on autorise uniquement localhost:3002 (frontend)
CORS(app, resources={r"/*": {"origins": "http://localhost:3001"}})

# Initialize Flask-SocketIO for real-time bi-directional communication
socketio = SocketIO(app, cors_allowed_origins="*")

@socketio.on('connect')
def on_connect():
    # Called when a client successfully establishes a WebSocket connection
    emit('status_response', {'message': 'connected'})

@socketio.on('request_status')
def handle_request_status(data):
    # Handle incoming 'request_status' events and respond with UI button configuration
    status = {
        "detection": [
            {
                "type": "button",
                "label": "Detection",
                "options": [
                    "true",
                    "scan",
                    ""
                ]
            }
        ],
        "front_camera_1": [
            {
                "type": "button",
                "label": "Cameras",
                "options": [
                    "true",
                    "camera",
                    ""
                ]
            }
        ],
        "server": [
            {
                "type": "button",
                "label": "Server",
                "options": [
                    "true",
                    "server",
                    ""
                ]
            }
        ]
    }
    # Emit the status configuration back to the client
    emit('status_response', status)

@socketio.on('request_item_info')
def handle_request_item_info(data):
    # Handle 'request_item_info'; expects optional stocking_result JSON payload
    if data and 'stocking_result' in data:
        try:
            stocking_result = json.loads(data['stocking_result'])
            emit('item_info_response', stocking_result)
        except json.JSONDecodeError:
            # Invalid JSON format: return error structure
            emit('item_info_response', {'error': 'Invalid JSON format in stocking_result'})
    else:
        # No data provided: send default empty result structure
        default_image = "static/assets/desion_logo.jpg"
        default = {
            "info": {
                "request_id": "None",
                "product": "STOCKING",
                "date": time.strftime("%d-%m-%Y %H:%M:%S"),
                "color": "#26C1E7"
            },
            "product_info": {
                "Leg Length": "-- cm",
                "Ankle Width": "-- cm",
                "Panty Length": "-- cm"
            },
            "defects": [],
            "decision": "OK",
            "images": {
                "top": default_image,
                "bottom": default_image
            }
        }
        emit('item_info_response', default)

@socketio.on('request_frames')
def handle_request_frames(data):
    # Handle 'request_frames'; return individual and full stocking images
    try:
        camera_id = data.get('camera_id', None)
        # If no camera_id, list available camera directories
        if not camera_id:
            camera_dirs = [d for d in os.listdir("./individual_frames") if os.path.isdir(os.path.join("./individual_frames", d))]
            emit('frames_response', {"status": "success", "cameras": camera_dirs})
            return
        
        frames_dir = f"./individual_frames/{camera_id}"
        if not os.path.exists(frames_dir):
            # Directory not found: return error message
            emit('frames_response', {"status": "error", "message": f"No frames available for camera {camera_id}"})
            return
        
        images = []
        full_stocking_images = []
        for filename in os.listdir(frames_dir):
            filepath = os.path.join(frames_dir, filename)
            if os.path.isfile(filepath) and filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                relative_path = f"individual_frames/{camera_id}/{filename}"
                if filename.startswith('full_stocking'):
                    full_stocking_images.append(relative_path)
                else:
                    images.append(relative_path)
        
        # Sort by numeric suffix if available
        images.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]) if '_' in x else 0)
        full_stocking_images.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]) if '_' in x and x.split('_')[-1].split('.')[0].isdigit() else 0)
        
        emit('frames_response', {
            "status": "success",
            "camera_id": camera_id,
            "individual_frames": images,
            "full_stocking_images": full_stocking_images
        })
    except Exception as e:
        # Log error and emit error response
        logger.error(f"Error serving individual frames: {str(e)}")
        emit('frames_response', {"status": "error", "message": str(e)})

if __name__ == "__main__":
    # Run the SocketIO server instead of Flask's built-in server
    socketio.run(app, host="0.0.0.0", port=3007, debug=True)
