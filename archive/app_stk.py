"""
### File: app_stk.py
### Author: <PERSON> & <PERSON>
### Email: <EMAIL>
### Copyright: Desion GmbH
### Description:
### Changelog:
"""

import signal
import numpy as np
import copy
import os
import shutil
import json
from queue import SimpleQueue, Queue, Empty, Full
import sys
from sqlalchemy import select, text
from flask import (
    render_template,
    redirect,
    url_for,
    request,
    Response,
    send_from_directory,
    jsonify
)

from functools import partial

from flask_login import (
    UserMixin,
    login_user,
    logout_user,
    login_required
)
import base64
import cv2
import time
import threading
from concurrent import futures
import logging
from logging.handlers import TimedRotatingFileHandler
import traceback

from backend.src.camera.camera_abc import CameraStatus
from backend.src.utils.data_process import yaml_to_dict, get_timestamp

sys.path.append("./")

from backend.src.setup_stk import *
import platform
import hashlib
import uuid
import struct
from Crypto.Cipher import AES

try:
    import wmi

    operating_system = "win"
except:
    import subprocess
    import pyudev

    operating_system = "lin"
from queue import Queue, Empty, Full

image_queue = Queue(maxsize=100)
camera_queues = {}
# Custom logger adapter to handle 'title' argument directly
class FlexibleLoggerAdapter(logging.LoggerAdapter):
    def process(self, msg, kwargs):
        title = kwargs.pop('title', None)
        if title:
            title = f"{title: <15.15}"
            msg = f"{title} | {msg}"
        return msg, kwargs

# Instantiate the custom logger adapter

def image_retrieval_thread(cameras):
    global camera_queues  # Add this line
    while not stop_threads:
        for camera in cameras:
            success, image = camera.retrieve_image()
            if success:
                try:
                    # Use the camera's serial number or alias as the key
                    queue_key = camera.serial_number
                    if queue_key not in camera_queues:
                        # Create queue if it doesn't exist yet
                        camera_queues[queue_key] = Queue(maxsize=100)
                        logger.info(f"Created queue for camera {queue_key}", title="Queue Setup")
                    
                    camera_queues[queue_key].put(image, timeout=1)
                except Full:
                    logger.warning(f"Queue for camera {camera.serial_number} is full. Dropping frame.", 
                                 title="Image Retrieval")
            else:
                time.sleep(0.01)

def get_system_information(cpu=False, mac=False, disk=False, os_id=False):
    """
    Retrieves system information such as CPU ID, MAC address, serial number, and UUID of the system.

    Returns:
        bytes: SHA256 digest of the system information, truncated to 16 bytes.
    """
    system_info = []

    if cpu:
        try:
            cpu_id = subprocess.check_output("lscpu | grep 'Model name' | awk -F ':' '{print $2}'", shell=True).decode().strip()
        except subprocess.CalledProcessError:
            cpu_id = platform.processor()
        system_info.append(cpu_id)
    
    if mac:
        try:
            mac_address = subprocess.check_output("cat /sys/class/net/eth0/address", shell=True).decode().strip()
        except subprocess.CalledProcessError:
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> ele) & 0xff) for ele in range(0, 8 * 6, 8)][::-1])
        system_info.append(mac_address)
    
    if disk:
        try:
            lsblk_output = subprocess.check_output(['lsblk', '-no', 'NAME']).decode('utf-8').split()
            for device_name in lsblk_output:
                try:
                    blkid_output = subprocess.check_output(['blkid', '-s', 'UUID', '-o', 'value', f'/dev/{device_name}']).decode('utf-8').strip()
                    if blkid_output:
                        system_info.append(blkid_output)
                        break
                except subprocess.CalledProcessError:
                    continue
        except subprocess.CalledProcessError:
            print("Failed to retrieve disk serial number")
    
    if os_id:
        try:
            with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                system_uuid = f.read().strip()
                system_info.append(system_uuid)
        except FileNotFoundError:
            print("System UUID not found")

    print(system_info)
    system_string = "_".join(system_info).replace(" ", "")
    system_string = hashlib.sha256(system_string.encode('utf-8')).digest()[:16]
    return system_string


def validate_license(filename_license):
    
    system_string = get_system_information(cpu=True)
    with open(filename_license, 'rb') as file:
        length_data = file.read(4)
        length = struct.unpack('i', length_data)[0]
        encrypted_string = file.read(length)
    iv = encrypted_string[-16:]
    encrypted_string = encrypted_string[:-16]
    cipher = AES.new(system_string, AES.MODE_CBC, iv)
    license_bytes = cipher.decrypt(encrypted_string)
    padding = license_bytes[-1]
    license_bytes = license_bytes[:-padding]
    license_string = license_bytes.decode("utf8")
    if "License_valid" in license_string:
        license_date = license_string.split(":")[1]
    else:
        return False
    # print(license_date)
    datetime_license = datetime.strptime(license_date, "%d-%m-%Y")
    datetime_current = datetime.now()

    if datetime_license >= datetime_current:
        return True
    else:
        return False
    
users = {'<EMAIL>': {
    'password': 'secret'},
    "cizeta": "cizetatest"}


class User(UserMixin):
    pass



Buffer = SimpleQueue()

defects_dump = []
dups = []  # list used to avoid sending duplicate defect entries to the frontend

# Conditional imports for the cameras drivers. Only needs to have installed the library for the camera you what to use .
if args.mode == "line":
    from backend.src.camera.Hikrobot.line_camera_driver import Camera
    #from backend.src.camera.Basler.line_camera_driver import Camera
elif args.mode == "simulator":
    from backend.src.camera.Simulator.camera_simulator import CameraSimulator
elif args.mode == "regular":
    from backend.src.camera.Basler.camera_basler import BaslerCamera as Camera
else:
    raise Exception("Incorrect Camera mode.")


###########################################
############ --- FUNCTIONS --- ############

global last_image_timestamp, stop_threads, first_image_name
last_image_timestamp = None
stop_threads = False
first_image_name = None

def signal_handler(sig, frame):
    global stop_threads
    logger.info("Ctrl-C pressed! Shutting down...", title="Exit")
    stop_threads = True
    # Wait for threads to finish
    time.sleep(1)
    os._exit(0)


### Thread function to update the status in status panel
@app.route('/get_status', methods=['GET'])
def get_status():
    status = {
        "detection": [
            {
                "type": "button",
                "label": "Detection",
                "options": [
                    "true",
                    "scan",
                    ""
                ]
            }
        ],
        "front_camera_1": [
            {
                "type": "button",
                "label": "Cameras",
                "options": [
                    "true",
                    "camera",
                    ""
                ]
            }
        ],
        "server": [
            {
                "type": "button",
                "label": "Server",
                "options": [
                    "true",
                    "server",
                    ""
                ]
            }
        ]
    }
    return jsonify(status)


@app.route('/get_item_info', methods=['GET'])
def get_results():
    if stocking_result:
        return jsonify(stocking_result)
    else:
        default_image = "static/assets/desion_logo.jpg"
        return jsonify({
            "info": {
                "request_id": "None",
                "product": "STOCKING",
                "date": time.strftime("%d-%m-%Y %H:%M:%S"),
                "color": "#26C1E7"
            },
            "product_info": {
                "Leg Length": "-- cm",
                "Ankle Width": "-- cm",
                "Panty Length": "-- cm"
            },
            "defects": [],
            "decision": "OK",
            "images": {
                "top": default_image,
                "bottom": default_image
            }
        })


# 1. route to send the images run_line to the frontend 
@app.route('/get_individual_frames', methods=['GET'])
def get_individual_frames():
    """Get all individual frames from specific cameras"""
    try:
        # Get camera ID from query parameter or use a default
        camera_id = request.args.get('camera_id')
        
        # If no camera_id specified, return all available camera directories
        if not camera_id:
            camera_dirs = [d for d in os.listdir("./individual_frames") 
                          if os.path.isdir(os.path.join("./individual_frames", d))]
            return jsonify({
                "status": "success", 
                "cameras": camera_dirs
            })
        
        # Check if directory exists for this camera
        frames_dir = f"./individual_frames/{camera_id}"
        if not os.path.exists(frames_dir):
            return jsonify({
                "status": "error",
                "message": f"No frames available for camera {camera_id}"
            })
        
        # Get all image files in the directory
        images = []
        full_stocking_images = []
        
        for filename in os.listdir(frames_dir):
            filepath = os.path.join(frames_dir, filename)
            if os.path.isfile(filepath) and filename.endswith(('.jpg', '.jpeg', '.png')):
                relative_path = f"individual_frames/{camera_id}/{filename}"
                if filename.startswith('full_stocking'):
                    full_stocking_images.append(relative_path)
                else:
                    images.append(relative_path)
        
        # Sort images by their frame number
        images.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]) if '_' in x else 0)
        full_stocking_images.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]) if '_' in x and x.split('_')[-1].split('.')[0].isdigit() else 0)
        
        return jsonify({
            "status": "success",
            "camera_id": camera_id,
            "individual_frames": images,
            "full_stocking_images": full_stocking_images
        })
    
    except Exception as e:
        logger.error(f"Error serving individual frames: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e)
        })

def retrieve_images_from_cameras(cameras):
    """Simulated function to retrieve images from cameras"""
    images = [] 
    for camera in cameras:
        success, image = camera.retrieve_image()  # Line
        print("success", success)
        if success:
            images.append(image)
    return images

def detect_stocking(image, threshold=15):
    """
    Detect stocking presence in the image based on pixel intensity variance.
    Args:
        image (np.array): Input image.
        threshold (int): Threshold for variance to determine stocking presence.
    
    Returns:
        bool: True if stocking is detected, False otherwise.
    """
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # Calculate the standard deviation of pixel intensity to detect changes
    std_intensity = np.std(gray)
    return std_intensity > threshold

def combine_images(image_list):
    """
    Vertically concatenate images from the list to form a full stocking image.
    Args:
        image_list (list of np.array): List of images to be concatenated.
    
    Returns:
        np.array: Combined image.
    """
    if len(image_list) > 0:
        image_full = cv2.vconcat(image_list)
        image_full = cv2.rotate(image_full, cv2.ROTATE_90_CLOCKWISE)

        cv2.imwrite("./full_stocking.jpg", image_full)
        return image_full 
    return None


def get_stocking_mask(image):
    """
    Create a mask for the stocking, excluding the background.
    """
    # Convert the image to HSV color space for better color range filtering
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Define the range for dark background (e.g., black, dark gray)
    lower_dark_background = np.array([0, 0, 0])  # Black/dark range
    upper_dark_background = np.array([180, 255, 50])  # Dark gray range
    
    # Define the range for blue background (e.g., navy, blue)
    lower_blue_background = np.array([90, 50, 50])  # Lower bound for blue hues
    upper_blue_background = np.array([130, 255, 255])  # Upper bound for blue hues
    
    # Create a mask for dark background
    dark_mask = cv2.inRange(hsv_image, lower_dark_background, upper_dark_background)
    
    # Create a mask for blue background
    blue_mask = cv2.inRange(hsv_image, lower_blue_background, upper_blue_background)
    
    # Combine both masks (dark + blue) to isolate the background
    combined_background_mask = cv2.bitwise_or(dark_mask, blue_mask)
    
    # Invert the combined mask to focus on the stocking (non-background areas)
    stocking_mask = cv2.bitwise_not(combined_background_mask)
    
    return stocking_mask

def is_in_foreground(bbox, stocking_mask):
    """
    Check if a bounding box lies mostly within the stocking (foreground).
    Args:
        bbox: Bounding box coordinates.
        stocking_mask: The mask of the stocking (foreground).
    
    Returns:
        bool: True if the bounding box is in the foreground, False otherwise.
    """
    x_min, y_min, x_max, y_max = bbox
    bbox_mask = stocking_mask[int(y_min):int(y_max), int(x_min):int(x_max)]
    
    # Check if most of the pixels in the bounding box are part of the foreground
    if np.mean(bbox_mask) > 0.5:  # 50% or more of the bounding box should overlap with the foreground
        return True
    return False

def analyse(images, pixel_ratio, timestamp):
    global ob_predictor
    global results_folder

    # Define classes for defect types
    classes = ["hole", "strips", "stain", "line", "knots", "fiber", "surface", "elastic"]
    
    # Result structure: list of defects with type and area
    result = []
    images_out = []
    save_images = False  # Flag to determine whether to save images

    if ob_predictor is not None:
        for idx, image in enumerate(images):
            image_out = image  # Default output image is the same as the input
            
            # Extract the foreground mask (to filter out detections on the background)
            stocking_mask = get_stocking_mask(image)
            
            # Extract the main color of the stocking
            avg_stocking_color = extract_main_stocking_color(image)
            
            with futures.ThreadPoolExecutor() as executor:
                thread_OB = executor.submit(ob_predictor.predict, image)
                try:
                    det = thread_OB.result()  # Get detection result
                    image_out = det["visualization"]  # Update output image with visualization
                    images_out.append(image_out)

                    # Iterate over detected bounding boxes and classes
                    for bbox, cls in zip(det['boxes'], det['classes']):
                        bbox = bbox[0]
                        
                        # Filter the bounding box to ensure it lies within the stocking (foreground)
                        if is_in_foreground(bbox, stocking_mask):
                            defect_type = cls  # Class of defect
                            width_mm = (bbox[2] - bbox[0]) * pixel_ratio
                            height_mm = (bbox[3] - bbox[1]) * pixel_ratio
                            area = width_mm * height_mm / 100

                            # Append each detected defect with its type and area
                            result.append({
                                "type": defect_type,  # Use class name for defect type
                                "area": area
                            })

                            save_images = True  # If defects are detected, mark for saving images
                        
                except Exception as e:
                    logger.error(f"Error during analysis: {e}")
                    logger.error(traceback.format_exc())

    return result, images_out, save_images, avg_stocking_color

def get_frames(configs):
    global stocking_result, camera_queues
    stocking_result = None
    
    # Maintain separate lists for each camera
    current_stockings = {}
    stocking_assembled = {}
    first_image_names = {}

    try:
        pixel_ratio = configs["REAL_WIDTH"] / configs["IMAGE_SIZE"][0]
    except:
        pixel_ratio = 1

    while not stop_threads:
        # Create a copy of the keys to iterate safely
        camera_ids = list(camera_queues.keys())
        
        # Process each camera queue
        for camera_id in camera_ids:
            if camera_id not in camera_queues:
                continue  # Queue may have been removed
                
            queue = camera_queues[camera_id]
            try:
                image = queue.get(timeout=0.1)  # Short timeout to check other queues
            except Empty:
                continue  # Try next queue
                
            # Initialize tracking data for this camera if needed
            if camera_id not in current_stockings:
                current_stockings[camera_id] = []
                stocking_assembled[camera_id] = False
                first_image_names[camera_id] = None
                
            # Process the image for this specific camera
            if detect_stocking(image):
                if first_image_names[camera_id] is None:
                    first_image_names[camera_id] = get_timestamp()
                    logger.info(f"New stocking begins on camera {camera_id}: {first_image_names[camera_id]}")
                
                current_stockings[camera_id].append(image)
                
                # Save individual frames
                os.makedirs(f"./individual_frames/{camera_id}", exist_ok=True)
                static_counter = getattr(get_frames, f"image_counter_{camera_id}", 0) + 1
                setattr(get_frames, f"image_counter_{camera_id}", static_counter)
                individual_path = os.path.join(f"./individual_frames/{camera_id}", f"image_{static_counter}.jpg")
                cv2.imwrite(individual_path, image)
                logger.info(f"Saved individual frame from camera {camera_id}: {individual_path}", 
                          title="Image Save")
                
            elif current_stockings[camera_id]:
                # Process completed stocking for this camera
                logger.info(f"Stocking finished on camera {camera_id}, sending to analysis")
                full_stocking_image = combine_images(current_stockings[camera_id])

                os.makedirs(f"./individual_frames/{camera_id}", exist_ok=True)
                stocking_name = f"full_stocking"
                stocking_index = 0
                stocking_path = os.path.join(f"./individual_frames/{camera_id}", f"{stocking_name}.jpg")

                # Check if file exists, increment counter until finding an available name
                while os.path.exists(stocking_path):
                    stocking_index += 1
                    stocking_path = os.path.join(f"./individual_frames/{camera_id}", f"{stocking_name}_{stocking_index}.jpg")

                # Save the full stocking image
                cv2.imwrite(stocking_path, full_stocking_image)
                logger.info(f"Saved full stocking: {stocking_path}", title="Image Save")


                stocking_assembled[camera_id] = True
                current_stockings[camera_id] = []
                first_image_names[camera_id] = None
                
                # Process the assembled stocking
                if stocking_assembled[camera_id]:
                    timestamp = get_timestamp()
                    stocking_folder = os.path.join(results_folder, f"stocking_{camera_id}_{timestamp}")
                    os.makedirs(stocking_folder, exist_ok=True)
                    
                    # Analysis and result accumulation
                    result, images_out, defects_found, avg_color = analyse([full_stocking_image], pixel_ratio, timestamp)
                    stocking_result = accumulate_results(result, timestamp, [full_stocking_image], 
                                                      images_out, defects_found, stocking_folder, avg_color)
                    logger.info(f"Analysis finished for camera {camera_id}")
                    logger.info(stocking_result)
                    stocking_assembled[camera_id] = False
            
        time.sleep(0.01)  # Small delay to prevent tight loop


def get_frames_sim(configs):
    global stocking_result, camera_queues
    stocking_result = None
    
    # Maintain separate lists for each camera
    current_stockings = {}
    stocking_assembled = {}
    first_image_names = {}

    try:
        pixel_ratio = configs["REAL_WIDTH"] / configs["IMAGE_SIZE"][0]
    except:
        pixel_ratio = 1

    while not stop_threads:
        # Create a copy of the keys to iterate safely
        camera_ids = list(camera_queues.keys())
        
        # Process each camera queue
        for camera_id in camera_ids:
            if camera_id not in camera_queues:
                continue  # Queue may have been removed
                
            queue = camera_queues[camera_id]
            try:
                image = queue.get(timeout=0.1)  # Short timeout to check other queues
            except Empty:
                continue  # Try next queue
                
            # Initialize tracking data for this camera if needed
            if camera_id not in current_stockings:
                current_stockings[camera_id] = []
                stocking_assembled[camera_id] = False
                first_image_names[camera_id] = None
                
            # Process the image for this specific camera
            if detect_stocking(image):
                if first_image_names[camera_id] is None:
                    first_image_names[camera_id] = get_timestamp()
                    logger.info(f"New stocking begins on camera {camera_id}: {first_image_names[camera_id]}")
                
                current_stockings[camera_id].append(image)
                
                # Save individual frames
                os.makedirs(f"./individual_frames/{camera_id}", exist_ok=True)
                static_counter = getattr(get_frames, f"image_counter_{camera_id}", 0) + 1
                setattr(get_frames, f"image_counter_{camera_id}", static_counter)
                individual_path = os.path.join(f"./individual_frames/{camera_id}", f"image_{static_counter}.jpg")
                cv2.imwrite(individual_path, image)
                logger.info(f"Saved individual frame from camera {camera_id}: {individual_path}", 
                          title="Image Save")
                
            elif current_stockings[camera_id]:
                # Process completed stocking for this camera
                logger.info(f"Stocking finished on camera {camera_id}, sending to analysis")
                full_stocking_image = combine_images(current_stockings[camera_id])

                os.makedirs(f"./individual_frames/{camera_id}", exist_ok=True)
                stocking_name = f"full_stocking"
                stocking_index = 0
                stocking_path = os.path.join(f"./individual_frames/{camera_id}", f"{stocking_name}.jpg")

                # Check if file exists, increment counter until finding an available name
                while os.path.exists(stocking_path):
                    stocking_index += 1
                    stocking_path = os.path.join(f"./individual_frames/{camera_id}", f"{stocking_name}_{stocking_index}.jpg")

                # Save the full stocking image
                cv2.imwrite(stocking_path, full_stocking_image)
                logger.info(f"Saved full stocking: {stocking_path}", title="Image Save")


                stocking_assembled[camera_id] = True
                current_stockings[camera_id] = []
                first_image_names[camera_id] = None
                
                # Process the assembled stocking
                if stocking_assembled[camera_id]:
                    timestamp = get_timestamp()
                    stocking_folder = os.path.join(results_folder, f"stocking_{camera_id}_{timestamp}")
                    os.makedirs(stocking_folder, exist_ok=True)
                    
                    # Analysis and result accumulation
                    result, images_out, defects_found, avg_color = analyse([full_stocking_image], pixel_ratio, timestamp)
                    stocking_result = accumulate_results(result, timestamp, [full_stocking_image], 
                                                      images_out, defects_found, stocking_folder, avg_color)
                    logger.info(f"Analysis finished for camera {camera_id}")
                    logger.info(stocking_result)
                    stocking_assembled[camera_id] = False
            
        time.sleep(0.01)  # Small delay to prevent tight loop
                    
def create_stocking_folder(timestamp):
    stocking_folder = os.path.join(results_folder, f"stocking_{timestamp}")
    os.makedirs(stocking_folder, exist_ok=True)
    return stocking_folder                

def extract_main_stocking_color(image):
    """
    Extract the main color of the stocking by masking out dark and blue backgrounds.
    Args:
        image (np.array): Input image.
    
    Returns:
        str: Average color of the stocking as a hex string (e.g., "#RRGGBB").
    """
    # Convert the image to HSV color space for better color range filtering
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Define the range for dark background (e.g., black, dark gray)
    lower_dark_background = np.array([0, 0, 0])  # Black/dark range
    upper_dark_background = np.array([180, 255, 50])  # Dark gray range
    
    # Define the range for blue background (e.g., navy, blue)
    lower_blue_background = np.array([90, 50, 50])  # Lower bound for blue hues
    upper_blue_background = np.array([130, 255, 255])  # Upper bound for blue hues
    
    # Create a mask for dark background
    dark_mask = cv2.inRange(hsv_image, lower_dark_background, upper_dark_background)
    
    # Create a mask for blue background
    blue_mask = cv2.inRange(hsv_image, lower_blue_background, upper_blue_background)
    
    # Combine both masks (dark + blue) to isolate the background
    combined_background_mask = cv2.bitwise_or(dark_mask, blue_mask)
    
    # Invert the combined mask to focus on the stocking (non-background areas)
    stocking_mask = cv2.bitwise_not(combined_background_mask)
    
    # Apply the mask to the original image to keep only stocking pixels
    stocking_pixels = cv2.bitwise_and(image, image, mask=stocking_mask)
    
    # Extract non-zero (non-background) pixels
    non_zero_pixels = stocking_pixels[np.where(stocking_mask != 0)]
    
    # If no stocking pixels are detected, return a default color (black)
    if len(non_zero_pixels) == 0:
        return '#000000'
    
    # Calculate the average color of the stocking (BGR format)
    average_color_bgr = np.mean(non_zero_pixels, axis=0)
    
    # Convert BGR to hex string format
    hex_color = '#{:02x}{:02x}{:02x}'.format(int(average_color_bgr[2]), int(average_color_bgr[1]), int(average_color_bgr[0]))
    
    return hex_color


def accumulate_results(result, timestamp, images, images_out, defects_found, stocking_folder, avg_stocking_color):
    src_image_paths = []
    vis_image_paths = []
    decision = "OK" if not defects_found else "NOK"
    
    # Save source images
    for i, image in enumerate(images):
        src_image_path = os.path.join(stocking_folder, f"{timestamp}_{i}_stocking-src.jpg")
        cv2.imwrite(src_image_path, image)
        relative_path = src_image_path.split("public/")[1]
        src_image_paths.append(f"http://10.10.4.146:3000/{relative_path}")


    # Save visualization images if defects are found
    
        for i, image_out in enumerate(images_out):
            vis_image_path = os.path.join(stocking_folder, f"{timestamp}_{i}_stocking-vis.jpg")
            cv2.imwrite(vis_image_path, image_out)
            vis_image_paths.append(vis_image_path.split("public/")[1])

    # Prepare the result data structure
    datetime_timestamp = datetime.strptime(timestamp, "%Y-%m-%d_%H-%M-%S")
    date_timestamp = datetime_timestamp.strftime("%d-%m-%Y %H:%M:%S")
    result_data = {
        "info": {
            "request_id": timestamp,
            "product": "STOCKING",
            "date": date_timestamp,
            "color": avg_stocking_color
        },
        "product_info": {
            "Leg Length": "-- cm",
            "Ankle Width": "-- cm",
            "Panty Length": "-- cm"
        },
        "defects": [
            [defect["type"].capitalize(), f"{defect['area']:.2f} mm²", "--"]
            for defect in result
        ],
        "decision": decision,
        "images": {
            "top": src_image_paths[0],
            "bottom": vis_image_paths[0] if vis_image_paths else src_image_paths[0]
        }
    }

    # Save the result as a JSON file in the stocking folder
    result_json_path = os.path.join(stocking_folder, f"{timestamp}_result.json")
    with open(result_json_path, 'w') as json_file:
        json.dump(result_data, json_file, indent=4)

    return result_data




### Initiate threads
def before_first_request():
    #threading.Thread(target=update_status, daemon=True).start()
    #threading.Thread(target=update_defects, daemon=True).start()
    #threading.Thread(target=get_frames, daemon=True).start()
    # threading.Thread(target=gen_frames, daemon=True).start()
    pass




def exit_handler():
    camera.stop_streaming()


if __name__ == '__main__':

    with open(os.path.join(args.configs, "config.yaml"), "r") as f:
        configs = yaml.safe_load(f)
    configs['cameras'] = yaml_to_dict(os.path.join(args.configs, "cameras.yaml"))
    
    global ob_predictor, defect_id, results_folder, logger
    log_file_directory = configs.get("LOG_PATH", "logs")
    os.makedirs(log_file_directory, exist_ok=True)
    # Logging configuration
    log = logging.getLogger('werkzeug')
    log.setLevel(logging.ERROR)
    logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-7.7s | %(message)s",
    handlers=[
        TimedRotatingFileHandler(os.path.join(log_file_directory,"defis.log"), when="midnight"),
        logging.StreamHandler(sys.stdout),
    ]
    )
    logger = FlexibleLoggerAdapter(logging.getLogger(__name__), {})
    
    

    ob_predictor = load_predictors()

    results_folder = os.path.join(configs["OUTPUT_FOLDER"], datetime.now().strftime("%Y%m%d"))
    os.makedirs(results_folder, exist_ok=True)
    cameras = []
    license_valid = True# validate_license("license-cizeta.lic")
    if not license_valid:
        print("License not valid")
        exit()
    else:
        print("License validated")

    if args.mode == "line":
        # Connecting to camera and setting it up
        # Must run only once
        for key in configs["cameras"]:
            if key != "common" and key!= "simulator":
                if args.cameras == "all" or key == selected_camera:
                    alias = configs['cameras'][key].get('alias', None)
                    logger.info(f"Trying to connect camera {key} with alias "
                                f"{alias}", title="Connection")
                    

                    # Attempts to connect to the camera until it succeeds.
                    i = 0
                    while i < configs["CAMERA_MAX_TRIES"]:
                        i += 1
                        try:
                            camera = Camera(serial_number=key, alias=alias, config=configs['cameras'], logger=logger)
                            
                            camera.start_streaming()
                            logger.info(f"Camera streaming : ({camera.cam.MV_CC_StartGrabbing})", title="Start streaming")

                            logger.info(f"Image size : ({camera.image_size})", title="Camera Props.")
                            break
                        except Exception as e:
                            logger.warning(f"Camera connection problem... Attempt {i}", title="Connection")
                            logger.error(traceback.format_exc(), title="Connection")
                            time.sleep(3)
                            continue
                    cameras.append(camera)
                else:
                    logger.info(f"Skipping camera {key} with alias"
                                f" {configs['cameras'][key].get('alias', None)} because args.cameras != all",
                                title="Connection")
    elif args.mode == "regular":
        camera = Camera(serial_number="40451366", config=configs['cameras'])
        camera.start_streaming()
        cameras.append(camera)
    elif args.mode == "simulator":
        camera = CameraSimulator("simulator", configs['cameras'])
        camera.start_streaming()
        cameras.append(camera)
    threading.Thread(target=image_retrieval_thread, args=(cameras,), daemon=True).start()
    
    # Start the processing thread
    threading.Thread(target=get_frames, args=(configs,), daemon=True).start()
    signal.signal(signal.SIGINT, signal_handler)
    print(f"\n✅ Flask server running: http://{args.ip}:{args.port}/\n")
    app.run(host=args.ip, port=int(args.port), debug=True, use_reloader=False, threaded=True)    


