import cv2
import numpy as np
from collections import deque
import os

# Parámetros
VIDEO_PATH = '/opt/MVS/bin/Temp/Data/fotos/MV-CL042-91GC+DA4274329/Video_20250606170025202.avi'
OUTPUT_DIR = '/mnt/dataset/test_output'
PRE_LINES = 50         # Líneas negras antes del cambio
POST_LINES = 50        # Líneas negras después del cambio
THRESHOLD = 25         # Umbral de diferencia
MIN_DIFF = 100000      # Diferencia mínima para considerar "calcetín"

os.makedirs(OUTPUT_DIR, exist_ok=True)

cap = cv2.VideoCapture(VIDEO_PATH)
if not cap.isOpened():
    raise Exception(f"No se pudo abrir el video: {VIDEO_PATH}")

# Leer frame por frame
recording = False
post_counter = 0
buffer_lines = deque(maxlen=PRE_LINES)
saved_image = []

frame_count = 0
while True:
    ret, frame = cap.read()
    if not ret:
        break

    # Simula el escaneo línea por línea de una cámara en línea
    height = frame.shape[0]
    for i in range(height):
        line = frame[i:i+1, :, :]  # Línea actual (1 píxel de alto)

        # Convertimos a gris y comparamos con la media del fondo
        gray_line = cv2.cvtColor(line, cv2.COLOR_BGR2GRAY)
        mean_intensity = np.mean(gray_line)
        diff = np.sum(np.abs(gray_line - np.mean(gray_line)))

        buffer_lines.append(line.copy())

        if diff > MIN_DIFF:
            if not recording:
                print(f"➡️ Inicio de detección en línea {i} del frame {frame_count}")
                saved_image = list(buffer_lines)
                recording = True
                post_counter = 0
            saved_image.append(line.copy())
            post_counter = 0
        elif recording:
            saved_image.append(line.copy())
            post_counter += 1
            if post_counter >= POST_LINES:
                print(f"✅ Fin de la detección en línea {i} del frame {frame_count}, guardando imagen")

                full_img = np.vstack(saved_image)
                out_path = os.path.join(OUTPUT_DIR, f"detected_{frame_count}.png")
                cv2.imwrite(out_path, full_img)

                recording = False
                saved_image = []

    frame_count += 1

cap.release()
print("🚀 Análisis finalizado")



# This script captures images from multiple cameras based on serial numbers,
# detects object entry and exit events, and saves the captured images to a specified directory.
# It uses threading to handle multiple cameras simultaneously and processes images in real-time.
# Ensure you have the necessary libraries installed and the camera SDK properly set up.
# Adjust the TARGET_SERIALS and OUTPUT_DIR variables as needed.
# Note: This script assumes you have the MvCameraControl_class.py file in the same directory.
# Make sure to run this script in an environment where the MVS SDK is installed and configured.
# Also, ensure that the cameras are connected and recognized by the system.
# The script is designed to run indefinitely until interrupted by the user.
# Make sure to run this script with appropriate permissions to access the cameras.
# The script captures images in a loop, detects significant changes in the image data,
# and saves the images when an object is detected entering or exiting the camera's field of view.
# The saved images are named with the camera serial number and a timestamp to avoid conflicts.
# The script uses OpenCV for image processing and saving.
# Adjust the exposure time and other camera settings as needed for your specific use case.
# Ensure that the output directory exists and has write permissions.
# The script uses a deque to maintain a buffer of recent frames for processing.
# The script is designed to be robust against camera disconnections and errors,
# and will attempt to recover gracefully.
# The script is structured to be easily extendable for additional functionality,
# such as adding more cameras or changing the processing logic.
