import os
import numpy as np
from collections import deque
import datetime
import threading
import time
import sys
import ctypes
from ctypes import cdll
import cv2
import signal

# Directory where result images will be saved
OUTPUT_DIR = "/mnt/dataset/test_output"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# List of GigE camera serial numbers (line-scan or area-scan)
TARGET_SERIALS = ["*********", "*********"]

# Human-readable location names for each camera line serial
CAMERA_LOCATIONS = {
    "*********": "up-left",
    "*********": "down-right"
}

# Number of frames to save before and after an event (sock detection)
# Adjust these values based on application and expected event size
PRE_FRAMES = 20
POST_FRAMES = 40

# Threshold for sum of grayscale differences to detect a sock
MIN_DIFF = 65000000 #down-right: 7 391 910  up-left: 58 431 858

# -------------------------------------------------------------------------------
# C types structures used by the MVS SDK binding
# -------------------------------------------------------------------------------
class MVCC_ENUMVALUE(ctypes.Structure):
    _fields_ = [
        ("nCurValue", ctypes.c_uint),
        ("nSupportedNum", ctypes.c_uint),
        ("nSupportValue", ctypes.c_uint * 16),
    ]

class MVCC_INTVALUE(ctypes.Structure):
    _fields_ = [
        ("nCurValue", ctypes.c_uint),
        ("nMax", ctypes.c_uint),
        ("nMin", ctypes.c_uint),
        ("nInc", ctypes.c_uint),
    ]

class MVCC_FLOATVALUE(ctypes.Structure):
    _fields_ = [
        ("fCurValue", ctypes.c_float),
        ("fMax", ctypes.c_float),
        ("fMin", ctypes.c_float),
        ("fInc", ctypes.c_float),
    ]

# -------------------------------------------------------------------------------
# Import Python wrapper for the MVS SDK. Adjust path if necessary.
# -------------------------------------------------------------------------------
sys.path.append('/opt/MVS/Samples/64/Python/MvImport')
from MvCameraControl_class import (
    MvCamera,
    MV_CC_DEVICE_INFO_LIST,
    MV_GIGE_DEVICE,
    MV_FRAME_OUT_INFO_EX,
    PixelType_Gvsp_RGB8_Packed,
)

# Global flag indicating whether the program should continue running
running = True

def shutdown(signum, frame):
    """
    Signal handler for SIGINT and SIGTERM. Sets the global flag to False
    so capture loops and the main thread can exit cleanly.
    """
    global running
    print("\n🛑 Signal received, initiating clean shutdown of cameras…")
    running = False

# Register signal handlers for Ctrl+C (SIGINT) and kill (SIGTERM)
signal.signal(signal.SIGINT, shutdown)
signal.signal(signal.SIGTERM, shutdown)

def open_camera_by_serial(serial):
    """
    Searches for the GigE camera with the given `serial`, opens it, and configures:
      - PixelFormat = RGB8_Packed
      - ExposureTime = 2000 µs (default)
      - TriggerMode = 0 (continuous)
    Does not force Width/Height to 1; uses the camera's default resolution.
    Prints using the camera's human-readable location name.
    """
    display_name = CAMERA_LOCATIONS.get(serial, serial)
    sdk_lib = cdll.LoadLibrary("libMvCameraControl.so")
    device_list = MV_CC_DEVICE_INFO_LIST()
    sdk_lib.MV_CC_EnumDevices(MV_GIGE_DEVICE, ctypes.byref(device_list))

    print("Connected serials found:")
    for i in range(device_list.nDeviceNum):
        dev_info = device_list.pDeviceInfo[i].contents
        if dev_info.nTLayerType == MV_GIGE_DEVICE:
            gige_info = dev_info.SpecialInfo.stGigEInfo
            found_serial = bytes(gige_info.chSerialNumber).decode("ascii").strip('\0')
            print(f" - {found_serial}")

            if found_serial == serial:
                cam = MvCamera()

                # 1) Create handle
                ret = cam.MV_CC_CreateHandle(dev_info)
                print(f"[{display_name}] CreateHandle returned {ret}")
                if ret != 0:
                    raise Exception(f"Error creating handle for camera {serial} (code {ret})")

                # 2) Open device
                ret = cam.MV_CC_OpenDevice()
                print(f"[{display_name}] OpenDevice returned {ret}")
                if ret != 0:
                    raise Exception(f"Error opening camera {serial} (code {ret})")

                # -------------------------------------------------------------------
                # Basic configuration: pixel format and exposure time.
                # Do not force Width/Height to 1 if not supported by the camera.
                # -------------------------------------------------------------------

                # Set PixelFormat = RGB8_Packed
                cam.MV_CC_SetEnumValue("PixelFormat", PixelType_Gvsp_RGB8_Packed)

                # Set continuous mode (TriggerMode = 0)
                cam.MV_CC_SetEnumValue("TriggerMode", 0)

                # Set ExposureTime = 500 µs
                cam.MV_CC_SetFloatValue("ExposureTime", 500.0)

                # Pause briefly to let settings take effect
                time.sleep(0.1)

                # -------------------------------------------------------------------
                # Read current values of Width, Height, ExposureTime, PixelFormat, TriggerMode.
                # -------------------------------------------------------------------
                width_val  = MVCC_INTVALUE()
                height_val = MVCC_INTVALUE()
                expo_val   = MVCC_FLOATVALUE()
                pix_val    = MVCC_ENUMVALUE()
                trig_val   = MVCC_ENUMVALUE()

                cam.MV_CC_GetIntValue("Width", width_val)
                cam.MV_CC_GetIntValue("Height", height_val)
                cam.MV_CC_GetFloatValue("ExposureTime", expo_val)
                cam.MV_CC_GetEnumValue("PixelFormat", pix_val)
                cam.MV_CC_GetEnumValue("TriggerMode", trig_val)

                print(f"[{display_name}] Settings applied:")
                print(f"  Width:       {width_val.nCurValue}")
                print(f"  Height:      {height_val.nCurValue}")
                print(f"  Exposure:    {expo_val.fCurValue:.2f} µs")
                print(f"  PixelFormat: {pix_val.nCurValue} (RGB8_Packed)")
                print(f"  TriggerMode: {trig_val.nCurValue} (Continuous)")

                return cam

    raise Exception(f"Camera with serial {serial} not found")

def close_camera(cam):
    """
    Stops grabbing and closes the camera cleanly.
    Ignores any non-zero return codes or exceptions so that repeated calls
    do not produce error messages if the camera is already stopped/closed.
    """
    try:
        cam.MV_CC_StopGrabbing()
    except:
        pass

    try:
        cam.MV_CC_CloseDevice()
    except:
        pass

    try:
        cam.MV_CC_DestroyHandle()
    except:
        pass

def start_grab_loop(cam, serial):
    """
    Capture loop for frames (area-scan or line-scan, depending on camera).
    - Each GetOneFrameTimeout call returns a frame of height×width.
    - Calculates diff = sum of |pixel_gray - mean_gray| to detect changes.
    - Maintains PRE_FRAMES before the event and POST_FRAMES after.
    - When an event finishes, stacks all lines to produce full_img, computes
      its length in pixels, and includes that in the filename (using serial).
    - Prints a heartbeat message every 5 seconds to confirm data reception,
      using camera's human-readable location name in console prints.
    The loop respects the global `running` flag; when it becomes False, the
    loop exits and the thread terminates cleanly.
    """
    display_name = CAMERA_LOCATIONS.get(serial, serial)
    ret = cam.MV_CC_StartGrabbing()
    if ret != 0:
        print(f"[{display_name}] ❌ Failed to start grabbing (code {ret})")
        return

    print(f"🎥 Capturing from camera {display_name}...")

    recording = False
    post_counter = 0
    buffer_frames = deque(maxlen=PRE_FRAMES)
    saved_frames = []
    event_count = 0
    frame_counter = 0
    last_heartbeat_time = time.time()

    # If you know pixel→mm conversion (vertical), set it here; otherwise, leave as None.
    PIXEL_TO_MM = 0.25  # Example: 1 px = 0.25 mm

    # Reserve a large buffer for a high-resolution frame (e.g., 4096×2800×3 ≈ 34 MB)
    buf_size = 50 * 1024 * 1024

    while running:
        frame_info = MV_FRAME_OUT_INFO_EX()
        data_buf = (ctypes.c_ubyte * buf_size)()

        # The wrapper automatically applies byref(frame_info)
        ret = cam.MV_CC_GetOneFrameTimeout(data_buf, buf_size, frame_info, 1000)
        if ret != 0:
            # Every 200 failed attempts, print the return code for debugging
            frame_counter += 1
            if frame_counter % 200 == 0:
                print(f"[{display_name}] ⚠️ GetOneFrameTimeout ret={ret} on attempt #{frame_counter}")
            time.sleep(0.005)
            continue

        # Successfully received a frame or line
        height = frame_info.nHeight
        width  = frame_info.nWidth
        pixel_type = frame_info.enPixelType
        frame_len  = frame_info.nFrameLen

        raw = np.frombuffer(data_buf, dtype=np.uint8)[:frame_len]
        if pixel_type == PixelType_Gvsp_RGB8_Packed:
            try:
                frame = raw.reshape((height, width, 3))
            except Exception as e:
                print(f"[{display_name}] ❌ Reshape error ({height},{width},3): {e}")
                continue
        else:
            print(f"[{display_name}] ❌ Unsupported pixel format: {pixel_type}")
            continue

        # Convert to grayscale and calculate diff
        gray = np.mean(frame, axis=2).astype(np.uint8)
        diff = np.sum(np.abs(gray - np.mean(gray)))

        frame_counter += 1
        now = time.time()
        # Print heartbeat every 1 seconds
        if now - last_heartbeat_time >= 1.0:
            print(f"[{display_name}] ◼︎ Received {frame_counter} frames. Last diff={diff:.0f}")
            last_heartbeat_time = now

        # Add current frame to the circular buffer of PRE_FRAMES
        buffer_frames.append(frame.copy())

        if diff > MIN_DIFF:
            # Start or continue the event
            if not recording:
                print(f"[{display_name}] ➡️ Detected event start (diff={diff})")
                saved_frames = list(buffer_frames)
                recording = True
                post_counter = 0

            saved_frames.append(frame.copy())
            post_counter = 0

        elif recording:
            # In POST phase: continue saving until POST_FRAMES reached
            saved_frames.append(frame.copy())
            post_counter += 1
            if post_counter >= POST_FRAMES:
                # Event finished: stack lines and save image
                print(f"[{display_name}] ✅ Detected event end, preparing image")

                # 1) Stack all captured lines (num_lines × width × 3)
                full_img = np.vstack(saved_frames)

                # 2) Compute length in pixels (number of rows)
                length_px = full_img.shape[0]
                # 3) Convert to mm if PIXEL_TO_MM is defined
                if PIXEL_TO_MM is not None:
                    length_mm = int(length_px * PIXEL_TO_MM)
                    length_str = f"{length_px}px_{length_mm}mm"
                else:
                    length_str = f"{length_px}px"

                # 4) Timestamp for filename
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

                # 5) Generate filename with camera serial, timestamp, length, and event count
                filename = f"{serial}_event_{timestamp}_len{length_str}_{event_count}.png"
                out_path = os.path.join(OUTPUT_DIR, filename)

                # 6) Write PNG to disk
                success = cv2.imwrite(out_path, full_img)
                if not success:
                    print(f"[{display_name}] ❌ Failed to write {out_path}")
                else:
                    print(f"[{display_name}] 📁 Saved: {filename}")

                event_count += 1
                recording = False
                saved_frames = []

        # Small pause to avoid maxing out CPU
        time.sleep(0.001)

    # If we exit the loop because running == False, close the camera
    print(f"[{display_name}] 🚧 Stopping capture loop.")
    close_camera(cam)

def main():
    cameras = {}
    threads = []

    try:
        # Initialize each camera from TARGET_SERIALS
        for serial in TARGET_SERIALS:
            print(f"🔌 Initializing camera {CAMERA_LOCATIONS.get(serial, serial)}")
            try:
                cam = open_camera_by_serial(serial)
                cameras[serial] = cam
            except Exception as e:
                print(f"❌ Could not initialize {serial}: {e}")

        if not cameras:
            print("❌ Could not open any cameras. Exiting.")
            return

        # Launch a capture thread for each opened camera
        for serial, cam in cameras.items():
            t = threading.Thread(target=start_grab_loop, args=(cam, serial))
            t.daemon = True
            t.start()
            threads.append(t)

        # Main thread waits until `running` becomes False (Ctrl+C or SIGTERM)
        while running:
            time.sleep(1)

    finally:
        # When running is False, close all cameras
        print("\n🔒 Closing all open cameras…")
        for serial, cam in cameras.items():
            print(f"🔧 Closing camera {CAMERA_LOCATIONS.get(serial, serial)}")
            close_camera(cam)
        print("✅ All cameras have been closed cleanly.")

if __name__ == "__main__":
    main()
