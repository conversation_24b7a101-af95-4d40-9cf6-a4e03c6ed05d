from PIL import Image
import os
import cv2
import numpy as np
from backend.src.motion_detection.motion_detection import Y<PERSON><PERSON>_Predictor


def detecte_bounding_box(side: str, model: YOLO_Predictor):
    if side == "right":
        pass
    elif side == "left":
        pass



def mirror_image(image):
    try:
        # Flip the image horizontally using OpenCV
        mirrored_img = cv2.flip(image, 1)
        return mirrored_img
    except Exception as e:
        return f"Error while mirroring image: {e}"

def get_cropped_segment_brightness(image, detector_range):
    try:
        # Crop the segment using detector_range values: from the left column to the right column.
        cropped_img = image[:, detector_range[0]:detector_range[1]]
        # Convert the cropped segment to grayscale.
        grayscale_img = cv2.cvtColor(cropped_img, cv2.COLOR_BGR2GRAY)
        # Compute the average brightness.
        brightness = np.exp(np.mean(np.log(grayscale_img + 1))) - 1
        return brightness
    except Exception as e:
        return f"Error while calculating brightness: {e}"

def detect_stocking_bas_bottom(image, threshold=1, logger=None): 
    try:
        #mirrored_img = mirror_image(image)
        brightness = get_cropped_segment_brightness(image, (0, 50))
        # print(brightness)
        return brightness > threshold
    except Exception as e:
        print(f"Error in detect_stocking: {str(e)}")

def detect_stocking_motion_detection(image, model, logger=None):
    pass


if __name__ == "__main__":        
    # Example usage
    base_path = "/mnt/data/projects/defis-medical/individual_frames/testing/Top"
    path1 = f"{base_path}/sample_top.jpg"
    images = [path1]
    for image in images:
        mirrored_img = mirror_image(image)
        detector_range = (0, 300)
        cropped_brightness = get_cropped_segment_brightness(mirrored_img, detector_range)
        print(f"Brightness of the selected pixels: {cropped_brightness}")
        print(f"Brightness of the selected pixels: {cropped_brightness}")
