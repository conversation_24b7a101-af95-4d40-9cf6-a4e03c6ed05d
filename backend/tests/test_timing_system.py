#!/usr/bin/env python3
"""
Test script to verify the timing system works correctly.
"""

import sys
import os
import time

# Add the project root directory to the path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

def test_performance_timer():
    """Test the PerformanceTimer context manager."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import (
            PerformanceTimer,
            timing_data,
            log_timing_summary
        )
        
        # Clear any existing timing data
        timing_data.clear()
        
        sock_id = 12345
        camera_alias = "test_camera"
        
        print("Testing PerformanceTimer...")
        
        # Test basic timing
        with PerformanceTimer("test_operation", sock_id, camera_alias):
            time.sleep(0.1)  # Simulate work
        
        # Test another operation
        with PerformanceTimer("another_operation", sock_id, camera_alias):
            time.sleep(0.05)  # Simulate work
        
        # Verify timing data was stored
        assert sock_id in timing_data
        assert f"{camera_alias}_test_operation" in timing_data[sock_id]
        assert f"{camera_alias}_another_operation" in timing_data[sock_id]
        
        # Check that timing values are reasonable
        test_time = timing_data[sock_id][f"{camera_alias}_test_operation"]
        another_time = timing_data[sock_id][f"{camera_alias}_another_operation"]
        
        assert 0.09 < test_time < 0.15  # Should be around 0.1 seconds
        assert 0.04 < another_time < 0.08  # Should be around 0.05 seconds
        
        print("✓ PerformanceTimer working correctly")
        
        # Test timing summary
        print("\nTesting timing summary...")
        log_timing_summary(sock_id)
        
        # Verify data was cleaned up
        assert sock_id not in timing_data
        print("✓ Timing summary and cleanup working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ PerformanceTimer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_timing_without_sock_id():
    """Test timing without sock_id and camera_alias."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import PerformanceTimer
        
        print("\nTesting PerformanceTimer without sock_id...")
        
        # Test timing without sock_id (should just log)
        with PerformanceTimer("general_operation"):
            time.sleep(0.02)
        
        print("✓ General timing (without sock_id) working correctly")
        return True
        
    except Exception as e:
        print(f"❌ General timing test failed: {e}")
        return False


def simulate_analysis_timing():
    """Simulate the timing that would occur during analysis."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import (
            PerformanceTimer,
            timing_data,
            log_timing_summary
        )
        
        # Clear any existing timing data
        timing_data.clear()
        
        sock_id = 67890
        camera_alias = "camera_top"
        
        print("\nSimulating complete analysis timing...")
        
        # Simulate the timing sequence that would occur during analysis
        with PerformanceTimer("mask_extraction", sock_id, camera_alias):
            time.sleep(0.01)  # Simulate mask extraction
        
        with PerformanceTimer("color_extraction", sock_id, camera_alias):
            time.sleep(0.005)  # Simulate color extraction
        
        with PerformanceTimer("defect_detection", sock_id, camera_alias):
            time.sleep(0.1)  # Simulate defect detection (usually the slowest)
        
        with PerformanceTimer("bbox_processing", sock_id, camera_alias):
            time.sleep(0.02)  # Simulate bounding box processing
        
        with PerformanceTimer("result_accumulation", sock_id, camera_alias):
            time.sleep(0.03)  # Simulate result accumulation
        
        with PerformanceTimer("save_source_images", sock_id, camera_alias):
            time.sleep(0.015)  # Simulate image saving
        
        with PerformanceTimer("save_json_result", sock_id, camera_alias):
            time.sleep(0.002)  # Simulate JSON saving
        
        # Log the complete timing summary
        print("\nComplete analysis timing summary:")
        log_timing_summary(sock_id)
        
        print("✓ Analysis timing simulation completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Analysis timing simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("Testing Timing System")
    print("=" * 40)
    
    success = True
    
    if not test_performance_timer():
        success = False
    
    if not test_timing_without_sock_id():
        success = False
    
    if not simulate_analysis_timing():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All timing tests passed!")
        print("\nThe timing system is working correctly and will help identify:")
        print("- Which operations take the most time")
        print("- Performance bottlenecks in the analysis pipeline")
        print("- Optimization opportunities")
    else:
        print("❌ Some timing tests failed")
        sys.exit(1)
