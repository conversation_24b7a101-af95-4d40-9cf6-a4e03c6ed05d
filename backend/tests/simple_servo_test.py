#!/usr/bin/env python3
"""
Simple test to verify the servo control functions work.
"""

import sys
import os

# Add the project root directory to the path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

def test_imports():
    """Test that we can import the servo control functions."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import (
            initialize_servo_for_sock_id,
            process_analysis_result,
            make_final_servo_decision,
            return_servo_to_middle,
            analysis_results,
            servo_states,
            sock_events
        )
        print("Successfully imported servo control functions")
        return True
    except ImportError as e:
        print(f"ailed to import servo control functions: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without mocking."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import (
            process_analysis_result,
            analysis_results,
            servo_states,
            sock_events
        )
        
        # Clear global state
        analysis_results.clear()
        servo_states.clear()
        sock_events.clear()
        
        sock_id = 12345
        
        # Test storing analysis results
        process_analysis_result(sock_id, "camera_top", True)
        
        # Verify result was stored
        assert sock_id in analysis_results
        assert 'camera_top' in analysis_results[sock_id]
        assert analysis_results[sock_id]['camera_top']['defects_found'] == True
        
        print("Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Running simple servo control tests...")
    print("=" * 40)
    
    success = True
    
    if not test_imports():
        success = False
    
    if not test_basic_functionality():
        success = False
    
    print("=" * 40)
    if success:
        print("All simple tests passed!")
    else:
        print("Some tests failed")
        sys.exit(1)
