#!/usr/bin/env python3
"""
Test suite for the servo control system.
Tests the combined top/bottom camera analysis and servo control logic.
"""

import unittest
import time
import threading
from unittest.mock import patch, MagicMock
import sys
import os

# Add the project root directory to the path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

from backend.src.stockings_analysis.stocking_analysis import (
    initialize_servo_for_sock_id,
    process_analysis_result,
    make_final_servo_decision,
    return_servo_to_middle,
    analysis_results,
    servo_states,
    sock_events
)


class TestServoControl(unittest.TestCase):
    """Test cases for servo control system."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Clear global state
        analysis_results.clear()
        servo_states.clear()
        sock_events.clear()
    
    def tearDown(self):
        """Clean up after each test method."""
        # Clear global state
        analysis_results.clear()
        servo_states.clear()
        sock_events.clear()
    
    @patch('backend.src.stockings_analysis.stocking_analysis.send_command')
    @patch('backend.src.stockings_analysis.stocking_analysis.logger')
    def test_initialize_servo_for_sock_id(self, mock_logger, mock_send_command):
        """Test servo initialization for new sock_id."""
        sock_id = 12345
        
        initialize_servo_for_sock_id(sock_id)
        
        # Verify servo command was sent
        mock_send_command.assert_called_once_with('m')
        
        # Verify servo state was set
        self.assertEqual(servo_states[sock_id], 'm')
        
        # Verify logging
        mock_logger.info.assert_called_with(f"Servo initialized to middle position for sock_id={sock_id}")
    
    @patch('stockings_analysis.stocking_analysis.make_final_servo_decision')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_process_analysis_result_single_camera(self, mock_logger, mock_make_decision):
        """Test processing analysis result for single camera."""
        sock_id = 12345
        camera_alias = "camera_top"
        defects_found = True
        
        process_analysis_result(sock_id, camera_alias, defects_found)
        
        # Verify result was stored
        self.assertIn(sock_id, analysis_results)
        self.assertIn('camera_top', analysis_results[sock_id])
        self.assertEqual(analysis_results[sock_id]['camera_top']['defects_found'], True)
        
        # Verify final decision was not made (only one camera)
        mock_make_decision.assert_not_called()
    
    @patch('stockings_analysis.stocking_analysis.make_final_servo_decision')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_process_analysis_result_both_cameras(self, mock_logger, mock_make_decision):
        """Test processing analysis result when both cameras complete."""
        sock_id = 12345
        
        # Process top camera result
        process_analysis_result(sock_id, "camera_top", True)
        mock_make_decision.assert_not_called()
        
        # Process bottom camera result
        process_analysis_result(sock_id, "camera_bottom", False)
        
        # Verify final decision was made
        mock_make_decision.assert_called_once_with(sock_id)
    
    @patch('stockings_analysis.stocking_analysis.send_command')
    @patch('stockings_analysis.stocking_analysis.threading.Timer')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_make_final_servo_decision_defects_found(self, mock_logger, mock_timer, mock_send_command):
        """Test final servo decision when defects are found."""
        sock_id = 12345
        
        # Set up analysis results with defects
        analysis_results[sock_id] = {
            'camera_top': {'defects_found': True, 'timestamp': time.time()},
            'camera_bottom': {'defects_found': False, 'timestamp': time.time()}
        }
        
        make_final_servo_decision(sock_id)
        
        # Verify reject command was sent
        mock_send_command.assert_called_once_with('r')
        
        # Verify servo state was set
        self.assertEqual(servo_states[sock_id], 'r')
        
        # Verify timer was started for return to middle
        mock_timer.assert_called_once_with(2.0, return_servo_to_middle, args=[sock_id])
        mock_timer.return_value.start.assert_called_once()
    
    @patch('stockings_analysis.stocking_analysis.send_command')
    @patch('stockings_analysis.stocking_analysis.threading.Timer')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_make_final_servo_decision_no_defects(self, mock_logger, mock_timer, mock_send_command):
        """Test final servo decision when no defects are found."""
        sock_id = 12345
        
        # Set up analysis results without defects
        analysis_results[sock_id] = {
            'camera_top': {'defects_found': False, 'timestamp': time.time()},
            'camera_bottom': {'defects_found': False, 'timestamp': time.time()}
        }
        
        make_final_servo_decision(sock_id)
        
        # Verify accept command was sent
        mock_send_command.assert_called_once_with('l')
        
        # Verify servo state was set
        self.assertEqual(servo_states[sock_id], 'l')
        
        # Verify timer was started for return to middle
        mock_timer.assert_called_once_with(2.0, return_servo_to_middle, args=[sock_id])
        mock_timer.return_value.start.assert_called_once()
    
    @patch('stockings_analysis.stocking_analysis.send_command')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_return_servo_to_middle(self, mock_logger, mock_send_command):
        """Test returning servo to middle position and cleanup."""
        sock_id = 12345
        
        # Set up initial state
        analysis_results[sock_id] = {'camera_top': {'defects_found': True}}
        servo_states[sock_id] = 'r'
        sock_events[sock_id] = {'top': time.time()}
        
        return_servo_to_middle(sock_id)
        
        # Verify middle command was sent
        mock_send_command.assert_called_once_with('m')
        
        # Verify cleanup was performed
        self.assertNotIn(sock_id, analysis_results)
        self.assertNotIn(sock_id, servo_states)
        self.assertNotIn(sock_id, sock_events)
    
    @patch('stockings_analysis.stocking_analysis.make_final_servo_decision')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_complete_workflow_with_defects(self, mock_logger, mock_make_decision):
        """Test complete workflow when defects are found in one camera."""
        sock_id = 12345
        
        # Simulate top camera finding defects
        process_analysis_result(sock_id, "camera_top", True)
        
        # Simulate bottom camera finding no defects
        process_analysis_result(sock_id, "camera_bottom", False)
        
        # Verify both results were stored
        self.assertEqual(len(analysis_results[sock_id]), 2)
        self.assertTrue(analysis_results[sock_id]['camera_top']['defects_found'])
        self.assertFalse(analysis_results[sock_id]['camera_bottom']['defects_found'])
        
        # Verify final decision was triggered
        mock_make_decision.assert_called_once_with(sock_id)
    
    @patch('stockings_analysis.stocking_analysis.make_final_servo_decision')
    @patch('stockings_analysis.stocking_analysis.logger')
    def test_complete_workflow_no_defects(self, mock_logger, mock_make_decision):
        """Test complete workflow when no defects are found."""
        sock_id = 12345
        
        # Simulate both cameras finding no defects
        process_analysis_result(sock_id, "camera_top", False)
        process_analysis_result(sock_id, "camera_bottom", False)
        
        # Verify both results were stored
        self.assertEqual(len(analysis_results[sock_id]), 2)
        self.assertFalse(analysis_results[sock_id]['camera_top']['defects_found'])
        self.assertFalse(analysis_results[sock_id]['camera_bottom']['defects_found'])
        
        # Verify final decision was triggered
        mock_make_decision.assert_called_once_with(sock_id)


if __name__ == '__main__':
    unittest.main()
