#!/usr/bin/env python3
"""
Integration test for the servo control system.
This script simulates the complete workflow with mock cameras.
"""

import time
import sys
import os
from unittest.mock import patch, MagicMock

# Add the project root directory to the path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

from backend.src.stockings_analysis.stocking_analysis import (
    initialize_servo_for_sock_id,
    process_analysis_result,
    analysis_results,
    servo_states,
    sock_events
)


def simulate_defect_scenario():
    """Simulate a scenario where defects are found."""
    print("=== Simulating Defect Scenario ===")
    
    sock_id = 12345
    
    # Clear global state
    analysis_results.clear()
    servo_states.clear()
    sock_events.clear()
    
    with patch('stockings_analysis.stocking_analysis.send_command') as mock_send_command, \
         patch('stockings_analysis.stocking_analysis.logger') as mock_logger:
        
        print(f"1. Initializing servo for sock_id={sock_id}")
        initialize_servo_for_sock_id(sock_id)
        
        # Verify initial servo command
        assert mock_send_command.call_count == 1
        assert mock_send_command.call_args[0][0] == 'm'
        print("   ✓ Servo initialized to middle position")
        
        print("2. Processing top camera analysis (defects found)")
        process_analysis_result(sock_id, "camera_top", True)
        print("   ✓ Top camera result stored")
        
        print("3. Processing bottom camera analysis (no defects)")
        process_analysis_result(sock_id, "camera_bottom", False)
        
        # Wait a moment for any threading operations
        time.sleep(0.1)
        
        # Verify final servo command was sent
        assert mock_send_command.call_count >= 2
        final_command = mock_send_command.call_args[0][0]
        assert final_command == 'r'
        print("   ✓ Final decision: REJECT (servo moved to 'r')")
        
        print("4. Waiting for servo to return to middle...")
        # In real scenario, this would happen after 2 seconds
        # For testing, we'll simulate it immediately
        time.sleep(0.1)
        
        print("✓ Defect scenario completed successfully")


def simulate_no_defect_scenario():
    """Simulate a scenario where no defects are found."""
    print("\n=== Simulating No Defect Scenario ===")
    
    sock_id = 67890
    
    # Clear global state
    analysis_results.clear()
    servo_states.clear()
    sock_events.clear()
    
    with patch('stockings_analysis.stocking_analysis.send_command') as mock_send_command, \
         patch('stockings_analysis.stocking_analysis.logger') as mock_logger:
        
        print(f"1. Initializing servo for sock_id={sock_id}")
        initialize_servo_for_sock_id(sock_id)
        
        # Verify initial servo command
        assert mock_send_command.call_count == 1
        assert mock_send_command.call_args[0][0] == 'm'
        print("   ✓ Servo initialized to middle position")
        
        print("2. Processing top camera analysis (no defects)")
        process_analysis_result(sock_id, "camera_top", False)
        print("   ✓ Top camera result stored")
        
        print("3. Processing bottom camera analysis (no defects)")
        process_analysis_result(sock_id, "camera_bottom", False)
        
        # Wait a moment for any threading operations
        time.sleep(0.1)
        
        # Verify final servo command was sent
        assert mock_send_command.call_count >= 2
        final_command = mock_send_command.call_args[0][0]
        assert final_command == 'l'
        print("   ✓ Final decision: ACCEPT (servo moved to 'l')")
        
        print("4. Waiting for servo to return to middle...")
        # In real scenario, this would happen after 2 seconds
        time.sleep(0.1)
        
        print("✓ No defect scenario completed successfully")


def simulate_mixed_defect_scenario():
    """Simulate a scenario where both cameras find defects."""
    print("\n=== Simulating Mixed Defect Scenario ===")
    
    sock_id = 11111
    
    # Clear global state
    analysis_results.clear()
    servo_states.clear()
    sock_events.clear()
    
    with patch('stockings_analysis.stocking_analysis.send_command') as mock_send_command, \
         patch('stockings_analysis.stocking_analysis.logger') as mock_logger:
        
        print(f"1. Initializing servo for sock_id={sock_id}")
        initialize_servo_for_sock_id(sock_id)
        
        print("2. Processing top camera analysis (defects found)")
        process_analysis_result(sock_id, "camera_top", True)
        print("   ✓ Top camera result stored")
        
        print("3. Processing bottom camera analysis (defects found)")
        process_analysis_result(sock_id, "camera_bottom", True)
        
        # Wait a moment for any threading operations
        time.sleep(0.1)
        
        # Verify final servo command was sent
        assert mock_send_command.call_count >= 2
        final_command = mock_send_command.call_args[0][0]
        assert final_command == 'r'
        print("   ✓ Final decision: REJECT (servo moved to 'r')")
        
        print("✓ Mixed defect scenario completed successfully")


def test_timing_behavior():
    """Test the timing behavior of the servo system."""
    print("\n=== Testing Timing Behavior ===")
    
    sock_id = 22222
    
    # Clear global state
    analysis_results.clear()
    servo_states.clear()
    sock_events.clear()
    
    with patch('stockings_analysis.stocking_analysis.send_command') as mock_send_command, \
         patch('stockings_analysis.stocking_analysis.logger') as mock_logger, \
         patch('stockings_analysis.stocking_analysis.threading.Timer') as mock_timer:
        
        print("1. Testing that servo stays in middle until both cameras complete")
        initialize_servo_for_sock_id(sock_id)
        
        # Process only top camera
        process_analysis_result(sock_id, "camera_top", False)
        
        # Verify no final decision was made yet
        assert mock_send_command.call_count == 1  # Only the initial 'm' command
        print("   ✓ Servo remains in middle with only one camera result")
        
        # Process bottom camera
        process_analysis_result(sock_id, "camera_bottom", False)
        
        # Now final decision should be made
        assert mock_send_command.call_count >= 2
        print("   ✓ Final decision made after both cameras complete")
        
        # Verify timer was set for return to middle
        mock_timer.assert_called_with(2.0, mock_logger.return_value, args=[sock_id])
        print("   ✓ Timer set for return to middle position")
        
        print("✓ Timing behavior test completed successfully")


if __name__ == '__main__':
    print("Starting Servo Control Integration Tests")
    print("=" * 50)
    
    try:
        simulate_defect_scenario()
        simulate_no_defect_scenario()
        simulate_mixed_defect_scenario()
        test_timing_behavior()
        
        print("\n" + "=" * 50)
        print("All integration tests passed successfully")
        print("\nThe servo control system is working correctly:")
        print("- Servo initializes to middle position for new objects")
        print("- Servo waits for both cameras to complete analysis")
        print("- Servo makes correct decision based on combined results")
        print("- Servo returns to middle after delay for object to fall")
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
