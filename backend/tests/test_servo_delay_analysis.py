#!/usr/bin/env python3
"""
Test script to verify the servo delay analysis system works correctly.
This will help identify bottlenecks in the servo response time.
"""

import sys
import os
import time

# Add the project root directory to the path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

def test_servo_delay_analysis():
    """Test the servo delay analysis system."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import (
            record_servo_milestone,
            log_servo_delay_analysis,
            servo_timing_milestones
        )
        
        # Clear any existing timing data
        servo_timing_milestones.clear()
        
        sock_id = 12345
        
        print("Testing servo delay analysis system...")
        
        # Simulate the complete timeline from event detection to servo movement
        print("1. Simulating event detection...")
        record_servo_milestone(sock_id, "event_detected", "camera_top")
        time.sleep(0.1)  # Simulate some processing time
        
        print("2. Simulating servo initialization...")
        record_servo_milestone(sock_id, "servo_init_start", "camera_top")
        time.sleep(0.05)  # Simulate servo init time
        record_servo_milestone(sock_id, "servo_init_complete", "camera_top")
        
        print("3. Simulating event end...")
        time.sleep(0.2)  # Simulate event duration
        record_servo_milestone(sock_id, "event_end", "camera_top")
        
        print("4. Simulating analysis phase...")
        record_servo_milestone(sock_id, "analysis_start", "camera_top")
        time.sleep(0.3)  # Simulate analysis time (this might be slow)
        record_servo_milestone(sock_id, "analysis_complete", "camera_top")
        
        print("5. Simulating bottom camera...")
        record_servo_milestone(sock_id, "event_detected", "camera_bottom")
        time.sleep(0.15)  # Simulate bottom camera event
        record_servo_milestone(sock_id, "event_end", "camera_bottom")
        record_servo_milestone(sock_id, "analysis_start", "camera_bottom")
        time.sleep(0.25)  # Simulate bottom camera analysis
        record_servo_milestone(sock_id, "analysis_complete", "camera_bottom")
        
        print("6. Simulating servo processing...")
        record_servo_milestone(sock_id, "servo_processing_start", "camera_bottom")
        time.sleep(0.02)  # Simulate servo processing
        record_servo_milestone(sock_id, "servo_processing_complete", "camera_bottom")
        
        print("7. Simulating both cameras complete...")
        record_servo_milestone(sock_id, "both_cameras_complete")
        
        print("8. Simulating final servo decision...")
        record_servo_milestone(sock_id, "final_decision_start")
        time.sleep(0.01)  # Simulate decision time
        record_servo_milestone(sock_id, "servo_command_send")
        time.sleep(0.1)   # Simulate servo command time (this might be slow if hardware is slow)
        record_servo_milestone(sock_id, "servo_final_decision")
        
        print("\n" + "="*60)
        print("SERVO DELAY ANALYSIS RESULTS:")
        print("="*60)
        
        # Analyze the complete timeline
        log_servo_delay_analysis(sock_id)
        
        print("✓ Servo delay analysis test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Servo delay analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_slow_scenario():
    """Test a scenario with intentionally slow operations to trigger warnings."""
    try:
        from backend.src.stockings_analysis.stocking_analysis import (
            record_servo_milestone,
            log_servo_delay_analysis,
            servo_timing_milestones
        )
        
        # Clear any existing timing data
        servo_timing_milestones.clear()
        
        sock_id = 99999
        
        print("\n" + "="*60)
        print("TESTING SLOW SCENARIO (should trigger warnings):")
        print("="*60)
        
        # Simulate a slow scenario
        record_servo_milestone(sock_id, "event_detected", "camera_top")
        time.sleep(0.1)
        
        record_servo_milestone(sock_id, "servo_init_start", "camera_top")
        time.sleep(0.05)
        record_servo_milestone(sock_id, "servo_init_complete", "camera_top")
        
        # Simulate very slow analysis (this should trigger a warning)
        record_servo_milestone(sock_id, "analysis_start", "camera_top")
        time.sleep(3.0)  # 3 seconds - this is slow!
        record_servo_milestone(sock_id, "analysis_complete", "camera_top")
        
        # Simulate bottom camera
        record_servo_milestone(sock_id, "event_detected", "camera_bottom")
        record_servo_milestone(sock_id, "analysis_start", "camera_bottom")
        time.sleep(2.5)  # Also slow
        record_servo_milestone(sock_id, "analysis_complete", "camera_bottom")
        
        record_servo_milestone(sock_id, "both_cameras_complete")
        record_servo_milestone(sock_id, "final_decision_start")
        record_servo_milestone(sock_id, "servo_final_decision")
        
        # This should show warnings about slow operations
        log_servo_delay_analysis(sock_id)
        
        print("✓ Slow scenario test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Slow scenario test failed: {e}")
        return False


def explain_analysis_output():
    """Explain what to look for in the analysis output."""
    print("\n" + "="*60)
    print("HOW TO INTERPRET SERVO DELAY ANALYSIS:")
    print("="*60)
    print("""
The servo delay analysis will show you:

1. **Total Delay**: Time from first event detection to final servo movement
   - Target: < 2 seconds
   - Warning: > 5 seconds

2. **Timeline Breakdown**: Each step with relative timing
   - Shows exactly where time is being spent
   - Helps identify bottlenecks

3. **Bottleneck Warnings**: Automatic detection of slow steps
   - Any step taking > 2 seconds will be flagged
   - Focus optimization efforts on these areas

4. **Common Bottlenecks to Watch For**:
   - analysis_start -> analysis_complete: AI model processing
   - servo_command_send -> servo_final_decision: Hardware response
   - event_detected -> event_end: Event duration (depends on object speed)
   - Large gaps between camera completions: Synchronization issues

5. **Optimization Strategies**:
   - If analysis is slow: Optimize AI model or use GPU
   - If servo command is slow: Check hardware/serial communication
   - If event duration is long: Check camera settings or object speed
   - If synchronization is poor: Check camera timing alignment
""")


if __name__ == '__main__':
    print("Testing Servo Delay Analysis System")
    print("=" * 60)
    
    success = True
    
    if not test_servo_delay_analysis():
        success = False
    
    if not test_slow_scenario():
        success = False
    
    explain_analysis_output()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All servo delay analysis tests passed!")
        print("\nThe system is now ready to help you identify:")
        print("- Exact timing of each step from event to servo movement")
        print("- Bottlenecks causing delays > 20 seconds")
        print("- Specific operations that need optimization")
        print("\nLook for [SERVO DELAY ANALYSIS] logs in your production system!")
    else:
        print("❌ Some tests failed")
        sys.exit(1)
