"""
# File: camera_abc.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
#     The script contains three classes: CameraStatus, AbstractCameraSettingsMap, and AbstractCamera. These classes
#     provide an abstract framework for managing cameras, including defining camera statuses, camera settings mapping,
#     and common camera functionalities. Concrete camera implementations can be derived from these abstract classes and
#     provide their own implementations for the abstract methods.
"""
import logging
import os
from abc import ABC, abstractmethod

import cv2

from backend.src.utils.events import Events
from enum import Enum


class CameraStatus(Enum):
    DISCONNECTED = 1  # The camera is not reachable
    REACHABLE = 2  # The camera is connected but not yet initialized
    INITIALIZED = 3  # Camera is connected and the configuration parameters are correctly loaded
    STREAMING = 4  # Camera is initialized and streaming

    def __str__(self):
        return self.name


class AbstractCamera(ABC, Events):
    """
      Camera(ABC) - Abstract class for managing different types of cameras

      This abstract class defines the methods for managing different types of cameras.

      Methods:
          - start_streaming(): Abstract method to start streaming frames
          - stop_streaming(): Abstract method to stop streaming frames
          - configure(config): Abstract method to configure camera settings
          - get_status(): Abstract method to get camera status
          - retrieve_image(): Abstract method to retrieve an image from the camera
      """

    def __init__(self, serial_number: str, config: dict, on_image=None, *args):
        """
        Initialize an instance of AbstractCamera.

        :param serial_number: (str) The ID of the camera.
        :param config: Dictionary or Path to a YAML configuration file. The file should follow the format:
                        <camid|"any">:
                            <param_01>: Value_01
                            <param_02>: Value_02
                            ...
                            <param_03>: Value_03
        :param on_image: (callable) A function to be called when an image is received (default: None).
        :param on_status: (callable) A function to be called when the camera status changes (default: None).
        """
        self.logger = logging.getLogger(self.__class__.__name__)

        self.status = CameraStatus.DISCONNECTED
        self.serial_number = serial_number
        self.image_size = (config["width"], config["height"])
        #self.image_size = (config["common"]["width"], config["common"]["height"])
        self.alias = config[serial_number]["alias"]

        self._config = config

        # Register event to inform that a new image was received
        self._events = Events()
        if on_image is not None:
            self._events.on("image", on_image)

        self.configure(config)

        self.status = CameraStatus
        self.cams = []

    @abstractmethod
    def start_streaming(self) -> bool:
        pass

    @abstractmethod
    def stop_streaming(self):
        pass

    @abstractmethod
    def configure(self, config):
        pass

    @abstractmethod
    def retrieve_image(self, max_tries, trigger_event):
        pass

    @abstractmethod
    def validate_calibration(self, store_dir=None) -> bool:
        pass

    @staticmethod
    def calculate_gray(img, gray_center, pixels_roi=15):
        """
        Get the brightness value of the gray card

        :param img: Image that contains gray card and QR codes
        :param gray_center: Tuple containing x and y coordinates of the gray card's center
        :param pixels_roi: Side size of the ROI

        :return: The mean gray value of the center of the gray card. Raises an exception if the measure is incorrect
        """
        center_x, center_y = gray_center
        pr = pixels_roi // 2
        roi = img[center_y - pr:center_y + pr, center_x - pr:center_x + pr]

        try:
            mean_gray = cv2.mean(cv2.cvtColor(roi, cv2.COLOR_BGR2HSV))[2]  # Brightness
            return mean_gray
        except cv2.error as e:
            raise Exception(f"Cannot calculate gray value. {e}")

    def validate_calibration(self, store_dir="static/calib/") -> bool:
        """
        Check whether the camera brightness is correctly calibrated.
        :return: True if the measured brightness is in the range of the target brightness +/- allowed deviation
        """
        # Capture image
        img = self.retrieve_image(trigger_event=False)
        if img is None:
            return False

        try:
            os.makedirs(store_dir, exist_ok=True)
            img_path = os.path.join(store_dir, f"{self.serial_number}.jpg")
            cv2.imwrite(img_path, img)
        except Exception as ex:
            self.logger.error(f"Cannot save calibration image {self.serial_number}.jpg on disk. {ex}")

        try:
            measured_brightness = self.calculate_gray(img, self._config[self.serial_number]["coordinates"])
            # measured_brightness = 110
        except cv2.error as ex:
            self.logger.error(f"{ex}")
            return False

        target_brightness = self._config["common"]["gray"]
        allowed_dev = target_brightness * self._config["common"]["gray_tolerance"] / 100

        self.logger.info(f"Gray [{self.serial_number}] {int(measured_brightness)}. Saved on {store_dir}")

        return abs(measured_brightness - target_brightness) <= allowed_dev
