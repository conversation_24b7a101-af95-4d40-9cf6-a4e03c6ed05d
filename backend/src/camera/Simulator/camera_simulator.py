"""
Simple directory-driven camera that pretends to be a physical device.
It exposes the same public surface the real cameras use:
    • serial_number        – unique id (key from YAML)
    • alias                – human-readable name
    • camera_imgs_queue    – Queue where frames are deposited
    • start_streaming()    – begins pushing images
    • retrieve_image()     – non-blocking dequeue for CameraManager
    • stop_streaming()     – graceful shutdown
"""

import cv2
import glob
import os
import threading
import time
from queue import Queue, Full, Empty
from typing import Tuple

class CameraSimulator:
    def __init__(self, serial_number: str, config: dict):
        self.serial_number = serial_number
        self.alias        = config.get("alias", serial_number)

        # --- frame source ----------------------------------------------------
        img_dir  = config["images_path"]                # required
        pattern  = os.path.join(img_dir, "*.*")
        self._files = sorted(glob.glob(pattern))
        if not self._files:
            raise FileNotFoundError(f"[{self.alias}] no images in {img_dir}")

        self._interval = float(config.get("interval", 1))   # seconds
        # ---------------------------------------------------------------------

        # data pipeline identical to real cameras
        self.camera_imgs_queue = Queue(maxsize=100)

        # optional fields referenced elsewhere in the codebase
        self.difference = None
        self.sharpness  = None
        self.gain       = 1.0
        self.deviation  = None
        self.low_tuple  = None
        self.high_tuple = None
        self.crop_left  = 0
        self.crop_right = 0

        self._stop_evt  = threading.Event()
        self._thread    = None
        self._idx       = 0                # current file index

    # ------------------------------------------------------------------ public
    def start_streaming(self) -> None:
        if self._thread is None:
            self._thread = threading.Thread(
                target=self._loop, name=f"SimCam-{self.serial_number}", daemon=True
            )
            self._thread.start()

    def stop_streaming(self) -> None:
        self._stop_evt.set()
        if self._thread:
            self._thread.join(timeout=2)

    def retrieve_image(self) -> Tuple[bool, "np.ndarray"]:
        """Called by CameraManager. Non-blocking."""
        try:
            img = self.camera_imgs_queue.get_nowait()
            return True, img
        except Empty:
            return False, None

    # ----------------------------------------------------------------- private
    def _loop(self) -> None:
        while not self._stop_evt.is_set():
            path = self._files[self._idx]
            img  = cv2.imread(path)

            if img is not None:
                try:
                    self.camera_imgs_queue.put(img, timeout=1)
                except Full:
                    # drop frame silently – caller already logs queue overflows
                    pass

            # advance & wrap around
            self._idx = (self._idx + 1) % len(self._files)
            time.sleep(self._interval)
