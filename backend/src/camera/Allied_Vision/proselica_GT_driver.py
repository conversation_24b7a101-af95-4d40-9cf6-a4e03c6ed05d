import atexit
import threading
import cv2

import numpy as np
import matplotlib.image

import os
import time
import traceback

import vmbpy

from typing import Optional

from backend.src.camera.camera import Camera as AbstractCamera

# To use the vmbpy wrapper for VimbaX on Linux, you need to register the instalation path of VimbaX.
# Add this line to the .bashrc file : export GENICAM_GENTL64_PATH=$GENICAM_GENTL64_PATH:"/opt/VimbaX_2022-1/cti"

from typing import Optional


class CameraException(Exception):
    """Raise for CameraException"""

import collections
#TODO : Replace with queue.SimpleQueue
class BlockingQueue:
    def __init__(self, max_size=30):
        self.queue = collections.deque(maxlen=max_size)
        self.acquiring = False
        self.mutex = threading.Lock()
        self.mutex.acquire()

    def dequeue(self):
        if not self.queue:
            if not self.mutex.locked():
                self.mutex.acquire()
            self.mutex.acquire()

        frame = self.queue.pop()
        return frame

    def enqueue(self, frame):
        self.acquiring = True
        self.queue.append(frame)

        if self.mutex.locked():
            self.mutex.release()

def feature_changed_handler(feature):
    msg = 'Feature \'{}\' changed value to \'{}\''
    print(msg.format(str(feature.get_name()), str(feature.get())), flush=True)

class Handler(threading.Thread):
    def __init__(self, cam, width, height, offsetX, offsetY, gain, exposureTime, white_balance_auto=False,
                        white_balance_red_value=None,
                        white_balance_blue_value=None):
        threading.Thread.__init__(self)
        self.queue=BlockingQueue()

        self.cam=cam

        self.width=width
        self.height=height
        self.offsetX=offsetX
        self.offsetY=offsetY
        self.gain=gain
        self.exposureTime=exposureTime

        self.white_balance_auto = white_balance_auto
        self.white_balance_red_value = white_balance_red_value
        self.white_balance_blue_value = white_balance_blue_value

        self.killswitch = threading.Event()

    def __call__(self, cam: vmbpy.Camera, stream: vmbpy.Stream, frame: vmbpy.Frame):
        if frame.get_status() == vmbpy.FrameStatus.Complete:
            print('{} acquired {}'.format(cam, frame), flush=True)
            # Convert frame to the correct format
            display = frame.convert_pixel_format(vmbpy.PixelFormat.Bgr8)
            #print(dir(frame))
            self.queue.enqueue(display.as_opencv_image())
        cam.queue_frame(frame)

    def setup_camera(self, cam: vmbpy.Camera, width, height, offsetX, offsetY, gain, exposureTime, white_balance_auto=False,
                        white_balance_red_value=None,
                        white_balance_blue_value=None):
        cam.get_feature_by_name('UserSetSelector').set(0)

        # Enable auto exposure time setting if camera supports it
        if exposureTime is None:
            try:
                cam.ExposureAuto.set('Continuous')
                cam.ExposureAutoTarget.set(60)
            except (AttributeError, vmbpy.VmbFeatureError):
                print("Failed ExposureAuto.")
                pass
        else:
            cam.ExposureAuto.set('Off')
            cam.ExposureTimeAbs.set(exposureTime)

        if white_balance_auto:
            cam.BalanceWhiteAuto.set('Continuous')
        else:
            if white_balance_red_value is not None and white_balance_blue_value is not None:
                cam.BalanceWhiteAuto.set('Off')
                cam.BalanceRatioSelector.set('Red')
                cam.BalanceRatioAbs.set(white_balance_red_value)
                cam.BalanceRatioSelector.set('Blue')
                cam.BalanceRatioAbs.set(white_balance_blue_value)



        # Todo : apply exposureTime value
        # Todo : apply gain value

        # Enable white balancing if camera supports it
        # try:
        #     cam.BalanceWhiteAuto.set('Continuous')
        # except (AttributeError, vmbpy.VmbFeatureError):
        #     print("Failed BalanceWhiteAuto.")
        #     pass
        # Try to adjust GeV packet size. This Feature is only available for GigE - Cameras.
        try:
            stream = cam.get_streams()[0]
            stream.GVSPAdjustPacketSize.run()
            while not stream.GVSPAdjustPacketSize.is_done():
                pass
        except (AttributeError, vmbpy.VmbFeatureError):
            print("Failed AdjustPacketSize.")
            pass
    
        cam.set_pixel_format(vmbpy.PixelFormat.BayerRG8)

        if width!=None:
            cam.Width.set(width)
        if height!=None:
            cam.Height.set(height)

        if offsetX!=None:
            cam.OffsetX.set(offsetX)
        if offsetY!=None:
            cam.OffsetY.set(offsetY)

        #cam.AcquisitionFrameRateAbs.set(1.0)

        # for event in cam.EventSelector.get_available_entries():
        #     cam.EventSelector.set(event)
        #     cam.EventNotification.set('Off')

        # feats = cam.get_features_by_category('/EventControl/EventData')

        # for feat in feats:
        #     feat.register_change_handler(feature_changed_handler)

    def stop(self):
        self.killswitch.set()

    def run(self):
        print("Handler setting up camera.")
        with self.cam as cam :
            self.setup_camera(self.cam, self.width, self.height, self.offsetX, self.offsetY, self.gain, self.exposureTime,
                              white_balance_auto=self.white_balance_auto, white_balance_red_value=self.white_balance_red_value,
                              white_balance_blue_value=self.white_balance_blue_value)
            print("Starting stream.")
            #time.sleep(30)
        
            cam.start_streaming(self)
            self.killswitch.wait()
            self.cam.stop_streaming()

def get_camera(camera_id: Optional[str]) -> vmbpy.Camera:
    with vmbpy.VmbSystem.get_instance() as vmb:
        if camera_id:
            try:
                return vmb.get_camera_by_id(camera_id)

            except vmbpy.VmbCameraError:
                print('Failed to access Camera \'{}\'. Abort.'.format(camera_id))

        else:
            cams = vmb.get_all_cameras()
            if not cams:
                print('No Cameras accessible. Abort.')

            return cams[0]

class Camera(AbstractCamera):
    def __init__(self):
        self._camera = None

        # Populated in configureCamera
        self._handlerImage = None

        self.reconnect_mutex = None

        # Save the config to reaply on reset or reconnect
        self.save_config = {}

        self.doneConfig=threading.Lock()
        self.doneConfig.acquire()

    def _configureCamera(self, ID, width, height, offsetX, offsetY, gain, exposureTime, white_balance_auto=False,
                        white_balance_red_value=None,
                        white_balance_blue_value=None):
        with vmbpy.VmbSystem.get_instance():
            print("getting camera!")
            self._camera = get_camera(ID)
            print(self._camera.get_id())

            self._handlerImage=Handler(self._camera, width, height, offsetX, offsetY, gain, exposureTime, white_balance_auto,white_balance_red_value,white_balance_blue_value)
            self._handlerImage.start()
            self.doneConfig.release()
            self._handlerImage.join()


    def configureCamera(self,
                        ID="DEV_000F3102E503",
                        width=None,
                        height=1100,
                        offsetX=None,
                        offsetY=1000,
                        gain=None,
                        exposureTime=None,
                        white_balance_auto=False,
                        white_balance_red_value=None,
                        white_balance_blue_value=None,
                        distanceFromObject=67e-2):
        
        self.save_config["ID"] = ID
        self.save_config["width"] = width
        self.save_config["height"] = height
        self.save_config["offsetX"] = offsetX
        self.save_config["offsetY"] = offsetY
        self.save_config["gain"] = gain
        self.save_config["exposureTime"] = exposureTime
        self.save_config["distanceFromObject"] = distanceFromObject

        threading.Thread(target=self._configureCamera, args=(ID, width, height, offsetX, offsetY, gain, exposureTime,
                                                             white_balance_auto,white_balance_red_value,white_balance_blue_value), daemon=True).start()
        self.doneConfig.acquire()

    def reaplyConfigCamera(self):
        # self._camera.DestroyDevice()
        # self.configureCamera(
        #     self.save_config["modelName"],
        #     self.save_config["nbLineFrame"],
        #     self.save_config["width"],
        #     self.save_config["offset"],
        #     self.save_config["nbFrameImage"],
        #     self.save_config["gain"],
        #     self.save_config["exposureTime"],
        #     self.save_config["packetSize"],
        #     self.save_config["distanceFromObject"])
        return

    def autoReconnectToCamera(self):
        pass
        # while True:
        #     print("autoReconnectToCamera mutex_empty_img_buffer : acquire")
        #     self.reconnect_mutex.acquire()
        #     print("autoReconnectToCamera mutex_empty_img_buffer : acquire OK")
        #     i = 0
        #     while True:
        #         print(
        #             "!! Warning : Camera disconnected !! \n Reconnecting to camera... \n Atempt ", i)
        #         i += 1
        #         try:
        #             self.reaplyConfigCamera()  # Using last configuration
        #             self.StartGrabbing()
        #             break
        #         except:
        #             traceback.print_exc()
        #             time.sleep(3)
        #             continue

    def GetFrameHeight(self):
        try :
            with self._camera as cam :
                value=cam.Height.get()
        except Exception as e :
            raise CameraException(str(e)) from e
        return value

    def GetImageHeight(self):
        return self.GetFrameHeight()

    def GetFrameWidth(self):
        try :
            with self._camera as cam :
                value=cam.Width.get()
        except Exception as e :
            raise CameraException(str(e)) from e
        return value

    def GetGain(self):
        pass
        # try:
        #     value = self._camera.GainRaw.Value
        # except pylon.GenericException as e:
        #     raise CameraException(str(e)) from e
        # return value

    def GetExposureTime(self):
        pass
        # try:
        #     value = self._camera.ExposureTimeRaw.Value
        # except pylon.GenericException as e:
        #     raise CameraException(str(e)) from e
        # return value

    def SetGain(self, value):
        pass
        # try:
        #     self._camera.GainRaw = value
        # except pylon.GenericException as e:
        #     raise CameraException(str(e)) from e

    def SetExposureTime(self, value):
        pass
        # try:
        #     self._camera.ExposureTimeRaw = value
        # except pylon.GenericException as e:
        #     raise CameraException(str(e)) from e

    def StartGrabbing(self):
        pass
        # To do : Kill current handler and recreate one.

        # try:
        #     self._camera.StartGrabbing(
        #         pylon.GrabStrategy_LatestImages,
        #         pylon.GrabLoop_ProvidedByInstantCamera)
        # except pylon.GenericException as e:
        #     raise CameraException(str(e)) from e

    def StopGrabbing(self):
        self._handlerImage.stop() # not tested

    def RetrieveImage(self):
        try:
            value = self._handlerImage.queue.dequeue()
        except Exception as e:
            raise CameraException(str(e)) from e
        return value

    def exit_handler(self):
        try:
            self.StopGrabbing()
        except:
            pass
        print('Quitting ...')




def test_retriever():
    img_display = np.zeros((camera1.GetFrameHeight(
    )*countOfImagesToGrab, camera1.GetFrameWidth()), dtype=np.uint8)
    
    for ii in range(0,1):
        print("ii : ",ii)
    
        i = 0

        # Concatenats every images captured into on big image.
        while i < (countOfImagesToGrab):
            print("test_retriever : Retrieving an image", i)
            image, succes, index = camera1.RetrieveImage()
            print("Camera status : ", camera1.status)
            if succes:
                # print("image.shape",image.shape)
                # print("img_display range : ", img_display[camera1.GetImageHeight()*i : camera1.GetImageHeight()*(i+1)].shape, "range :", camera1.GetFrameHeight()*i*nbFrameimage, " : ", camera1.GetFrameHeight()*(i+1)*nbFrameimage)
                print("Retrieved an image : index : ",
                    index, ":", image[5][500:520])
                img_display[camera1.GetImageHeight(
                )*i: camera1.GetImageHeight()*(i+1)] = image
                i += 1
            else:
                print("Failed to acquire image !!")
            time.sleep(0.5)

        print("!!!!DONE!!!!")
        #camera1.StopGrabbing()

        # plt.figure()
        # plt.imshow(img_display, cmap='gray', norm="linear")

        # for i in range(countOfImagesToGrab*nbFrameimage):
        #     plt.plot([0,4096],[i*camera1.nbLineFrame,i*camera1.nbLineFrame],linewidth=0.4, alpha=0.4, color="b")

        # for i in range(countOfImagesToGrab):
        #     plt.plot([0,4096],[i*camera1.GetImageHeight(),i*camera1.GetImageHeight()],linewidth=0.4, alpha=0.4, color="r")

        # plt.show()

        index_png = 0
        while os.path.exists("./images/"+str(index_png)+"IMAGE.png"):
            index_png += 1
            if index_png > 200:
                break

        matplotlib.image.imsave("./images/"+str(index_png) +
                                "IMAGE.png", img_display, cmap='gray')


# START
if __name__ == "__main__":
    countOfImagesToGrab = 3

    log = vmbpy.Log.get_instance ()

    log.enable(vmbpy.LOG_CONFIG_INFO_CONSOLE_ONLY)
    # log.disable()

    print("Biginning setup !!!")

    camera1 = Camera()
    camera1.configureCamera()  # nbFrameImage=nbFrameimage

    atexit.register(camera1.exit_handler)

    print("Done with setup !!!")

    index_png = 0
    while os.path.exists("./images/"+str(index_png)+"IMAGE.png"):
        index_png += 1
        if index_png > 200:
            break

    while True :
        print("Loop")
        frame=camera1.RetrieveImage()
        print(frame.shape)
        print(cv2.imwrite("./images/"+str(index_png) +"IMAGE.png", frame))
        # matplotlib.image.imsave("./images/"+str(index_png) +
        #                         "IMAGE.png", frame, cmap="bgr")
        index_png += 1
        print("Retrieved image main.")
        time.sleep(1)

    #test_retriever()
