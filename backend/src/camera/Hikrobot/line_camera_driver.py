# File: hikrobotics_camera.py
# Author: [Your Name]
# Description: Concrete implementation of AbstractCamera for Hikrobotics cameras.

import os
import sys
import time
import cv2
import numpy as np
from enum import Enum
#sys.path.append("MvImport")
from backend.src.camera.Hikrobot.MvImport.MvCameraControl_class import *

from backend.src.camera.camera_abc import AbstractCamera, CameraStatus
from backend.src.utils.data_process import yaml_to_dict
from backend.src.camera.Hikrobot.MvImport.CameraParams_header import MVCC_FLOATVALUE
from queue import Queue
from ctypes import memset, sizeof, byref
import traceback

class Camera(AbstractCamera):
    def __init__(self, serial_number, alias, config, logger=None):
        #super().__init__(serial_number, config)
        if logger is not None:
            self.logger = logger
        self.cam = MvCamera()
        self.device_list = MV_CC_DEVICE_INFO_LIST()
        self.serial_number = serial_number
        self.image_size = (config[self.serial_number]["width"], config[self.serial_number]["height"])
        self.alias = alias
        self.camera_imgs_queue = Queue()
        try:
            # Call the camera initialization method
            self._initialize_camera()
            # Configure the camera
            self._configure_camera(config)
        except Exception as e:
            # Log the error and traceback if the logger is available
            if self.logger:
                self.logger.error(f"Error during camera initialization: {e}")
                self.logger.error("Exception traceback: %s", traceback.format_exc())
            raise  RuntimeError("Failed to initialize camera")

        
    def _initialize_camera(self):
        ret = self.cam.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, self.device_list)
        if ret != 0 or self.device_list.nDeviceNum == 0:
            raise RuntimeError("No Hikrobotics cameras found!")

        for i in range(self.device_list.nDeviceNum):
            mvcc_dev_info = self.device_list.pDeviceInfo[i].contents
            if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
                if ''.join([chr(i) for i in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber]).rstrip('\x00') == self.serial_number:
                    ret = self.cam.MV_CC_CreateHandle(cast(self.device_list.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents)
                    if ret != 0:
                        raise RuntimeError("error: create handle fail! ret[0x%x]" % ret)
                    ret = self.cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
                    if ret != 0:
                        raise RuntimeError("error: open device fail! ret[0x%x]" % ret)
                    break
        else:
          raise RuntimeError(f"Hikrobotics camera with serial number {self.serial_number} not found!")

        self.status = CameraStatus.REACHABLE

    def _configure_camera(self, config):
        """
        Load all camera features/parameters from a feature file using
        Hikrobot's built-in MV_CC_FeatureLoad method.
        """
        config = config[self.serial_number]
        
        # Load camera settings from a configuration file if provided
        camsetting_file = config.get("camsetting_file", None)
        ret = self.cam.MV_CC_FeatureLoad(camsetting_file)
        if ret != 0:
            error_msg = f"Failed to load feature file '{camsetting_file}', ret = 0x{ret:x}"
            if self.logger:
                self.logger.error(error_msg, title=f"{self.alias}")
            raise RuntimeError(error_msg)

        if self.logger:
            self.logger.info(f"Successfully loaded camera feature file: {camsetting_file}", title=self.alias)        

    def start_streaming(self):
        ret = self.cam.MV_CC_StartGrabbing()
        if ret != 0:
            raise RuntimeError(f"Failed to start streaming: ret[0x{ret:x}]")
        if self.logger:
            self.logger.info("Camera started streaming", title=f"{self.alias}")
        self.status = CameraStatus.STREAMING

    def stop_streaming(self):
        ret = self.cam.MV_CC_StopGrabbing()
        if ret != 0:
            raise RuntimeError(f"Failed to stop streaming: ret[0x{ret:x}]")
        self.status = CameraStatus.INITIALIZED
        
    
    def retrieve_image(self, max_tries=3):
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))
        
        for _ in range(max_tries):
            ret = self.cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
            # print("ret", ret)
            if ret == 0 and stOutFrame.pBufAddr:
                try:
                    # Create a numpy array from the raw buffer
                    buffer_size = stOutFrame.stFrameInfo.nFrameLen
                    raw_buffer = (c_ubyte * buffer_size).from_address(addressof(stOutFrame.pBufAddr.contents))
                    img_data = np.frombuffer(raw_buffer, dtype=np.uint8, count=buffer_size)
                    
                    # Check the pixel format to determine how to process the data
                    pixel_format = stOutFrame.stFrameInfo.enPixelType
                    width = stOutFrame.stFrameInfo.nWidth
                    height = stOutFrame.stFrameInfo.nHeight
                    
                    # Create a properly structured output image                    
                    # Handle different pixel formats
                    if pixel_format == PixelType_Gvsp_RGB8_Packed:
                        if self.logger:
                            # Already in RGB format
                            try:
                                img_array = img_data.reshape((height, width, 3)).copy()
                                output_img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                                #self.logger.info("Capturing . . .", title=self.alias)
                            except:
                                self.logger.error("Error converting RGB8 image", title=self.alias)
                    else:
                        img_array = img_data.reshape((height, width)).copy()
                        output_img = cv2.cvtColor(img_array, cv2.COLOR_GRAY2BGR)
                    
                    # Free the buffer before returning the image
                    self.cam.MV_CC_FreeImageBuffer(stOutFrame)
                    
                    # Return the properly formatted image
                    return True, output_img
                    
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error in custom image conversion: {str(e)}", title=f"Line cam {self.alias}")
                        self.logger.error(traceback.format_exc(), title=f"{self.serial_number}")
                    self.cam.MV_CC_FreeImageBuffer(stOutFrame)
            
            # elif ret == 2147483655:  # MV_E_NODATA
            #     if self.logger:
            #         self.logger.info("No data has been captured", title=f"{self.alias}")
            # else:
            #     if self.logger:
            #         self.logger.warning(f"Failed to get image buffer: 0x{ret:x}", title=f"{self.alias}")
        
        return False, None
    
    def set_exposure(self, exposure_value: float):
        """
        Set the camera's exposure time manually.
        """
        # Ensure auto exposure is off
        ret = self.cam.MV_CC_SetEnumValue("ExposureAuto", 0)  # 0 = Off
        if ret != 0 and self.logger:
            self.logger.warning(f"Failed to disable auto exposure, ret = 0x{ret:x}", title=self.alias)

        # Now set manual exposure
        ret = self.cam.MV_CC_SetFloatValue("ExposureTime", exposure_value)
        if ret != 0:
            if self.logger:
                self.logger.error(f"Failed to set exposure to {exposure_value}, ret = 0x{ret:x}", title=self.alias)
        else:
            if self.logger:
                self.logger.info(f"Exposure set to {exposure_value}", title=self.alias)


    def validate_calibration(self, store_dir=None):
        return True

    def configure(self, config):
        self._configure_camera(config)

def start_grabbing(camera, output_folder):
    while True:
        image, success, index = camera.retrieve_image()
        # if success:
        #     # print("image.shape",image.shape)
        #     # cv2.imshow("IMG", image)
        #     # cv2.waitKey(0)
        #     #timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        #     cv2.imwrite(os.path.join(output_folder, timestamp + ".jpg"), image)
        time.sleep(0.5)

if __name__ == "__main__":
    import yaml

    with open("path_to_config.yaml", "r") as file:
        config = yaml.safe_load(file)

    camera = Camera(serial_number="your_serial_number", alias="Line Bottom", config=config)
    camera.start_streaming()
    image = camera.retrieve_image()
    if image is not None:
        cv2.imshow("Hikrobotics Camera Image", image)
        cv2.waitKey(0)
    camera.stop_streaming()
    cv2.destroyAllWindows()

