class Events:
    def __init__(self):
        self.events = {}

    def on(self, event_name, callback):
        """
        This will not be updated if the a thread is already running
        :param event_name: Register the name of the event 
        :param callback: Function to be called when the event occurs
        """
        if callback is not None:
            if event_name not in self.events:
                self.events[event_name] = [callback]
            else:
                self.events[event_name].append(callback)

    def trigger(self, event_name, *args):
        if event_name in self.events:
            for callback in self.events[event_name]:
                callback(*args)