import atexit
import datetime
import threading
from pypylon import genicam
from pypylon import pylon

import numpy as np
# import matplotlib.pyplot as plt
import matplotlib.image

import os
import time
from threading import Lock
import traceback
import cv2

import math

from sewar.full_ref import rmse

# Allow the import to work when running directly
if __name__ == "__main__":
    from configurationeventprinter import ConfigurationEventPrinter
else:
    from backend.src.camera.Basler.configurationeventprinter import ConfigurationEventPrinter



class CameraException(Exception):
    """Raise for my specific kind of exception"""


def connectToCamera(modelName):

    camConnected = False

    tlf = pylon.TlFactory.GetInstance()
    devices = tlf.EnumerateDevices()
    for d in devices:
        if d.GetModelName() == modelName:
            print(d.GetModelName(), d.GetSerialNumber())
            camera = pylon.InstantCamera(tlf.CreateDevice(d))
            print("CONECTED")
            camConnected = True
            break
    if camConnected == False:
        raise RuntimeError("Camera "+str(modelName)+" not found !")

    return camera

class Camera():
    roller_diameter=25e-3
    encoder_pulses_per_revolution=556
    
    # Only creates an empty object
    def __init__(self):
        self._camera = None

        # Populated in configureCamera
        self._handlerImage = None
        self.nbLineFrame = None

        self.reconnect_mutex = None

        # Save the config to reaply on reset or reconnect
        self.save_config = {}
        pass
    # None : default or automatic
    def configureCamera(self,
                        modelName="raL4096-24gm",
                        nbLineFrame=1000,
                        width=4096,
                        offset=None,
                        gain=256,
                        exposureTime=320,
                        packetSize=1500,
                        widthDistance=29.59e-2, use_shading=False):

        # Every single exemples for pypylon needs to be addapted to work on this camera, use the feature documentation from pylon Viewer to find the correct names for the nodes of this camera.

        self.save_config["modelName"] = modelName
        self.save_config["nbLineFrame"] = nbLineFrame
        self.save_config["width"] = width
        self.save_config["offset"] = offset
        self.save_config["gain"] = gain
        self.save_config["exposureTime"] = exposureTime
        self.save_config["packetSize"] = packetSize
        self.save_config["widthDistance"] = widthDistance
        self.save_config["use_shading"] = use_shading

        self._camera = connectToCamera(modelName)

        # Not needed to work
        eventPrinter = ConfigurationEventPrinter()
        self.reconnect_mutex = eventPrinter.reconnect_mutex
        self._camera.RegisterConfiguration(
            eventPrinter, pylon.RegistrationMode_Append, pylon.Cleanup_Delete)

        # Open the camera for setting parameters.
        self._camera.Open()

        # To get consistant results it is always good to start from "power-on" state.
        self._camera.UserSetSelector = "Default"
        self._camera.UserSetLoad.Execute()

        # Auto gain and exposure only works in low light.
        if gain==None :
            self._camera.GainAuto="Continuous"
        else :
            self._camera.GainRaw = gain
        if exposureTime==None :
            self._camera.ExposureAuto="Continuous"
        else :
            self._camera.ExposureTimeRaw = exposureTime

        self.nbLineFrame = nbLineFrame
        self._camera.Height.SetValue(nbLineFrame)

        if width==None and offset!=None :
            raise CameraException("You can not have an offset if you are using the full width of the sensor.")
        if width==None:
            self._camera.Width.SetValue(int(self._camera.WidthMax.Value))
        else:
            self._camera.Width.SetValue(width)
        if offset==None :
            self._camera.CenterX=True
        elif width!=None :
            self._camera.OffsetX.SetValue(offset)

        # Line configuration :
        self._camera.LineSelector="Line2"
        self._camera.LineTermination=True
        self._camera.LineSelector="Line1"
        self._camera.LineTermination=True

        # Shaft encoder module configuration :
        self._camera.ShaftEncoderModuleLineSelector="PhaseA"
        self._camera.ShaftEncoderModuleLineSource="Line2"
        self._camera.ShaftEncoderModuleLineSelector="PhaseB"
        self._camera.ShaftEncoderModuleLineSource="Line1"
        self._camera.ShaftEncoderModuleMode="ForwardOnly"
        self._camera.ShaftEncoderModuleCounterMode="FollowDirection"
        self._camera.ShaftEncoderModuleCounterMax=32767
        self._camera.ShaftEncoderModuleCounterReset.Execute()
        self._camera.ShaftEncoderModuleReverseCounterMax=2000
        self._camera.ShaftEncoderModuleReverseCounterReset.Execute()

        # Trigger configuration :
        self._camera.TriggerSelector = "LineStart"
        self._camera.TriggerSource = "ShaftEncoderModuleOut"
        self._camera.TriggerActivation = "RisingEdge"
        self._camera.TriggerMode = "On"

        # To do : Set encoder to a high pulse rate and calculate divider to get a square image.
        
        # self._camera.FrequencyConverterInputSource="ShaftEncoderModuleOut"
        # self._camera.FrequencyConverterSignalAlignment="RisingEdge"

        self.resolution=(widthDistance/width)
        # pulses_per_revolution=(2*math.pi*Camera.roller_diameter)/self.resolution
        # multiplier=int(pulses_per_revolution/Camera.roller_pulses_per_revolution)
        # print(multiplier)
        # if multiplier>1:
        #     self._camera.FrequencyConverterMultiplier=multiplier
        #     self._camera.FrequencyConverterPreventOvertrigger=True

        # Jumbo packets
        self._camera.GevSCPSPacketSize = packetSize

        if use_shading:
            self._camera.ShadingSelector = "GainShading"
            self._camera.ShadingEnable = True
            self._camera.ShadingSetDefaultSelector = "UserShadingSet1"
            self._camera.ShadingSetSelector = "UserShadingSet1"


        #print("Framerate : ", self._camera.ResultingFrameRateAbs.Value)

        # Creating and registering the ImageEventHandler, that contains the Image Buffer.
        self._handlerImage = ImageEventHandler(self._camera)
        self._camera.RegisterImageEventHandler(
            self._handlerImage, pylon.RegistrationMode_Append, pylon.Cleanup_Delete)

    def reaplyConfigCamera(self):
        self._camera.DestroyDevice()
        self.configureCamera(
            self.save_config["modelName"],
            self.save_config["nbLineFrame"],
            self.save_config["width"],
            self.save_config["offset"],
            self.save_config["gain"],
            self.save_config["exposureTime"],
            self.save_config["packetSize"],
            self.save_config["widthDistance"],
            self.save_config["use_shading"])
        return

    def autoReconnectToCamera(self):
        while True:
            print("autoReconnectToCamera mutex_empty_img_buffer : acquire")
            self.reconnect_mutex.acquire()
            print("autoReconnectToCamera mutex_empty_img_buffer : acquire OK")
            i = 0
            while True:
                print(
                    "!! Warning : Camera disconnected !! \n Reconnecting to camera... \n Atempt ", i)
                i += 1
                try:
                    self.reaplyConfigCamera()  # Using last configuration
                    self.StartGrabbing()
                    break
                except:
                    traceback.print_exc()
                    time.sleep(3)
                    continue

    def GetImageHeight(self):
        try:
            value = self._camera.Height.Value
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e
        return value

    def GetFrameWidth(self):
        try:
            value = self._camera.Width.Value
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e
        return value

    def GetGain(self):
        try:
            value = self._camera.GainRaw.Value
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e
        return value

    def GetExposureTime(self):
        try:
            value = self._camera.ExposureTimeRaw.Value
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e
        return value

    def SetGain(self, value):
        try:
            self._camera.GainRaw = value
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e

    def SetExposureTime(self, value):
        try:
            self._camera.ExposureTimeRaw = value
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e

    def StartGrabbing(self):
        try:
            self._camera.StartGrabbing(
                pylon.GrabStrategy_LatestImages,
                pylon.GrabLoop_ProvidedByInstantCamera)
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e

    def StopGrabbing(self):
        try:
            self._camera.StopGrabbing()
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e

    def RetrieveImage(self):
        try:
            value = self._handlerImage.RetrieveImage()
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e
        return value

    def GetStatus(self):
        try:
            return self._camera.IsOpen()  # Use IsGrabbing() ?
        except:
            traceback.print_exc()
            return False

    def exit_handler(self):
        try:
            self._camera.DestroyDevice()
        except:
            pass
        print('Quitting ...')

    def GetCameraResolution(self):
        '''Returns camera resolution in m/px .'''
        return self.resolution


SIZE_BUFFER = 100

class ImageEventHandler(pylon.ImageEventHandler):
    def __init__(self, cam):
        super().__init__()

        # Index of the frame being writen.
        self.i = 0

        self.lastCompleteImage = -1
        self.lastImageRetrieved = -1

        # Rolling buffer of fixed size : 300 images of 400*4096 in 8 bits : 0,5 GB
        self.img_buffer = np.zeros(
            (SIZE_BUFFER, cam.Height.Value, cam.Width.Value), dtype=np.uint8)
        self.index = np.zeros((SIZE_BUFFER), dtype=int)
        # Mutex for img_buffer
        self.mutex_img_buffer = Lock()

        # Mutex for empty_img_buffer
        self.mutex_empty_img_buffer = Lock()
        self.mutex_empty_img_buffer.acquire()

        self.totalImageCaptured = 0

        # self.t1_image=time.time()
        # self.t2_image=time.time()

        # self.t1_frame=time.time()
        # self.t2_frame=time.time()

    def OnImageGrabbed(self, cam, grabResult):
        """ we get called on every image
            !! this code is run in a pylon thread context
            always wrap your code in the try .. except to capture
            errors inside the grabbing as this can't be properly reported from 
            the background thread to the foreground python code
        """
        # print("CSampleImageEventHandler.OnImageGrabbed called.")
        try:
            if grabResult.GrabSucceeded():
                img = grabResult.Array

                self.mutex_img_buffer.acquire()

                # To do : Add some overlap : reapeat last frame from the previous image

                # Edge cases :
                # Warning if the buffeer is full and overwriting some data. X
                # Warning if the buffer is empty. X
                # Prevent read head from overtaking write head (buffer empty). X
                # Prevent from retrieving an incomplete image. X

                # Add to current image
                # print("Adding to current image : Number : ", self.i, "Range : ", self.ii*cam.Height.Value, ":", (self.ii+1)*cam.Height.Value)
                # print("OnImageGrabbed : self.lastImageRetrieved : ",self.lastImageRetrieved, "self.lastCompleteImage : ", self.lastCompleteImage, "self.i : ", self.i, "self.totalImageCaptured : ", self.totalImageCaptured)
                self.img_buffer[self.i] = img
                self.index[self.i] = self.totalImageCaptured

                # Points read point to the oldest image not overwriten in the buffer
                if self.i == self.lastImageRetrieved:
                    self.lastImageRetrieved = self.i
                    print("Warning : Overwriting images in buffer not retrieved.")

                # If Image is complete :
                if self.i >= 0:
                    rmse_result = rmse(
                        self.img_buffer[self.i], self.img_buffer[self.i-1])
                    if rmse_result < 3:
                        print("!! Warning : cloth is stuck !!", rmse_result)

                self.lastCompleteImage = self.i
                self.i += 1
                self.i %= SIZE_BUFFER

                self.totalImageCaptured += 1
                # If someone is waiting for the buffer to not be empty, release the lock:
                if self.mutex_empty_img_buffer.locked():
                    self.mutex_empty_img_buffer.release()

                # self.t2_image=time.time()
                # print("Image aqisition time : ",self.t2_image-self.t1_image)
                # self.t1_image=self.t2_image

                self.mutex_img_buffer.release()

                # self.t2_frame=time.time()
                # print("Frame aqisition time : ",self.t2_frame-self.t1_frame)
                # self.t1_frame=self.t2_frame

            else:
                raise RuntimeError("Grab Failed")
        except Exception as e:
            traceback.print_exc()

        return

    def RetrieveImage(self):
        succes = False
        image = None

        self.mutex_img_buffer.acquire()

        # check if an image is available
        if self.lastCompleteImage < 0 or (((self.lastImageRetrieved+1) % SIZE_BUFFER) == self.i):
            #print("Buffer empty")

            self.mutex_img_buffer.release()

            # If it is not already locked : lock it.
            if not self.mutex_empty_img_buffer.locked():
                # print("RetrieveImage mutex_empty_img_buffer : acquire 1")
                self.mutex_empty_img_buffer.acquire()
                # print("RetrieveImage mutex_empty_img_buffer : acquire 1 OK")

            # Wait for it to be released when a new image is added.
            # print("RetrieveImage mutex_empty_img_buffer : acquire 2", self.mutex_empty_img_buffer.locked())
            if not self.mutex_empty_img_buffer.acquire(timeout=5):
                return None, False, -1
            # print("RetrieveImage mutex_empty_img_buffer : acquire 2 OK")

            self.mutex_img_buffer.acquire()

        # print("RetrieveImage : self.lastImageRetrieved : ",self.lastImageRetrieved, "self.lastCompleteImage : ", self.lastCompleteImage, "self.i : ", self.i)
        self.lastImageRetrieved += 1
        self.lastImageRetrieved %= SIZE_BUFFER
        image = self.img_buffer[self.lastImageRetrieved]
        index = self.index[self.lastImageRetrieved]

        succes = True

        self.mutex_img_buffer.release()

        return image, succes, index

# Use mutex to passively wait for next frame

def start_grabbing(camera, output_folder):

    while True:
        image, success, index = camera.RetrieveImage()
        if success:
            # print("image.shape",image.shape)
            #cv2.imshow("IMG", image)
            #cv2.waitKey(0)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            cv2.imwrite(os.path.join(output_folder, timestamp+".jpg"),image)
        time.sleep(0.5)


# START
if __name__ == "__main__":

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_folder = os.path.join("/home/<USER>/projects/defis/data/", timestamp)
    os.makedirs(output_folder,exist_ok=True)

    camera1 = Camera()
    camera1.configureCamera(nbLineFrame=1000, exposureTime=12000, use_shading=False)

    print("Image size : ", camera1.GetFrameWidth(),"*", camera1.GetImageHeight())
    print("Resolution : %.4f mm/px" % (camera1.GetCameraResolution()*1e3))
    camera1.StartGrabbing()

    atexit.register(camera1.exit_handler)

    threading.Thread(target=camera1.autoReconnectToCamera, daemon=True).start()

    start_grabbing(camera1,output_folder)