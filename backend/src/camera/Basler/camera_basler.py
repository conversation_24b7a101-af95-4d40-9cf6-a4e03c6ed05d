"""
# File: camera_basler.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
#   The script camera_basler.py is designed to interface with Basler cameras. It provides functionalities for
#   configuring the camera, starting and stopping the video stream, and capturing images. The script is built on top
#   of the pypylon library and utilizes OpenCV (cv2) for image processing.
"""

import os
import threading
from datetime import datetime

import cv2 as cv2
from backend.src.camera.camera_abc import AbstractCamera
from pypylon import pylon
from queue import Queue, Empty, Full
import logging


class BaslerCamera(AbstractCamera):
    def __init__(self, serial_number: int, config: dict, on_image=None, alias = None,  cam: pylon.InstantCamera = None,
                 event_handler: pylon.ConfigurationEventHandler = None, camera_ip=None, logger=None):
        """
        Initialize the BaslerCamera instance.

        :param serial_number: Serial number of the camera.
        :param config: Configuration settings as a dictionary.
        :param cam: (pylon.InstantCamera) Camera instance.
        :param event_handler: (pylon.ConfigurationEventHandler) event handler for camera status changed.
        :raises ConnectionError: If the camera is not available.
        """
        if logger is not None:
            self.logger = logger
        self.serial_number = str(serial_number)

        self.camera_capturing_thread = threading.Event()
        self.camera_imgs_queue = Queue()
        
        self.alias = alias
        self.camera_ip = config[serial_number].get("camera_ip", None)
        
        if self.camera_ip is None:
            if cam is None:
                try:
                    tl_factory = pylon.TlFactory.GetInstance()
                    devices = tl_factory.EnumerateDevices()
                    if not devices:
                        raise ConnectionError(f"No Basler cameras detected")
                    
                    cam_found = False
                    
                    for i, di in enumerate(devices):
                        camera = pylon.InstantCamera(tl_factory.CreateDevice(di))
                        device_serial = di.GetSerialNumber()
                        
                        if str(device_serial) == self.serial_number:
                            cam = camera
                            cam_found = True
                            break
                    
                    if not cam_found:
                        available_cameras = [di.GetSerialNumber() for di in devices]
                        raise ConnectionError(f"Camera {self.serial_number} not found. Available cameras: {available_cameras}")
                    
                except Exception as ex:
                    raise ConnectionError(f"Camera {self.serial_number} not available: {str(ex)}")

        else:
            # Initialize camera using IP address
            try:
                tl_factory = pylon.TlFactory.GetInstance()
                device_info = pylon.DeviceInfo()
                device_info.SetPropertyValue("IpAddress", self.camera_ip)
                device_info.SetPropertyValue("DeviceClass", "BaslerGigE")
                
                # Try to create the device
                if not tl_factory.IsDeviceAccessible(device_info):
                    raise ConnectionError(f"Camera at IP {self.camera_ip} is not accessible")
                    
                device = tl_factory.CreateDevice(device_info)
                cam = pylon.InstantCamera(device)
                
                # Verify serial number matches
                device_serial = cam.GetDeviceInfo().GetSerialNumber()
                if device_serial != self.serial_number:
                    raise ConnectionError(f"Camera at IP {self.camera_ip} has serial number {device_serial}, expected {self.serial_number}")
                
                # try:
                #     camera = pylon.InstantCamera(device)
                #     print(f"  Opening camera... ", end="")
                #     camera.Open()
                    
                #     if camera.IsOpen():
                #         print("Success!")
                #         # Try getting camera info
                #         try:
                #             print(f"  Camera information:")
                #             print(f"    Model: {camera.GetDeviceInfo().GetModelName()}")
                #             print(f"    Serial: {camera.GetDeviceInfo().GetSerialNumber()}")
                #         except Exception as info_ex:
                #             print(f"  Could not get camera details: {info_ex}")
                        
                #         # Always properly close
                #         camera.Close()
                #     else:
                #         print("Failed to open camera!")
                # except Exception as cam_ex:
                #     print(f"\n  Error opening camera: {cam_ex}")

            except Exception as ex:
                raise ConnectionError(f"Failed to connect to camera at IP {self.camera_ip}: {str(ex)}")

        self.cam: pylon.InstantCamera = cam
        self.event_handler = event_handler
        self._configure_camera(config)
            #super().__init__(serial_number, config, on_image)

    def _configure_camera(self, config):
        """
        Load camera configuration from a .pfs file if specified in the config.
        
        The .pfs file contains all camera settings saved from the Basler Pylon Viewer
        and can be loaded directly to configure the camera.
        """
        # Check if a configuration file is specified

        
        camera_config = config[self.serial_number]
        config_file_path = camera_config.get("camsetting_file")
        
        if not config_file_path:
            self.logger.info(f"No configuration file specified for camera {self.serial_number}")
            return False
        
        # Check if the file exists
        if not os.path.exists(config_file_path):
            self.logger.error(f"Configuration file {config_file_path} not found")
            return False
        
        try:
            # Open the camera if it's not already open
            was_open = self.cam.IsOpen()
            if not was_open:
                self.cam.Open()
            
            # Load the configuration file
            self.logger.info(f"Loading configuration from {config_file_path}")
            pylon.FeaturePersistence.Load(config_file_path, self.cam.GetNodeMap(), True)
            self.logger.info(f"Configuration loaded successfully for camera {self.alias}")
            
            # Close the camera if it was initially closed
            if not was_open:
                self.cam.Close()
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {str(e)}")
            if not was_open and self.cam.IsOpen():
                self.cam.Close()
            return False

    
    def set_exposure(self, exposure_value: float):
        """
        Set the camera's exposure time manually.
        """
        # Ensure auto exposure is off
        ret = self.cam.MV_CC_SetEnumValue("ExposureAuto", 0)  # 0 = Off
        if ret != 0 and self.logger:
            self.logger.warning(f"Failed to disable auto exposure, ret = 0x{ret:x}", title=self.alias)

        # Now set manual exposure
        ret = self.cam.MV_CC_SetFloatValue("ExposureTime", exposure_value)
        if ret != 0:
            if self.logger:
                self.logger.error(f"Failed to set exposure to {exposure_value}, ret = 0x{ret:x}", title=self.alias)
        else:
            if self.logger:
                self.logger.info(f"Exposure set to {exposure_value}", title=self.alias)


    def start_streaming(self) -> bool:
        """
        Start the video stream of the camera.

        :return: True if streaming started successfully, False otherwise.
        """
        try:
            self.cam.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
            self.logger.info("Camera started streaming", title=f"{self.alias}")
            return True
        except Exception as es:
            self.cam.EndAcquisition()
            self.logger.error(str(es))
            return False

    def stop_streaming(self):
        """
        Stop the video stream of the camera.
        """
        self.cam.StopGrabbing()
        self.logger.debug(f"Camera {self.serial_number} end acquisition")

    def configure(self, config):
        """
         Configure the camera settings based on the provided configuration dictionary.
         """
        # Enable SW trigger
        self.cam.RegisterConfiguration(pylon.SoftwareTriggerConfiguration(), pylon.RegistrationMode_Append,
                                       pylon.Cleanup_Delete)

        if self.event_handler:
            self.cam.RegisterConfiguration(self.event_handler, pylon.RegistrationMode_Append,
                                           pylon.Cleanup_Delete)

        self.cam.Open()

        # COMMON SETTINGS
        #  = self._config["common"]["settings"]
        # self.cam.Height.SetValue(settings["height"])

        # CAMERA SPECIFIC SETTINGS
        settings = config[self.serial_number]["settings"]

        # Set Acquisition Mode to
        self.cam.AcquisitionMode.SetValue("Continuous")

        # Adjust packet size and delay
        # self.cam.GevSCPSPacketSize.SetValue(self.cam.GevSCPSPacketSize.GetMax())
        # self.cam.GevSCPD.SetValue(0)

        # Camera calibration settings
        for camid, params in config.items():
            if str(camid) == self.serial_number:
                for set in params["settings"]:
                    for k, v in set.items():

                        setting = getattr(self.cam, k)
                        setting.SetValue(v)
                        x = setting.GetValue()
                        print(f"[{self.serial_number}] {k}: {v} -> {x}")

        self.cam.Close()
        self.logger.info(f"Camera {self.alias} configuration complete")

    def retrieve_image(self, max_tries=3, trigger_event=True):
        """
        Capture a single image from the camera.

        :param max_tries: Maximum number of attempts to capture an image.
        :param trigger_event:
        :return: Captured image in OpenCV format, or None if capture fails.
        """
        # self.logger.debug(f"Trying to take picture on camera {self.alias}")

        img_cv2 = None
        tries = 0
        while tries < max_tries:
            if not (self.cam.IsGrabbing()):
                self.logger.info(f"Cannot retrieve image on camera {self.alias}"
                                 f" because it is no grabbing...")
                return None

            self.cam.ExecuteSoftwareTrigger()
            
            try:
                #self.logger.info("Capturing . . .", title=self.alias)
                grab_result = self.cam.RetrieveResult(1000, pylon.TimeoutHandling_Return)
                
                if not (self.cam.IsGrabbing()):
                    self.logger.info(f"Cannot retrieve image on camera {self.alias}"
                                     f" because it is no grabbing...")
                    return None
                if grab_result is not None and grab_result.GrabSucceeded():
                    # Transform the image to proper format.
                    img_cv2 = cv2.cvtColor(grab_result.GetArray(), cv2.COLOR_BayerRG2RGB)

                    break
                else:
                    self.logger.error(
                        f"Couldn't acquire image on camera {self.alias}. Error code: {grab_result.ErrorCode}."
                        f" Error description, {grab_result.ErrorDescription}. \n Retrying {tries + 1}/{max_tries}")
                    
                    grab_result.Release()

                    
            except Exception as ex:
                self.logger.error(f"[{self.alias}] Cannot grab image: {grab_result}. Exception: {ex}")
                tries += 1
        if tries >= max_tries:
            self.logger.warning(f"Impossible to retrieve frames on camera {self.alias} after {max_tries} tries")
            return False, None

        # # ROTATION 
        # rotation_angle = self._config.get(self.serial_number, {}).get("image", {}).get("rotate")

        # if rotation_angle is None:
        #     rotation_angle = self._config.get("common", {}).get("image", {}).get("rotate")

        # # Rotate image based on the specified angle
        # if rotation_angle == 90:
        #     img_cv2 = cv2.rotate(img_cv2, cv2.ROTATE_90_CLOCKWISE)
        # elif rotation_angle == 270:
        #     img_cv2 = cv2.rotate(img_cv2, cv2.ROTATE_90_COUNTERCLOCKWISE)

        # if trigger_event:
        #     self._events.trigger("image", str(self.serial_number), img_cv2)

        return True, img_cv2

    def clean_device(self):
        """
        Release the camera resources and stop the video stream.
        """
        self.stop_streaming()
        self.cam.Close()
        self.logger.info(f"Camera {[self.alias, self.serial_number]} released properly")


if __name__ == "__main__":
    logger = create_logging("logs")  
    basler_config = {
    "40451359": {
        "width": 4096,
        "height": 2048,
        "type": "basler",
        "alias": "Basler Bottom",
        # "camera_ip": "************",  # Uncomment if needed
        "camsetting_file": "backend/config/Line_Bottom_acA4096-11gc_40451359.pfs"
    },
    "40302421": {
        "width": 4096,
        "height": 2048,
        "type": "basler",
        "alias": "Basler Top",
        # "camera_ip": "************",  # Uncomment if needed
        "camsetting_file": "backend/config/Line_Top_acA4096-11gc_40302421.pfs"
    }
}
    cam_test = BaslerCamera(serial_number="40451359", alias="Basler Bottom", config=basler_config, logger=logger)

    exit()