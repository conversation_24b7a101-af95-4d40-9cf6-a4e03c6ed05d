import atexit
import datetime
import threading
from pypylon import genicam
from pypylon import pylon

import numpy as np
# import matplotlib.pyplot as plt
import matplotlib.image

import os
import time
from threading import Lock
import traceback
import cv2

import math

from sewar.full_ref import rmse

from backend.src.camera.camera_abc import AbstractCamera, CameraStatus
from backend.src.utils.data_process import yaml_to_dict

# Allow the import to work when running directly
if __name__ == "__main__":
    from configurationeventprinter import ConfigurationEventPrinter
else:
    from backend.src.camera.Basler.configurationeventprinter import ConfigurationEventPrinter


class CameraException(Exception):
    """Raise for my specific kind of exception"""


class Camera(AbstractCamera):
    roller_diameter = 25e-3
    encoder_pulses_per_revolution = 556

    # Only creates an empty object
    def __init__(self, serial_number, config):

        self.resolution = None
        self.save_config = {}

        tlf = pylon.TlFactory.GetInstance()
        devices = tlf.EnumerateDevices()
        self.cam = None
        for d in devices:
            if d.GetModelName() == "raL4096-24gm":
                print(d.GetModelName(), d.GetSerialNumber())
                self.cam = pylon.InstantCamera(tlf.CreateDevice(d))
                break

        self.reconnect_mutex = None
        self._handlerImage = None
        self.nbLineFrame = None

        if self.cam is None:
            raise RuntimeError("Camera <raL4096-24g> not found !")

        AbstractCamera.__init__(self, serial_number, config)

        self.status = CameraStatus.DISCONNECTED


    # None : default or automatic
    def configure(self, cfg):

        # Every single exemples for pypylon needs to be addapted to work on this camera, use the feature documentation
        # from pylon Viewer to find the correct names for the nodes of this camera.

        config = cfg[self.serial_number]
        self.save_config["modelName"] = config

        self.status = CameraStatus.REACHABLE

        # Not needed to work
        eventPrinter = ConfigurationEventPrinter()
        self.reconnect_mutex = eventPrinter.reconnect_mutex
        self.cam.RegisterConfiguration(eventPrinter, pylon.RegistrationMode_Append, pylon.Cleanup_Delete)
        # Open the camera for setting parameters.
        self.cam.Open()

        # To get consistant results it is always good to start from "power-on" state.
        self.cam.UserSetSelector = "Default"
        self.cam.UserSetLoad.Execute()

        # Auto gain and exposure only works in low light.
        self.cam.GainRaw = config["gain"] if "gain" in config else "Continuous"
        self.cam.ExposureTimeRaw = config["exposureTime"] if "exposureTime" in config else "Continuous"
        self.nbLineFrame = config["nbLineFrame"]
        self.cam.Height.SetValue(self.nbLineFrame)

        width = config["width"] if "width" in config else int(self._camera.WidthMax.Value)
        self.cam.Width.SetValue(width)
        self.cam.Width.SetValue(int(self.cam.WidthMax.Value))

        if config["offset"] is None:
            self.cam.CenterX = True
        elif width is not None:
            self.cam.OffsetX.SetValue(config["offset"])

        # Line configuration :
        self.cam.LineSelector = "Line2"
        self.cam.LineTermination = True
        self.cam.LineSelector = "Line1"
        self.cam.LineTermination = True

        # Shaft encoder module configuration :
        self.cam.ShaftEncoderModuleLineSelector = "PhaseA"
        self.cam.ShaftEncoderModuleLineSource = "Line2"
        self.cam.ShaftEncoderModuleLineSelector = "PhaseB"
        self.cam.ShaftEncoderModuleLineSource = "Line1"
        self.cam.ShaftEncoderModuleMode = "ForwardOnly"
        self.cam.ShaftEncoderModuleCounterMode = "FollowDirection"
        self.cam.ShaftEncoderModuleCounterMax = 32767
        self.cam.ShaftEncoderModuleCounterReset.Execute()
        self.cam.ShaftEncoderModuleReverseCounterMax = 2000
        self.cam.ShaftEncoderModuleReverseCounterReset.Execute()

        # Trigger configuration :
        self.cam.TriggerSelector = "LineStart"
        self.cam.TriggerSource = "ShaftEncoderModuleOut"
        self.cam.TriggerActivation = "RisingEdge"
        self.cam.TriggerMode = "On"

        # To do : Set encoder to a high pulse rate and calculate divider to get a square image.

        # self._camera.FrequencyConverterInputSource="ShaftEncoderModuleOut"
        # self._camera.FrequencyConverterSignalAlignment="RisingEdge"

        self.resolution = (config["widthDistance"] / width)
        # pulses_per_revolution=(2*math.pi*Camera.roller_diameter)/self.resolution
        # multiplier=int(pulses_per_revolution/Camera.roller_pulses_per_revolution)
        # print(multiplier)
        # if multiplier>1:
        #     self._camera.FrequencyConverterMultiplier=multiplier
        #     self._camera.FrequencyConverterPreventOvertrigger=True

        # Jumbo packets
        self.cam.GevSCPSPacketSize = config["packetSize"]

        if config["use_shading"]:
            self.cam.ShadingSelector = "GainShading"
            self.cam.ShadingEnable = True
            self.cam.ShadingSetDefaultSelector = "UserShadingSet1"
            self.cam.ShadingSetSelector = "UserShadingSet1"

        # print("Framerate : ", self._camera.ResultingFrameRateAbs.Value)

        # Creating and registering the ImageEventHandler, that contains the Image Buffer.
        self._handlerImage = ImageEventHandler(self.cam)
        self.cam.RegisterImageEventHandler(
            self._handlerImage, pylon.RegistrationMode_Append, pylon.Cleanup_Delete)

        self.status = CameraStatus.INITIALIZED

        return

    def auto_reconnect_to_Camera(self):
        while True:
            print("autoReconnectToCamera mutex_empty_img_buffer : acquire")
            self.reconnect_mutex.acquire()
            print("autoReconnectToCamera mutex_empty_img_buffer : acquire OK")
            i = 0
            while True:
                print(
                    "!! Warning : Camera disconnected !! \n Reconnecting to camera... \n Atempt ", i)
                i += 1
                try:
                    # Using last configuration
                    self._camera.DestroyDevice()
                    self.configureCamera(self.config)
                    self.StartGrabbing()
                    break
                except:
                    traceback.print_exc()
                    time.sleep(3)
                    continue

    def start_streaming(self):
        try:
            self.cam.StartGrabbing(
                pylon.GrabStrategy_LatestImages,
                pylon.GrabLoop_ProvidedByInstantCamera)
            self.status = CameraStatus.STREAMING
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e

    def stop_streaming(self):
        try:
            self.cam.StopGrabbing()
            self.status = CameraStatus.INITIALIZED
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e

    def retrieve_image(self, max_tries=3):
        try:
            value = self._handlerImage.RetrieveImage()
        except pylon.GenericException as e:
            raise CameraException(str(e)) from e
        return value

    def validate_calibration(self, store_dir=None) -> bool:
        return True

    def exit_handler(self):
        try:
            self.cam.DestroyDevice()
        except:
            pass
        print('Quitting ...')


SIZE_BUFFER = 100


class ImageEventHandler(pylon.ImageEventHandler):
    def __init__(self, cam):
        super().__init__()

        # Index of the frame being writen.
        self.i = 0

        self.lastCompleteImage = -1
        self.lastImageRetrieved = -1

        # Rolling buffer of fixed size : 300 images of 400*4096 in 8 bits : 0,5 GB
        self.img_buffer = np.zeros(
            (SIZE_BUFFER, cam.Height.Value, cam.Width.Value), dtype=np.uint8)
        self.index = np.zeros((SIZE_BUFFER), dtype=int)
        # Mutex for img_buffer
        self.mutex_img_buffer = Lock()

        # Mutex for empty_img_buffer
        self.mutex_empty_img_buffer = Lock()
        self.mutex_empty_img_buffer.acquire()

        self.totalImageCaptured = 0

        # self.t1_image=time.time()
        # self.t2_image=time.time()

        # self.t1_frame=time.time()
        # self.t2_frame=time.time()

    def OnImageGrabbed(self, cam, grabResult):
        """ we get called on every image
            !! this code is run in a pylon thread context
            always wrap your code in the try .. except to capture
            errors inside the grabbing as this can't be properly reported from 
            the background thread to the foreground python code
        """
        # print("CSampleImageEventHandler.OnImageGrabbed called.")
        try:
            if grabResult.GrabSucceeded():
                img = grabResult.Array

                self.mutex_img_buffer.acquire()

                # To do : Add some overlap : reapeat last frame from the previous image

                # Edge cases :
                # Warning if the buffeer is full and overwriting some data. X
                # Warning if the buffer is empty. X
                # Prevent read head from overtaking write head (buffer empty). X
                # Prevent from retrieving an incomplete image. X

                # Add to current image
                # print("Adding to current image : Number : ", self.i, "Range : ", self.ii*cam.Height.Value, ":", (self.ii+1)*cam.Height.Value)
                # print("OnImageGrabbed : self.lastImageRetrieved : ",self.lastImageRetrieved, "self.lastCompleteImage : ", self.lastCompleteImage, "self.i : ", self.i, "self.totalImageCaptured : ", self.totalImageCaptured)
                self.img_buffer[self.i] = img
                self.index[self.i] = self.totalImageCaptured

                # Points read point to the oldest image not overwriten in the buffer
                if self.i == self.lastImageRetrieved:
                    self.lastImageRetrieved = self.i
                    print("Warning : Overwriting images in buffer not retrieved.")

                # If Image is complete :
                if self.i >= 0:
                    rmse_result = rmse(
                        self.img_buffer[self.i], self.img_buffer[self.i - 1])
                    if rmse_result < 3:
                        print("!! Warning : cloth is stuck !!", rmse_result)

                self.lastCompleteImage = self.i
                self.i += 1
                self.i %= SIZE_BUFFER

                self.totalImageCaptured += 1
                # If someone is waiting for the buffer to not be empty, release the lock:
                if self.mutex_empty_img_buffer.locked():
                    self.mutex_empty_img_buffer.release()

                # self.t2_image=time.time()
                # print("Image aqisition time : ",self.t2_image-self.t1_image)
                # self.t1_image=self.t2_image

                self.mutex_img_buffer.release()

                # self.t2_frame=time.time()
                # print("Frame aqisition time : ",self.t2_frame-self.t1_frame)
                # self.t1_frame=self.t2_frame

            else:
                raise RuntimeError("Grab Failed")
        except Exception as e:
            traceback.print_exc()

        return

    def RetrieveImage(self):
        succes = False
        image = None

        self.mutex_img_buffer.acquire()

        # check if an image is available
        if self.lastCompleteImage < 0 or (((self.lastImageRetrieved + 1) % SIZE_BUFFER) == self.i):
            # print("Buffer empty")

            self.mutex_img_buffer.release()

            # If it is not already locked : lock it.
            if not self.mutex_empty_img_buffer.locked():
                # print("RetrieveImage mutex_empty_img_buffer : acquire 1")
                self.mutex_empty_img_buffer.acquire()
                # print("RetrieveImage mutex_empty_img_buffer : acquire 1 OK")

            # Wait for it to be released when a new image is added.
            # print("RetrieveImage mutex_empty_img_buffer : acquire 2", self.mutex_empty_img_buffer.locked())
            if not self.mutex_empty_img_buffer.acquire(timeout=5):
                return None, False, -1
            # print("RetrieveImage mutex_empty_img_buffer : acquire 2 OK")

            self.mutex_img_buffer.acquire()

        # print("RetrieveImage : self.lastImageRetrieved : ",self.lastImageRetrieved, "self.lastCompleteImage : ", self.lastCompleteImage, "self.i : ", self.i)
        self.lastImageRetrieved += 1
        self.lastImageRetrieved %= SIZE_BUFFER
        image = self.img_buffer[self.lastImageRetrieved]
        index = self.index[self.lastImageRetrieved]

        succes = True

        self.mutex_img_buffer.release()

        return image, succes, index


# Use mutex to passively wait for next frame

def start_grabbing(camera, output_folder):
    while True:
        image, success, index = camera.retrieve_image()
        if success:
            # print("image.shape",image.shape)
            # cv2.imshow("IMG", image)
            # cv2.waitKey(0)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            cv2.imwrite(os.path.join(output_folder, timestamp + ".jpg"), image)
        time.sleep(0.5)


# START
if __name__ == "__main__":
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_folder = os.path.join("output/data/", timestamp)
    os.makedirs(output_folder, exist_ok=True)
    configs = dict()
    configs['cameras'] = yaml_to_dict("backend/config/cameras.yaml")

    camera1 = Camera(serial_number="24546596", config=configs['cameras'])

    #camera1.configureCamera(nbLineFrame=1000, exposureTime=2300, use_shading=True)

    print(f"Image size : ({camera1.image_size})")
    print(f"Resolution : {camera1.image_size[0] * camera1.image_size[1] * 1e3} mm/px")
    camera1.start_streaming()

    atexit.register(camera1.exit_handler)

    threading.Thread(target=camera1.auto_reconnect_to_Camera, daemon=True).start()

    start_grabbing(camera1, output_folder)
