from queue import Queue, Empty, Full
import threading
import time
from typing import List, Dict, Union
from backend.src.camera.Hikrobot.line_camera_driver import Camera
from backend.src.camera.Basler.camera_basler     import BaslerCamera
from backend.src.camera.Simulator.camera_simulator import CameraSimulator
from typing import Optional

class CameraManager:
    def __init__(self, logger):
        self.logger = logger
        self.camera_queues: Dict[str, Queue] = {}
        self.stop_threads = False
        self.cameras: List[Union[BaslerCamera, Camera, CameraSimulator]] = []
        self.camera_pause_events: Dict[str, threading.Event] = {}

    def add_camera(self, camera: Union[Camera, BaslerCamera, CameraSimulator]) -> None:
        if not isinstance(camera, (Camera, BaslerCamera, CameraSimulator)):
            raise TypeError("Camera must be either HikRobot Camera or BaslerCamera")

        self.cameras.append(camera)
        # Get serial number based on camera type
        serial_number = camera.serial_number
        
        #self.camera_queues[serial_number] = Queue(maxsize=100)
        self.camera_pause_events[serial_number] = threading.Event()

        self.logger.info(f"Added {camera.__class__.__name__} with ID: {serial_number}")

    def start_image_retrieval(self) -> None:
        """"Start image retrieval for all cameras"""
        for camera in self.cameras:
            try:
                camera.camera_capturing_thread = threading.Thread(
                    target=self._camera_retrival_thread,
                    args=(camera,),
                    daemon=True
                ).start()
                self.logger.info(f"Retrival on camera {camera.__class__.__name__} with ID: {camera.serial_number} started")
            except:
                self.logger.error(f"Failed to start retrieval thread for camera {camera.serial_number}")

    def _camera_retrival_thread(self, camera: Union[Camera, BaslerCamera, CameraSimulator]) -> None:
        """Worker thread to continuously retrieve images for a single camera"""
        serial_number = camera.serial_number if hasattr(camera, 'serial_number') else camera.device_id
        while not self.stop_threads:
            time.sleep(0.01)  # Prevent busy waiting

            #Check if the camera is paused
            if self.check_cameras_thread_is_paused(camera):
                # Pause this camera's retrieval only.
                time.sleep(5)  # or desired pause duration
                self.resume_image_retrival(camera)

            success, image = camera.retrieve_image()
            # self.logger.info("Image retrieved", title=f"{camera.alias}")
            if success:
                try:
                    camera.camera_imgs_queue.put(image, timeout=1)
                    #self.camera_queues[serial_number].put(image, timeout=1)
                except Full:
                    self.logger.warning(
                        f"Queue for camera {serial_number} is full. Dropping frame. [NOT IMPLEMENTED]",
                        title="Image Retrieval"
                    )
            else:
                time.sleep(0.01)
    # def start_image_retrieval(self) -> None:
    #     """Start image retrieval thread"""
    #     threading.Thread(
    #         target=self._image_retrieval_thread,
    #         daemon=True
    #     ).start()

    # def pause_image_retrival(self) -> None:
    #     """Pause event modification"""
    #     self.pause_retrival.set()
    #     self.logger.info("Image retrieval paused.")

    # def clear_image_retrival(self) -> None:
    #     """Clear the image retrieval event"""
    #     self.pause_retrival.clear()
    #     self.logger.info("Image retrieval resumed.")

    def check_cameras_thread_is_paused(self, camera: Union[Camera, BaslerCamera,CameraSimulator]) -> bool:
        """Check if the camera retrieval thread is paused"""
        if camera.serial_number in self.camera_pause_events:
            return self.camera_pause_events[camera.serial_number].is_set()
        return False

    def resume_image_retrival(self, camera: Union[Camera, BaslerCamera, CameraSimulator]) -> None:
        """Clear the pause event for a specific camera"""
        if camera.serial_number in self.camera_pause_events:
            self.camera_pause_events[camera.serial_number].clear()
            self.logger.info(f"Image retrieval for camera {camera.serial_number} resumed at {time.time()}.")



    # def _image_retrieval_thread(self) -> None:
    #     """Thread function to retrieve images from cameras"""
    #     while not self.stop_threads:
    #         for camera in self.cameras:
    #             #Experimental implementation
    #             if self.pause_retrival.is_set():
    #                 time.sleep(10)
    #                 self.clear_image_retrival()
    #             success, image = camera.retrieve_image()
    #             if success:
    #                 try:
    #                     self.camera_queues[camera.serial_number].put(image, timeout=1)
    #                 except Full:
    #                     self.logger.warning(
    #                         f"Queue for camera {camera.serial_number} is full. Dropping frame.",
    #                         title="Image Retrieval"
    #                     )
    #             else:
    #                 time.sleep(0.01)

    def stop_all_cameras(self) -> None:
        """Stop all cameras"""
        self.stop_threads = True
        for camera in self.cameras:
            camera.stop_streaming()
            print(f"Camera {camera.serial_number} stopped streaming.")

 