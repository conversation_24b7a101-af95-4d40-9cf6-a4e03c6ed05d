from typing import Dict, Optional, Any, List
from backend.src.camera_manager.camera_manager import CameraManager
import time

class CameraSetupManager:
    """Handles camera initialization and setup configurations"""
    
    def __init__(self, cameras_config: Dict, logger):
        self.cam_config = cameras_config
        self.logger = logger
        self.camera_manager = CameraManager(logger)
    
    @staticmethod
    def create_camera(camera_type: str, serial_number: str, alias: Optional[str] = None, 
                    config: Dict[str, Any] = None, logger=None):
        """
        Factory function to create the appropriate camera instance
        
        Args:
            camera_type: Type of camera (basler, hikrobot, line, simulator)
            serial_number: Serial number of the camera
            alias: Optional alias for the camera
            config: Configuration dictionary
            logger: Logger instance
        
        Returns:
            Instantiated camera object
        """
        try:
            if camera_type.lower() == "simulator":
                from backend.src.camera.Simulator.camera_simulator import CameraSimulator
                return CameraSimulator(serial_number, config)
                
            elif camera_type.lower() == "basler":
                from backend.src.camera.Basler.camera_basler import BaslerCamera
                return BaslerCamera(serial_number=serial_number, alias=alias, config=config, logger=logger)
                
            elif camera_type.lower() == "line":
                from backend.src.camera.Hikrobot.line_camera_driver import Camera
                return Camera(serial_number=serial_number, alias=alias, config=config, logger=logger)
        
        except ImportError as e:
            if logger:
                logger.error(f"Failed to import camera module for {camera_type}: {str(e)}")
            raise
        except Exception as e:
            if logger:
                logger.error(f"Failed to create camera of type {camera_type}: {str(e)}")
            raise


    def setup_camera_system(
        self, 
        mode: str,
        selected_cameras: str = ""
    ) -> CameraManager:
        
        try:
            # Parse selected cameras
            if selected_cameras == "all":
                selected_cam_list = ["all"]
            else:
                selected_cam_list = selected_cameras.split(",")
            
            cameras_loaded = False
            
            # Handle legacy mode-specific setup
            if mode in ["line", "basler", "simulator"]:
                cameras_loaded = self._setup_mode_specific_cameras(mode, selected_cam_list)
            elif mode == "all":
                # Try to load all types of cameras
                for camera_mode in ["line", "basler"]:
                    mode_cameras_loaded = self._setup_mode_specific_cameras(camera_mode, selected_cam_list)
                    cameras_loaded = cameras_loaded or mode_cameras_loaded
            else:
                raise ValueError(f"Unsupported camera mode: {mode}")
                
            if not cameras_loaded:
                self.logger.warning(f"No cameras were loaded in {mode} mode", title="Setup")
                
            return self.camera_manager
            
        except Exception as e:
            self.logger.error(f"Failed to setup cameras: {str(e)}", title="Setup")
            self.camera_manager.stop_all_cameras()
            raise

    def _setup_mode_specific_cameras(self, mode: str, selected_cameras: List[str]) -> bool:
            """
            Set up cameras of a specific mode
            
            Args:
                mode: Camera mode (line, regular, simulator)
                selected_cameras: List of selected camera serials or ["all"]
                
            Returns:
                bool: True if any cameras were loaded, False otherwise
            """
            cameras_loaded = False
         
            # First check if there's a specific section for this mode
            if mode in self.cam_config and isinstance(self.cam_config[mode], dict):
                mode_config = self.cam_config[mode]
                for serial_number, camera_details in mode_config.items():
                    if "all" in selected_cameras or serial_number in selected_cameras:
                        camera_type = camera_details.get("type", mode)
                        alias = camera_details.get("alias")
                        difference = camera_details.get("difference")
                        sharpness = camera_details.get("sharpness")
                        gain = camera_details.get("gain")
                        deviation = camera_details.get("deviation")
                        low_tuple = camera_details.get("low_tuple")
                        high_tuple = camera_details.get("high_tuple")
                        
                        # Default values for optional parameters
                        crop_left = camera_details.get("crop_left", 0)
                        crop_right = camera_details.get("crop_right", 0)
                        self.logger.info(f"Connecting {camera_type} camera {serial_number}", title="Connection")
                        
                        try:
                            self._connect_camera(camera_type, serial_number, alias, mode_config, difference, sharpness, gain, deviation, low_tuple, high_tuple, crop_left, crop_right)
                            cameras_loaded = True
                        except Exception as e:
                            self.logger.error(f"Failed to connect camera {serial_number}: {str(e)}", title="Connection")
            return cameras_loaded

    def _connect_camera(
        self,
        camera_type: str,
        serial_number: str,
        alias: Optional[str],
        camera_configs: Dict,
        difference: Optional[int],
        sharpness: None,
        gain: Optional[float],
        deviation: Optional[float],
        low_tuple: Optional[str],
        high_tuple: Optional[str],
        crop_left: int,
        crop_right: int,
        max_retries: Optional[int] = 3
    ) -> None:
        """Attempt to connect to a camera with retries"""
        
        if camera_type.lower() == "simulator":
            # If a whole block was passed, cut it down to the entry for this serial.
            if serial_number in camera_configs and "images_path" not in camera_configs:
                driver_cfg = camera_configs[serial_number]
            else:                                   # already a single-cam dict
                driver_cfg = camera_configs
        else:
            driver_cfg = camera_configs      
        
        for attempt in range(max_retries):
            try:
                camera = CameraSetupManager.create_camera(
                    camera_type,
                    serial_number,
                    alias,
                    driver_cfg,
                    self.logger
                )
                # Attach the difference value if provided
                if difference is not None:
                    try:
                        camera.difference = int(difference)
                    except ValueError:
                        self.logger.error(f"Invalid difference value '{difference}' for camera {serial_number}", title="Connection")
                        raise
                if sharpness is not None:
                    try:
                        camera.sharpness = int(sharpness)
                    except ValueError:
                        self.logger.error(f"Invalid sharpness value '{sharpness}' for camera {serial_number}", title="Connection")
                        raise
                if gain is not None:
                    try:
                        camera.gain = float(gain)
                    except ValueError:
                        self.logger.error(f"Invalid gain value '{gain}' for camera {serial_number}", title="Connection")
                        raise           
                if deviation is not None:
                    try:
                        camera.deviation = float(deviation)
                    except ValueError:
                        self.logger.error(f"Invalid deviation value '{deviation}' for camera {serial_number}", title="Connection")
                        raise         
                if low_tuple is not None:
                    try:
                        # Permitir tanto string "0,0,0" como lista [0,0,0]
                        if isinstance(low_tuple, str):
                            camera.low_tuple = tuple(map(int, low_tuple.split(',')))
                        elif isinstance(low_tuple, (list, tuple)):
                            camera.low_tuple = tuple(low_tuple)
                        else:
                            camera.low_tuple = None
                    except Exception:
                        self.logger.error(f"Invalid low_tuple value '{low_tuple}' for camera {serial_number}", title="Connection")
                        raise  
                else:
                    camera.low_tuple = None

                if high_tuple is not None:
                    try:
                        if isinstance(high_tuple, str):
                            camera.high_tuple = tuple(map(int, high_tuple.split(',')))
                        elif isinstance(high_tuple, (list, tuple)):
                            camera.high_tuple = tuple(high_tuple)
                        else:
                            camera.high_tuple = None
                    except Exception:
                        self.logger.error(f"Invalid high_tuple value '{high_tuple}' for camera {serial_number}", title="Connection")
                        raise  
                else:
                    camera.high_tuple = None
                camera.crop_left = crop_left
                camera.crop_right = crop_right
                self.camera_manager.add_camera(camera)
                camera.start_streaming()
                self.logger.info(f"Camera connected: {serial_number}", title="Connection")
                return
                
            except Exception as e:
                self.logger.warning(
                    f"Camera connection attempt {attempt + 1}/{max_retries}",
                    title="Connection"
                )
                self.logger.error(str(e), title="Connection")
                if attempt < max_retries - 1:
                    time.sleep(3)
        
        raise ConnectionError(
            f"Failed to connect camera {serial_number} after {max_retries} attempts"
        )
