import numpy as np
import cv2
import logging
logger = logging.getLogger(__name__)

def detect_stocking(image, threshold=15):
    """
    Detect stocking presence in the image based on pixel intensity variance.
    Args:
        image (np.array): Input image.
        threshold (int): Threshold for variance to determine stocking presence.
    
    Returns:
        bool: True if stocking is detected, False otherwise.
    """
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # Calculate the standard deviation of pixel intensity to detect changes
    std_intensity = np.std(gray)
    return std_intensity > threshold

def combine_images(image_list):
    """
    Vertically concatenate images from the list to form a full stocking image.
    Args:
        image_list (list of np.array): List of images to be concatenated.
    
    Returns:
        np.array: Combined image.
    """
    if len(image_list) > 0:
        image_full = cv2.vconcat(image_list)
        image_full = cv2.rotate(image_full, cv2.ROTATE_90_CLOCKWISE)

        cv2.imwrite("./full_stocking.jpg", image_full)
        return image_full 
    return None

def get_stocking_mask(image):
    """
    Create a mask for the stocking, excluding the background.
    """
    # Convert the image to HSV color space for better color range filtering
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Define the range for dark background (e.g., black, dark gray)
    lower_dark_background = np.array([0, 0, 0])  # Black/dark range
    upper_dark_background = np.array([180, 255, 50])  # Dark gray range
    
    # Define the range for blue background (e.g., navy, blue)
    lower_blue_background = np.array([90, 50, 50])  # Lower bound for blue hues
    upper_blue_background = np.array([130, 255, 255])  # Upper bound for blue hues
    
    # Create a mask for dark background
    dark_mask = cv2.inRange(hsv_image, lower_dark_background, upper_dark_background)
    
    # Create a mask for blue background
    blue_mask = cv2.inRange(hsv_image, lower_blue_background, upper_blue_background)
    
    # Combine both masks (dark + blue) to isolate the background
    combined_background_mask = cv2.bitwise_or(dark_mask, blue_mask)
    
    # Invert the combined mask to focus on the stocking (non-background areas)
    stocking_mask = cv2.bitwise_not(combined_background_mask)
    
    return stocking_mask

def extract_main_stocking_color(image):
    """
    Extract the main color of the stocking by masking out dark and blue backgrounds.
    Args:
        image (np.array): Input image.
    
    Returns:
        str: Average color of the stocking as a hex string (e.g., "#RRGGBB").
    """
    # Convert the image to HSV color space for better color range filtering
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Define the range for dark background (e.g., black, dark gray)
    lower_dark_background = np.array([0, 0, 0])  # Black/dark range
    upper_dark_background = np.array([180, 255, 50])  # Dark gray range
    
    # Define the range for blue background (e.g., navy, blue)
    lower_blue_background = np.array([90, 50, 50])  # Lower bound for blue hues
    upper_blue_background = np.array([130, 255, 255])  # Upper bound for blue hues
    
    # Create a mask for dark background
    dark_mask = cv2.inRange(hsv_image, lower_dark_background, upper_dark_background)
    
    # Create a mask for blue background
    blue_mask = cv2.inRange(hsv_image, lower_blue_background, upper_blue_background)
    
    # Combine both masks (dark + blue) to isolate the background
    combined_background_mask = cv2.bitwise_or(dark_mask, blue_mask)
    
    # Invert the combined mask to focus on the stocking (non-background areas)
    stocking_mask = cv2.bitwise_not(combined_background_mask)
    
    # Apply the mask to the original image to keep only stocking pixels
    stocking_pixels = cv2.bitwise_and(image, image, mask=stocking_mask)
    
    # Extract non-zero (non-background) pixels
    non_zero_pixels = stocking_pixels[np.where(stocking_mask != 0)]
    
    # If no stocking pixels are detected, return a default color (black)
    if len(non_zero_pixels) == 0:
        return '#000000'
    
    # Calculate the average color of the stocking (BGR format)
    average_color_bgr = np.mean(non_zero_pixels, axis=0)
    
    # Convert BGR to hex string format
    hex_color = '#{:02x}{:02x}{:02x}'.format(int(average_color_bgr[2]), int(average_color_bgr[1]), int(average_color_bgr[0]))
    
    return hex_color


