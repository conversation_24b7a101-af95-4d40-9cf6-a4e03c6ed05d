import numpy as np
import os
import cv2
import json
import logging
import time
from datetime import datetime
from concurrent import futures
import traceback
from queue import Empty
from collections import deque
import threading
from typing import Dict

#from backend.src.stockings_analysis.detection_criteria_basler import detect_stocking_bas_bottom    
from backend.src.stockings_analysis.stocking_image_processor import get_stocking_mask, extract_main_stocking_color, combine_images, detect_stocking
from backend.src.utils.files_manager import create_stocking_folder
from backend.src.motion_detection.motion_detection import YOLO_Predictor
from backend.src.utils.data_process import get_timestamp
from backend.src.utils.sock_id_manager import manager as sock_id_manager
from backend.src.network.ui_bridge import emit_sock_event
from datetime import datetime
# Import socketio from ui_bridge to avoid circular imports
from backend.src.network.ui_bridge import socketio
from backend.config.servo_optimization_config import apply_preset
from backend.src.network.ui_bridge import push_new_result


logger = logging.getLogger(__name__)
RESULTS_ROOT = "/mnt/data/projects/defis-medical/user-interfaces/medical/images"
SNAP_DIRNAME = "static/output"              # dated folder will be added later
sock_event_lock = threading.Lock()

partial_results: Dict[str, Dict[str, dict]] = {}
partial_lock = threading.Lock()          # protects partial_results

# Adjust current number of pre and post lines to save around the event
FRAME_SIZE = 200
POST_LINES = int(FRAME_SIZE * 1.2)*4
PRE_LINES = int(FRAME_SIZE * 1.2)*4

# Delay in seconds before returning servo to middle position after movement
DELAY_SERVO = 2.0

# Import image processing configuration
from backend.config.image_processing_config import (
    FRONTEND_IMAGE_SCALE_FACTOR,
    SAVE_ORIGINAL_IMAGES_ASYNC,
    FRONTEND_IMAGE_QUALITY,
    FULL_RESOLUTION_IMAGE_QUALITY
)

# Import servo optimization configuration
from backend.config.servo_optimization_config import (
    EXIT_HYSTERESIS_MULTIPLIER,
    SERVO_DELAY_WARNING_THRESHOLD,
    STEP_DELAY_WARNING_THRESHOLD,
    AI_IMAGE_RESIZE_FACTOR,
    ENABLE_AI_OPTIMIZATION,
    SKIP_MASK_EXTRACTION,
    SKIP_COLOR_EXTRACTION
)

DELAY_SERVO = 1.0  # seconds

# Global variable to share top/bottom events between threads
sock_events: Dict[int, Dict[str, float]] = {}

# Global variable to track analysis results for servo control
analysis_results: Dict[int, Dict[str, any]] = {}

# Global variable to track servo states per sock_id
servo_states: Dict[int, str] = {}

# Global variable to track timing information for performance analysis
timing_data: Dict[int, Dict[str, float]] = {}

# Global variable to track critical timing milestones for servo delay analysis
servo_timing_milestones: Dict[int, Dict[str, float]] = {}


class PerformanceTimer:
    """
    Context manager for timing operations and logging performance data.
    """
    def __init__(self, operation_name: str, sock_id: int = None, camera_alias: str = None):
        self.operation_name = operation_name
        self.sock_id = sock_id
        self.camera_alias = camera_alias
        self.start_time = None
        self.end_time = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time

        # Log timing information
        if self.sock_id and self.camera_alias:
            logger.info(f"[TIMING] {self.operation_name} for sock_id={self.sock_id}, camera={self.camera_alias}: {duration:.3f}s")

            # Store timing data globally for analysis
            if self.sock_id not in timing_data:
                timing_data[self.sock_id] = {}
            timing_key = f"{self.camera_alias}_{self.operation_name}"
            timing_data[self.sock_id][timing_key] = duration
        else:
            logger.info(f"[TIMING] {self.operation_name}: {duration:.3f}s")


def _build_and_emit_combined_result(sock_id: str, sock_folder: str) -> None:
    """
    Merge the two side-results already stored in `partial_results[sock_id]`,
    create ONE combined JSON, push it to the UI and write it to disk.
    """
    with partial_lock:
        sides = partial_results.pop(sock_id, {})   # remove to avoid re-use

    top_res    = sides.get("top")
    bottom_res = sides.get("bottom")
    if not (top_res and bottom_res):
        logger.warning(f"[MERGE] Incomplete data for sock_id={sock_id}")
        return

    # ——— Combine ———
    combined = top_res  # shallow copy is fine; we’ll mutate a few keys
    combined["defects"] += bottom_res["defects"]        # concat defects
    combined["decision"] = (
        "NOK" if combined["defects"] else "OK"
    )  # very simple rule

    # use *visualisation* for top & bottom
    combined["images"] = {
        "top":    top_res["images"]["bottom"],     # we saved vis in "bottom"
        "bottom": bottom_res["images"]["bottom"],
    }

    # ——— Persist + emit ———
    timestamp = get_timestamp()
    out_path = os.path.join(sock_folder, f"{timestamp}_sock_{sock_id}_result.json")
    with open(out_path, "w") as f:
        json.dump(combined, f, indent=4)

    push_new_result(combined)                     # one single UI event
    logger.info(f"[MERGE] Combined result stored & pushed for sock_id={sock_id}")

def log_timing_summary(sock_id: int):
    """
    Log a comprehensive timing summary for a specific sock_id.
    """
    if sock_id not in timing_data:
        return

    sock_timing = timing_data[sock_id]
    total_time = sum(sock_timing.values())

    logger.info(f"[TIMING SUMMARY] sock_id={sock_id} - Total processing time: {total_time:.3f}s")

    # Sort operations by duration (slowest first)
    sorted_operations = sorted(sock_timing.items(), key=lambda x: x[1], reverse=True)

    for operation, duration in sorted_operations:
        percentage = (duration / total_time) * 100 if total_time > 0 else 0
        logger.info(f"[TIMING SUMMARY]   {operation}: {duration:.3f}s ({percentage:.1f}%)")

    # Clean up timing data
    del timing_data[sock_id]


def record_servo_milestone(sock_id: int, milestone: str, camera_alias: str = None):
    """
    Record critical timing milestones for servo delay analysis.
    """
    global servo_timing_milestones

    if sock_id not in servo_timing_milestones:
        servo_timing_milestones[sock_id] = {}

    current_time = time.time()
    milestone_key = f"{camera_alias}_{milestone}" if camera_alias else milestone
    servo_timing_milestones[sock_id][milestone_key] = current_time

    logger.info(f"[SERVO MILESTONE] sock_id={sock_id} - {milestone_key} at {current_time:.3f}")


def log_servo_delay_analysis(sock_id: int):
    """
    Analyze and log the complete timeline from event detection to servo movement.
    """
    if sock_id not in servo_timing_milestones:
        logger.warning(f"[SERVO DELAY] No timing milestones found for sock_id={sock_id}")
        return

    milestones = servo_timing_milestones[sock_id]

    # Find the earliest event detection time
    event_start_time = None
    for key, timestamp in milestones.items():
        if "event_detected" in key:
            if event_start_time is None or timestamp < event_start_time:
                event_start_time = timestamp

    if event_start_time is None:
        logger.warning(f"[SERVO DELAY] No event detection milestone found for sock_id={sock_id}")
        return

    # Find servo movement time
    servo_movement_time = milestones.get("servo_final_decision", None)

    if servo_movement_time is None:
        logger.warning(f"[SERVO DELAY] No servo movement milestone found for sock_id={sock_id}")
        return

    total_delay = servo_movement_time - event_start_time

    logger.info(f"[SERVO DELAY ANALYSIS] sock_id={sock_id} - Total delay from event to servo: {total_delay:.3f}s")

    # Calculate delay from Bottom camera specifically for physical correlation
    bottom_event_time = milestones.get("Line Bottom_event_detected", None)
    if bottom_event_time and servo_movement_time:
        bottom_to_servo_delay = servo_movement_time - bottom_event_time
        logger.info(f"[PHYSICAL CORRELATION] sock_id={sock_id} - Delay from Bottom camera detection to servo: {bottom_to_servo_delay:.3f}s")
        logger.info(f"[PHYSICAL CORRELATION] This should correlate with physical timing measurements")

    # Log detailed timeline
    sorted_milestones = sorted(milestones.items(), key=lambda x: x[1])

    for milestone, timestamp in sorted_milestones:
        relative_time = timestamp - event_start_time
        logger.info(f"[SERVO DELAY ANALYSIS]   +{relative_time:.3f}s: {milestone}")

    # Analyze specific delays using configurable thresholds
    if total_delay > SERVO_DELAY_WARNING_THRESHOLD:
        logger.warning(f"[SERVO DELAY WARNING] sock_id={sock_id} - Excessive delay detected: {total_delay:.3f}s (threshold: {SERVO_DELAY_WARNING_THRESHOLD}s)")

        # Check for specific bottlenecks
        for i, (milestone, timestamp) in enumerate(sorted_milestones[:-1]):
            next_milestone, next_timestamp = sorted_milestones[i + 1]
            step_delay = next_timestamp - timestamp

            if step_delay > STEP_DELAY_WARNING_THRESHOLD:
                logger.warning(f"[SERVO DELAY BOTTLENECK] sock_id={sock_id} - Slow step: {milestone} -> {next_milestone}: {step_delay:.3f}s (threshold: {STEP_DELAY_WARNING_THRESHOLD}s)")

    # Clean up milestone data
    del servo_timing_milestones[sock_id]

SIM_MODE = os.getenv("SIMULATOR_MODE") == "1"
if not SIM_MODE:
    logger.info("Running real mode with arduino")
    import serial
    try:
        arduino = serial.Serial(port='/dev/ttyUSB0',
                                baudrate=115200,
                                timeout=0.1)
    except Exception as e:
        # fall back safely even on real hardware if the port is missing
        arduino = None
        logger.warning(f"Could not open Arduino serial port: {e}")
else:
    logger.info("Running in simulator mode without arduino")
    arduino = None     # never touch hardware in simulator mode


class DifferenceTracker:
    def __init__(self, windows_size: int, threshold: float, sharpness_threshold=None, deviation_threshold=None, low_tuple_threshold=None, high_tuple_threshold=None, logger=None, camera_alias=None):
        self.history = deque(maxlen=windows_size)
        self.sharpness_history = deque(maxlen=windows_size)
        # Robust handling for threshold
        if threshold is not None and str(threshold).lower() != "none":
            self.threshold = int(threshold)
        else:
            self.threshold = None
        if sharpness_threshold is not None and str(sharpness_threshold).lower() != "none":
            self.sharpness_threshold = int(sharpness_threshold)
        else:
            self.sharpness_threshold = None
        if deviation_threshold is not None and str(deviation_threshold).lower() != "none":
            self.deviation_threshold = float(deviation_threshold)
        else:
            self.deviation_threshold = None
        # Robust handling for low_tuple_threshold
        if low_tuple_threshold is not None and str(low_tuple_threshold).lower() != "none":
            if isinstance(low_tuple_threshold, str):
                self.low_tuple = tuple(map(int, low_tuple_threshold.split(',')))
            elif isinstance(low_tuple_threshold, (list, tuple)):
                self.low_tuple = tuple(low_tuple_threshold)
            else:
                self.low_tuple = None
        else:
            self.low_tuple = None

        # Robust handling for high_tuple_threshold
        if high_tuple_threshold is not None and str(high_tuple_threshold).lower() != "none":
            if isinstance(high_tuple_threshold, str):
                self.high_tuple = tuple(map(int, high_tuple_threshold.split(',')))
            elif isinstance(high_tuple_threshold, (list, tuple)):
                self.high_tuple = tuple(high_tuple_threshold)
            else:
                self.high_tuple = None
        else:
            self.high_tuple = None

        self.logger = logger
        self.camera_alias = camera_alias
        self.last_print_time = 0
        self.window_size = windows_size
        self.trigger_start = None
        # For tracking max bg_dev
        self.max_bg_dev = 0.0
        self.max_diff = 0.0
        self.max_sharpness = 0.0
        # Track historical maximums
        self.historic_max_bg_dev = 0.0
        self.historic_max_diff = 0.0
        self.historic_max_sharpness = 0.0
        self.max_metrics_reset_time = time.time()
        
    def compute_line_average(self) -> float:
        if not self.history:
            return 0.0
        return sum(self.history) / len(self.history)

    def compute_sharpness(self, frame: np.ndarray) -> float:
        # Use the variance of the Laplacian as a sharpness measure
        return cv2.Laplacian(frame, cv2.CV_64F).var()

    
    def compute_bg_deviation(self, frame: np.ndarray, lower, upper) -> float:
        """
        Compute the fraction of pixels outside the given background range.
        Converts the frame to RGB and uses inRange for the acceptable region.
        """
        # If the limits are not defined, deviation cannot be calculated
        if lower is None or upper is None:
            return 0.0
        # Assume input frame is in BGR
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        lower_arr = np.array(lower, dtype=np.uint8)
        upper_arr = np.array(upper, dtype=np.uint8)
        mask = cv2.inRange(rgb, lower_arr, upper_arr)
        # Count pixels not in the acceptable range (mask==0)
        deviation = np.mean(mask == 0)
        return float(deviation)


    def update(self, difference: float, frame=None) -> bool:
        self.history.append(difference)
        current_time = time.time()

        # Track max difference only if threshold is set
        if self.threshold is not None and difference > self.max_diff:
            self.max_diff = difference
        if self.threshold is not None and difference > self.historic_max_diff:
            self.historic_max_diff = difference

        sharpness_value = None
        if frame is not None and self.sharpness_threshold is not None:
            sharpness_value = self.compute_sharpness(frame)
            self.sharpness_history.append(sharpness_value)
            # Track max sharpness only if threshold is set
            if sharpness_value > self.max_sharpness:
                self.max_sharpness = sharpness_value
            if sharpness_value > self.historic_max_sharpness:
                self.historic_max_sharpness = sharpness_value

        bg_dev = None
        if frame is not None and self.low_tuple is not None and self.high_tuple is not None and self.deviation_threshold is not None:
            bg_dev = self.compute_bg_deviation(frame, self.low_tuple, self.high_tuple)
            if bg_dev > self.max_bg_dev:
                self.max_bg_dev = float(bg_dev)
            if bg_dev > self.historic_max_bg_dev:
                self.historic_max_bg_dev = bg_dev

        if len(self.history) < self.window_size:
            if current_time - self.last_print_time >= 2:
                msg = f"Filling buffer: {len(self.history)}/{self.window_size} values collected..."
                if self.logger:
                    self.logger.info(msg, camera_alias=self.camera_alias)
                else:
                    print(msg)
                self.last_print_time = current_time
            self.trigger_start = None  # reset any previous trigger
            return False

        # Log mean values every 2 seconds
        if current_time - self.last_print_time >= 5:
            msg = ""
            if self.threshold is not None and self.history:
                mean_diff = self.compute_line_average()
                msg = f"Mean diff history: {mean_diff:.1f} Using threshold: {self.threshold}"
            if self.sharpness_threshold is not None and self.sharpness_history:
                mean_sharp = sum(self.sharpness_history) / len(self.sharpness_history)
                # if msg:
                #     msg += f" | Mean sharpness: {mean_sharp:.1f} Using sharpness threshold: {self.sharpness_threshold}"
                # else:
                #     msg = f"Mean sharpness: {mean_sharp:.1f} Using sharpness threshold: {self.sharpness_threshold}"
            if frame is not None and self.low_tuple is not None and self.high_tuple is not None:
                bg_dev = self.compute_bg_deviation(frame, self.low_tuple, self.high_tuple)
                if self.deviation_threshold is not None:
                    deviation_str = f"{float(self.deviation_threshold):.2f}"
                else:
                    deviation_str = "-"
                if msg:
                    msg += f" | Deviation Threshold: {deviation_str}| BG Deviation: {bg_dev:.2f}"
                else:
                    msg = f"Deviation Threshold: {deviation_str}| BG Deviation: {bg_dev:.2f}"
            if msg:
                if self.logger:
                    self.logger.info(msg, camera_alias=self.camera_alias)
                else:
                    print(msg)
            self.last_print_time = current_time
        seconds_max_value = 10
        # Log max values every x seconds, only if their respective thresholds are set
        if current_time - self.max_metrics_reset_time >= seconds_max_value:
            max_parts = []
            if self.threshold is not None:
                max_parts.append(f"Diff={self.max_diff:.2f} (historic={self.historic_max_diff:.2f})")
            # if self.sharpness_threshold is not None:
            #     max_parts.append(f"Sharpness={self.max_sharpness:.2f} (historic={self.historic_max_sharpness:.2f})")
            if self.deviation_threshold is not None:
                max_parts.append(f"BG Deviation={self.max_bg_dev:.4f} (historic={self.historic_max_bg_dev:.4f})")
            if max_parts:
                max_msg = f"MAXS (last {seconds_max_value}s): " + " ".join(max_parts)
                if self.logger:
                    self.logger.info(max_msg, camera_alias=self.camera_alias)
                else:
                    print(max_msg)
            self.max_bg_dev = 0.0
            self.max_diff = 0.0
            self.max_sharpness = 0.0
            self.max_metrics_reset_time = current_time

        # Determine if trigger condition is met
        condition_met = False
        if self.threshold is not None:
            condition_met = (self.compute_line_average() > self.threshold)
        condition_met = condition_met or (
            self.sharpness_threshold is not None and 
            self.sharpness_history and 
            (sum(self.sharpness_history) / len(self.sharpness_history)) > self.sharpness_threshold
        )
        # Trigger if more than X% of pixels are not in the blue background range
        if frame is not None and self.low_tuple is not None and self.high_tuple is not None and self.deviation_threshold is not None:
            bg_dev = self.compute_bg_deviation(frame, self.low_tuple, self.high_tuple)
            condition_met = condition_met or (bg_dev > self.deviation_threshold)

        if condition_met:
            if self.trigger_start is None:
                self.trigger_start = current_time
            if current_time - self.trigger_start >= 0:  # Event lasts at least 2 seconds
                return True
            return False
        else:
            self.trigger_start = None
            return False

def crop_line_by_pixels(line, crop_left: int, crop_right: int):
    _, w = line.shape[:2]
    return line[:, crop_left:w - crop_right]

# -------------------------------------------------------------------------------
# Focus metric (variance of Laplacian) for object sharpness only after event
# -------------------------------------------------------------------------------
def compute_focus_metric(frame): 
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    lap = cv2.Laplacian(gray, cv2.CV_64F)
    return float(lap.var())

def send_command(command_id: str):
    """Send 'l' or 'r' to the servo controller, unless in simulator mode."""
    command_start_time = time.time()
    logger.info(f"[SERVO TIMING] Sending command '{command_id}' at {command_start_time}")

    if arduino is None:        # covers both SIM_MODE and missing HW
        logger.info(f"[SERVO TIMING] Simulator mode - command '{command_id}' sent instantly")
        return None

    try:
        arduino.write(bytes(command_id, 'utf-8'))
        time.sleep(0.1)
        response = arduino.readline().decode('utf-8').strip()
        command_sent_time = time.time()
        command_duration = command_sent_time - command_start_time
        logger.info(f"[SERVO TIMING] Command '{command_id}' sent successfully at {command_sent_time}, took {command_duration:.3f}s")
        logger.info(f"[SERVO TIMING] Arduino response: '{response}'")
        logger.info(f"[SERVO TIMING] Physical servo movement should occur within 0.1-0.5s after this timestamp")
        return response
    except Exception as e:
        logger.error(f"Error sending command to servo: {e}")
        return None


def analyse(images, pixel_ratio, ob_predictor, sock_id=None, camera_alias=None):

    # Add timing to identify bottlenecks in analysis
    analyse_start_time = time.time()
    logger.info(f"[{camera_alias}] analyse() function started for sock_id={sock_id} at {analyse_start_time}")

    # Emit analysis start event to UI
    try:
        emit_sock_event(sock_id, camera_alias, 'analysis_start')
    except Exception as e:
        logger.warning(f"Failed to emit analysis start event: {e}")

    # Define classes for defect types
    classes = ["hole", "strips", "stain", "line", "knots", "fiber", "surface", "elastic"]

    # Result structure: list of defects with type and area
    result = []
    images_out = []
    save_images = False  # Flag to determine whether to save images

    if ob_predictor is not None:
        for idx, image in enumerate(images):
            # Optimize image size for AI processing
            original_image = image
            if AI_IMAGE_RESIZE_FACTOR != 1.0:
                with PerformanceTimer("image_resize", sock_id, camera_alias):
                    h, w = image.shape[:2]
                    new_h, new_w = int(h * AI_IMAGE_RESIZE_FACTOR), int(w * AI_IMAGE_RESIZE_FACTOR)
                    image = cv2.resize(image, (new_w, new_h))
                    logger.info(f"[AI OPTIMIZATION] Resized image from {w}x{h} to {new_w}x{new_h} (factor: {AI_IMAGE_RESIZE_FACTOR})")

            image_out = original_image  # Keep original for output

            # Extract the foreground mask (to filter out detections on the background)
            if not SKIP_MASK_EXTRACTION:
                with PerformanceTimer("mask_extraction", sock_id, camera_alias):
                    stocking_mask = get_stocking_mask(image)
            else:
                # Skip mask extraction for speed - assume entire image is foreground
                stocking_mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
                logger.info(f"[AI OPTIMIZATION] Skipped mask extraction for speed")

            # Extract the main color of the stocking
            if not SKIP_COLOR_EXTRACTION:
                with PerformanceTimer("color_extraction", sock_id, camera_alias):
                    avg_stocking_color = extract_main_stocking_color(image)
            else:
                # Skip color extraction for speed
                avg_stocking_color = [128, 128, 128]  # Default gray color
                logger.info(f"[AI OPTIMIZATION] Skipped color extraction for speed")
            
            with PerformanceTimer("defect_detection", sock_id, camera_alias):
                with futures.ThreadPoolExecutor() as executor:
                    # Record AI model start time
                    ai_start_time = time.time()
                    thread_OB = executor.submit(ob_predictor.predict, image)
                    try:
                        det = thread_OB.result()  # Get detection result
                        ai_end_time = time.time()
                        ai_duration = ai_end_time - ai_start_time
                        logger.info(f"[AI MODEL TIMING] sock_id={sock_id}, camera={camera_alias}: AI prediction took {ai_duration:.3f}s")
                        image_out = det["visualization"]  # Update output image with visualization
                        images_out.append(image_out)

                        # Iterate over detected bounding boxes and classes
                        with PerformanceTimer("bbox_processing", sock_id, camera_alias):
                            for bbox, cls in zip(det['boxes'], det['classes']):
                                bbox = bbox[0]

                                # Filter the bounding box to ensure it lies within the stocking (foreground)
                                if is_in_foreground(bbox, stocking_mask):
                                    defect_type = cls  # Class of defect
                                    width_mm = (bbox[2] - bbox[0]) * pixel_ratio
                                    height_mm = (bbox[3] - bbox[1]) * pixel_ratio
                                    area = width_mm * height_mm / 100

                                    # Append each detected defect with its type and area
                                    result.append({
                                        "type": defect_type,  # Use class name for defect type
                                        "area": area
                                    })

                                    save_images = True  # If defects are detected, mark for saving images

                    except Exception as e:
                        logger.error(f"Error during analysis: {e}")
                        logger.error(traceback.format_exc())

    # Log completion of analyse function
    analyse_end_time = time.time()
    analyse_duration = analyse_end_time - analyse_start_time
    logger.info(f"[{camera_alias}] analyse() function completed for sock_id={sock_id} at {analyse_end_time}, took {analyse_duration:.3f}s")

    # Emit analysis complete event to UI
    try:
        emit_sock_event(sock_id, camera_alias, 'analysis_complete')
    except Exception as e:
        logger.warning(f"Failed to emit analysis complete event: {e}")

    return result, images_out, save_images, avg_stocking_color

def downscale_image(image: np.ndarray, scale_factor: float = 0.25) -> np.ndarray:
    """
    Downscale an image by the given scale factor.

    Args:
        image: Input image as numpy array
        scale_factor: Scale factor (0.25 = 25% of original size)

    Returns:
        Downscaled image as numpy array
    """
    if scale_factor >= 1.0:
        return image

    height, width = image.shape[:2]
    new_width = int(width * scale_factor)
    new_height = int(height * scale_factor)

    # Use INTER_AREA for downscaling (best quality for size reduction)
    downscaled = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    return downscaled


def save_image_async(image_path: str, image: np.ndarray, quality: int = None):
    """
    Save an image asynchronously using threading with optional quality setting.

    Args:
        image_path: Path where to save the image
        image: Image data as numpy array
        quality: JPEG quality (1-100), uses FULL_RESOLUTION_IMAGE_QUALITY if None
    """
    if quality is None:
        quality = FULL_RESOLUTION_IMAGE_QUALITY

    def _save_image():
        try:
            # Use JPEG quality if the file is a JPEG
            if image_path.lower().endswith(('.jpg', '.jpeg')):
                cv2.imwrite(image_path, image, [cv2.IMWRITE_JPEG_QUALITY, quality])
            else:
                cv2.imwrite(image_path, image)
            logger.debug(f"Async image saved: {image_path} (quality: {quality})")
        except Exception as e:
            logger.error(f"Failed to save image asynchronously: {image_path}, error: {e}")

    # Start the save operation in a separate thread
    thread = threading.Thread(target=_save_image)
    thread.daemon = True  # Don't block program exit
    thread.start()


def save_image_with_quality(image_path: str, image: np.ndarray, quality: int):
    """
    Save an image with specified JPEG quality.

    Args:
        image_path: Path where to save the image
        image: Image data as numpy array
        quality: JPEG quality (1-100)
    """
    try:
        if image_path.lower().endswith(('.jpg', '.jpeg')):
            cv2.imwrite(image_path, image, [cv2.IMWRITE_JPEG_QUALITY, quality])
        else:
            cv2.imwrite(image_path, image)
        logger.debug(f"Image saved: {image_path} (quality: {quality})")
    except Exception as e:
        logger.error(f"Failed to save image: {image_path}, error: {e}")


def pseudo_expose(image: np.ndarray, value: int = 50) -> np.ndarray:
    """
    Simulate increased exposure by increasing brightness uniformly.

    Args:
        image (np.ndarray): Input image (line).
        value (int): Value to add to brightness (0–255).

    Returns:
        np.ndarray: Brightness-adjusted image.
    """
    image_float = image.astype(np.float32)
    image_bright = np.clip(image_float * value, 0, 255).astype(np.uint8)
    return image_bright


def is_in_foreground(bbox, stocking_mask):
    """
    Check if a bounding box lies mostly within the stocking (foreground).
    Args:
        bbox: Bounding box coordinates.
        stocking_mask: The mask of the stocking (foreground).
    
    Returns:
        bool: True if the bounding box is in the foreground, False otherwise.
    """
    x_min, y_min, x_max, y_max = bbox
    bbox_mask = stocking_mask[int(y_min):int(y_max), int(x_min):int(x_max)]
    
    # Check if most of the pixels in the bounding box are part of the foreground
    if np.mean(bbox_mask) > 0.5:  # 50% or more of the bounding box should overlap with the foreground
        return True
    return False

def accumulate_results(result, timestamp, images, images_out, defects_found, stocking_folder, avg_stocking_color, sock_id=None, camera_alias=None, broadcast: bool = True):
    """
    Save analysis results and images, associating them with a unique sock_id and camera_alias.
    """
    with PerformanceTimer("result_accumulation", sock_id, camera_alias):
        src_image_paths = []
        vis_image_paths = []
        decision = "OK" if not defects_found else "NOK"

        # Save source images
        with PerformanceTimer("save_source_images", sock_id, camera_alias):
            for i, image in enumerate(images):
                fname = f"{timestamp}_ID_{sock_id}_camera_{camera_alias}_{i}_stocking-src.jpg" if sock_id and camera_alias else f"{timestamp}_{i}_stocking-src.jpg"
                src_image_path = os.path.join(stocking_folder, fname)
                rotated_image = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)

                if SAVE_ORIGINAL_IMAGES_ASYNC:
                    # Save full resolution image asynchronously
                    save_image_async(src_image_path, rotated_image)
                else:
                    # Save synchronously (original behavior)
                    cv2.imwrite(src_image_path, rotated_image)

                src_image_paths.append(src_image_path.split("/medical/")[1])

        # Save visualization images if defects are found
        if defects_found:
            with PerformanceTimer("save_visualization_images", sock_id, camera_alias):
                for i, image_out in enumerate(images_out):
                    # Rotate the image first
                    rotated_image_out = cv2.rotate(image_out, cv2.ROTATE_90_CLOCKWISE)

                    # Save full resolution visualization image asynchronously (if enabled)
                    if SAVE_ORIGINAL_IMAGES_ASYNC:
                        fname_full = f"{timestamp}_ID_{sock_id}_camera_{camera_alias}_{i}_stocking-vis-full.jpg" if sock_id and camera_alias else f"{timestamp}_{i}_stocking-vis-full.jpg"
                        vis_image_path_full = os.path.join(stocking_folder, fname_full)
                        save_image_async(vis_image_path_full, rotated_image_out)
                        logger.info("image path %s", vis_image_path_full)

                    # Create downscaled version for frontend
                    downscaled_vis_image = downscale_image(rotated_image_out, FRONTEND_IMAGE_SCALE_FACTOR)

                    # Save downscaled visualization image (synchronously for immediate frontend use)
                    fname = f"{timestamp}_ID_{sock_id}_camera_{camera_alias}_{i}_stocking-vis.jpg" if sock_id and camera_alias else f"{timestamp}_{i}_stocking-vis.jpg"
                    vis_image_path = os.path.join(stocking_folder, fname)
                    save_image_with_quality(vis_image_path, downscaled_vis_image, FRONTEND_IMAGE_QUALITY)
                    vis_image_paths.append(vis_image_path.split("/medical/")[1])
                    logger.info("Visimage path %s", vis_image_path_full)
                    logger.info(f"[IMAGE DOWNSCALE] Visualization image downscaled from {rotated_image_out.shape[:2]} to {downscaled_vis_image.shape[:2]} (factor: {FRONTEND_IMAGE_SCALE_FACTOR}, quality: {FRONTEND_IMAGE_QUALITY})")

    # Prepare the result data structure
    datetime_timestamp = datetime.strptime(timestamp, "%Y-%m-%d_%H-%M-%S")
    date_timestamp = datetime_timestamp.strftime("%d-%m-%Y %H:%M:%S")
    result_data = {
        "info": {
            "request_id": timestamp,
            "sock_id": sock_id,
            "camera": camera_alias,
            "product": "STOCKING",
            "date": date_timestamp,
            "color": avg_stocking_color
        },
        "product_info": {
            "Leg Length": "-- cm",
            "Ankle Width": "-- cm",
            "Panty Length": "-- cm"
        },
        "defects": [
            [defect["type"].capitalize(), f"{defect['area']:.2f} mm²", "--"]
            for defect in result
        ],
        "decision": decision,
        "images": {
            "top": src_image_paths[0],
            "bottom": vis_image_paths[0] if vis_image_paths else src_image_paths[0]
        }
    }

        # Save the result as a JSON file in the stocking folder
    with PerformanceTimer("save_json_result", sock_id, camera_alias):
        json_name = (
            f"{timestamp}_ID_{sock_id}_camera_{camera_alias}_result.json"
            if sock_id and camera_alias
            else f"{timestamp}_result.json"
        )
        json_path = os.path.join(stocking_folder, json_name)
        with open(json_path, "w") as f:
            json.dump(result_data, f, indent=4)

    # ⬇⬇  broadcast only if requested
    #if broadcast:
    #    push_new_result(result_data)

    return result_data


# def servo_flap (defects_found):
#     """
#     Activate the servo flap based on the defects found.
#     If defects are found, the servo is activated to the right.
#     If not, to the left.
#     """
#     if defects_found:
#         send_command('r')
#     else:
#         send_command('l')


def initialize_servo_for_sock_id(sock_id):
    """
    Initialize servo to middle position for a new sock_id.
    This ensures the servo is in the middle position until analysis is complete.
    """
    global servo_states
    servo_states[sock_id] = 'm'
    send_command('m')
    logger.info(f"Servo initialized to middle position for sock_id={sock_id}")


def process_analysis_result(sock_id, camera_alias, defects_found):
    """
    Process analysis result for a specific camera and sock_id.
    Stores the result and checks if both cameras have completed analysis.
    If both are complete, makes the final servo decision.
    """
    global analysis_results, servo_states

    # Initialize analysis_results for this sock_id if not exists
    if sock_id not in analysis_results:
        analysis_results[sock_id] = {}

    # Store the analysis result for this camera
    analysis_results[sock_id][camera_alias.lower()] = {
        'defects_found': defects_found,
        'timestamp': time.time()
    }

    logger.info(f"Analysis result stored for sock_id={sock_id}, camera={camera_alias}, defects_found={defects_found}")

    # Check if both top and bottom cameras have completed analysis (including early analysis)
    sock_results = analysis_results[sock_id]
    has_top = any('top' in camera for camera in sock_results.keys())
    has_bottom = any('bottom' in camera for camera in sock_results.keys())

    if arduino and (has_top and has_bottom):
        # Both cameras have completed analysis, make final decision
        record_servo_milestone(sock_id, "both_cameras_complete")
        make_final_servo_decision(sock_id)


def make_final_servo_decision(sock_id):
    """
    Make the final servo decision based on combined results from both cameras.
    If either camera found defects, send 'r' (reject), otherwise send 'l' (accept).
    After movement, wait for object to fall, then return servo to middle.
    """
    global analysis_results, servo_states

    # Record milestone for final decision start
    record_servo_milestone(sock_id, "final_decision_start")

    sock_results = analysis_results[sock_id]

    # Check if any camera found defects
    any_defects = False
    for camera_data in sock_results.values():
        if camera_data['defects_found']:
            any_defects = True
            break

    # Record milestone before servo command
    record_servo_milestone(sock_id, "servo_command_send")

    # Send appropriate servo command
    if any_defects:
        send_command('r')
        servo_states[sock_id] = 'r'
        logger.info(f"Final decision for sock_id={sock_id}: REJECT (defects found)")
        logger.info(f"[PHYSICAL TIMING] SERVO ACTIVATED - STOP PHYSICAL TIMER NOW!")
        decision = 'REJECT'
    else:
        send_command('l')
        servo_states[sock_id] = 'l'
        logger.info(f"Final decision for sock_id={sock_id}: ACCEPT (no defects)")
        logger.info(f"[PHYSICAL TIMING] SERVO ACTIVATED - STOP PHYSICAL TIMER NOW!")
        decision = 'ACCEPT'

    # Emit servo decision event to UI
    try:
        emit_sock_event(sock_id, 'SERVO', 'servo_decision', time.time())
        # Also emit with decision details
        socketio.emit('servo_decision', {
            'sock_id': sock_id,
            'decision': decision,
            'defects_found': any_defects,
            'timestamp': time.time(),
            'formatted_time': datetime.fromtimestamp(time.time()).strftime("%H:%M:%S.%f")[:-3]
        })
    except Exception as e:
        logger.warning(f"Failed to emit servo decision event: {e}")

    # Record milestone for servo movement complete
    record_servo_milestone(sock_id, "servo_final_decision")

    # Schedule return to middle position after delay
    threading.Timer(DELAY_SERVO, return_servo_to_middle, args=[sock_id]).start()





def perform_immediate_full_analysis(
    image,
    sock_id: str,
    camera_alias: str,
    pixel_ratio: float,
    ob_predictor,
    results_folder: str,
):
    """
    Analyse one side (Top/Bottom), save artefacts, stash partial result.
    When both sides are present, build + emit a single combined JSON.
    """
    try:
        if not sock_id:
            return

        side = "top" if "top" in camera_alias.lower() else "bottom"
        logger.info(f"[{camera_alias}] Immediate analysis for sock_id={sock_id}, side={side}")

        # -------- ensure folder --------
        sock_folder = os.path.join(results_folder, f"sock_{sock_id}")
        os.makedirs(sock_folder, exist_ok=True)

        # -------- run analysis --------
        with PerformanceTimer("immediate_full_analysis", sock_id, camera_alias):
            result, images_out, defects_found, avg_color = analyse(
                [image], pixel_ratio, ob_predictor, sock_id, camera_alias
            )

        # -------- save artefacts (NO broadcast yet) --------
        ts = get_timestamp()
        partial = accumulate_results(
            result,
            ts,
            [image],
            images_out,
            defects_found,
            sock_folder,
            avg_color,
            sock_id=sock_id,
            camera_alias=f"{camera_alias}_immediate",
            broadcast=False,                  # ← suppress ui event
        )

        # -------- stash & maybe merge --------
        with partial_lock:
            partial_results.setdefault(sock_id, {})[side] = partial
            ready = len(partial_results[sock_id]) == 2

        if ready:
            _build_and_emit_combined_result(sock_id, sock_folder)

        # servo bookkeeping unchanged
        process_analysis_result(sock_id, f"{camera_alias}_immediate", defects_found)

    except Exception:
        logger.exception(f"[{camera_alias}] Immediate analysis failed for sock {sock_id}")


def return_servo_to_middle(sock_id):
    """
    Return servo to middle position after object has fallen.
    Clean up tracking data for this sock_id.
    """
    global analysis_results, servo_states, sock_events

    send_command('m')
    logger.info(f"Servo returned to middle position for sock_id={sock_id}")

    # Log timing summary before cleanup
    log_timing_summary(sock_id)

    # Log servo delay analysis before cleanup
    log_servo_delay_analysis(sock_id)

    # Clean up tracking data
    if sock_id in analysis_results:
        del analysis_results[sock_id]
    if sock_id in servo_states:
        del servo_states[sock_id]
    if sock_id in sock_events:
        del sock_events[sock_id]

    logger.info(f"Cleaned up tracking data for sock_id={sock_id}")

def get_frames(logger, configs, stop_threads, camera_manager=None, results_folder=None, ob_predictor=None):    
    # Maintain separate lists for each camera
    # Experimental implementation
    camera_queues = camera_manager.camera_queues
    current_stockings = {}
    stocking_assembled = {}
    first_image_names = {}

    try:
        pixel_ratio = configs["REAL_WIDTH"] / configs["IMAGE_SIZE"][0]
    except:
        pixel_ratio = 1

    while not stop_threads:
        # Create a copy of the keys to iterate safely
        camera_ids = list(camera_queues.keys())
        
        # Process each camera queue
        for camera_id in camera_ids:
            if camera_id not in camera_queues:
                continue  # Queue may have been removed
                
            queue = camera_queues[camera_id]
            try:
                image = queue.get(timeout=0.1)  # Short timeout to check other queues
            except Empty:
                continue  # Try next queue
                
            # Initialize tracking data for this camera if needed
            if camera_id not in current_stockings:
                current_stockings[camera_id] = []
                stocking_assembled[camera_id] = False
                first_image_names[camera_id] = None
                
            # Process the image for this specific camera
            # detection = True
            # if detection == True:
            # if detect_stocking_bas_bottom(image):  # Temporarily commented out
                # camera_manager.pause_image_retrival()
                print("PAUSE")
            # if detect_stocking(image):
                if first_image_names[camera_id] is None:
                    first_image_names[camera_id] = get_timestamp()
                    logger.info(f"New stocking begins on camera {camera_id}: {first_image_names[camera_id]}")
                
                current_stockings[camera_id].append(image)
                
                # Save individual frames
                os.makedirs(f"./individual_frames/{camera_id}", exist_ok=True)
                static_counter = getattr(get_frames, f"image_counter_{camera_id}", 0) + 1
                setattr(get_frames, f"image_counter_{camera_id}", static_counter)
                individual_path = os.path.join(f"./individual_frames/{camera_id}", f"image_{static_counter}.jpg")
                cv2.imwrite(individual_path, image)
                logger.info(f"Saved individual frame from camera {camera_id}: {individual_path}", 
                          title="Image Save")
                
            elif current_stockings[camera_id]:
                # Process completed stocking for this camera
                logger.info(f"Stocking finished on camera {camera_id}, sending to analysis")
                full_stocking_image = combine_images(current_stockings[camera_id])

                os.makedirs(f"./individual_frames/{camera_id}", exist_ok=True)
                stocking_name = f"full_stocking"
                stocking_index = 0
                stocking_path = os.path.join(f"./individual_frames/{camera_id}", f"{stocking_name}.jpg")

                # Check if file exists, increment counter until finding an available name
                while os.path.exists(stocking_path):
                    stocking_index += 1    #

                stocking_assembled[camera_id] = True
                current_stockings[camera_id] = []
                first_image_names[camera_id] = None
                
                # Process the assembled stocking
                if stocking_assembled[camera_id]:
                    timestamp = get_timestamp()
                    stocking_folder = create_stocking_folder(timestamp, results_folder)
                    # Analysis and result accumulation
                    result, images_out, defects_found, avg_color = analyse([full_stocking_image], pixel_ratio, ob_predictor)
                    stocking_result = accumulate_results(result, timestamp, [full_stocking_image], 
                                                      images_out, defects_found, stocking_folder, avg_color)
                    logger.info(f"Analysis finished for camera {camera_id}")
                    logger.info(stocking_result)
                    stocking_assembled[camera_id] = False
            
        time.sleep(0.01)  # Small delay to prevent tight loop

# Contador global para generar identificadores únicos
global_event_counter = 0

def generate_unique_sock_id():
    """Generates a unique identifier for each event."""
    global global_event_counter
    global_event_counter += 1
    return f"{get_timestamp()}_{global_event_counter}"

def get_frames_single_cam(
    camera,
    logger,
    configs,
    stop_threads,
    results_folder=None,
    ob_predictor=None,
):
    camera_alias  = camera.alias          # e.g. “Line Top” / “Line Bottom”
    alias_lower   = camera_alias.lower()
    is_top_cam    = alias_lower.endswith("top")
    is_bottom_cam = alias_lower.endswith("bottom")

    # ------------------------------------------------------------------ init
    diff_tracker = DifferenceTracker(
        windows_size        = FRAME_SIZE,
        threshold           = camera.difference,
        deviation_threshold = camera.deviation,
        sharpness_threshold = camera.sharpness,
        low_tuple_threshold = camera.low_tuple,
        high_tuple_threshold= camera.high_tuple,
        logger              = logger,
        camera_alias        = camera_alias,
    )

    try:
        pixel_ratio = configs["REAL_WIDTH"] / configs["IMAGE_SIZE"][0]
    except Exception:
        pixel_ratio = 1

    pre_buffer         = deque(maxlen=PRE_LINES)
    saved_lines        = []
    recording          = False
    below_thr_cnt      = 0
    immediate_done     = False
    exit_hysteresis    = FRAME_SIZE * EXIT_HYSTERESIS_MULTIPLIER
    frame_counter      = 0
    last_heartbeat     = time.time()
    current_sock_id    = None             # will be assigned inside event start

    queue = camera.camera_imgs_queue

    # ----------------------------------------------------------------- loop
    while not stop_threads:
        try:
            frame = queue.get(timeout=0.1)
        except Empty:
            continue

        frame_counter += 1
        now = time.time()
        if now - last_heartbeat >= 2.0:
            #logger.info("[%s] %d frames processed", camera_alias, frame_counter)
            last_heartbeat = now

        h, _ = frame.shape[:2]
        for y in range(h):
            line = frame[y:y+1, :, :]
            line = crop_line_by_pixels(line, camera.crop_left, camera.crop_right)
            line = pseudo_expose(line, value=camera.gain)

            gray = cv2.cvtColor(line, cv2.COLOR_BGR2GRAY)
            diff = float(np.sum(np.abs(gray - gray.mean())))
            trigger = diff_tracker.update(diff, frame=line)

            pre_buffer.append(line.copy())

            # -------------------------------  EVENT START  ------------------
            if trigger and not recording:
                recording       = True
                below_thr_cnt   = 0
                immediate_done  = False
                saved_lines     = list(pre_buffer)

                now_ts = now
                side   = "top" if is_top_cam else "bottom"

                if is_top_cam:
                    # ---- TOP always starts a brand-new sock_id
                    with sock_event_lock:
                        current_sock_id           = sock_id_manager.generate_unique_sock_id()
                        sock_events[current_sock_id] = {"top": now_ts}
                else:
                    # ---- BOTTOM must *attach* to the most-recent top-only event
                    with sock_event_lock:
                        attach_id = None
                        for sid, times in sorted(
                            ((sid, t) for sid, t in sock_events.items() if "top" in t and "bottom" not in t),
                            key=lambda x: x[1]["top"],
                            reverse=True,
                        ):
                            attach_id = sid
                            break

                        if attach_id is None:
                            # No matching TOP → pairing failed; drop this event
                            #logger.warning("[%s] Bottom saw object but no open "
                            #               "top event exists - discarding lines",
                            #               camera_alias)
                            recording = False
                            saved_lines.clear()
                            pre_buffer.clear()
                            continue

                        current_sock_id                 = attach_id
                        sock_events[attach_id]["bottom"] = now_ts

                logger.info("[%s] %s event for sock_id=%s",
                            camera_alias, side.capitalize(), current_sock_id)

                # ---- servo midpoint & milestones
                record_servo_milestone(current_sock_id, "event_detected", camera_alias)
                record_servo_milestone(current_sock_id, "servo_init_start", camera_alias)
                initialize_servo_for_sock_id(current_sock_id)
                record_servo_milestone(current_sock_id, "servo_init_complete", camera_alias)
                logger.info("[%s] %s event for sock_id=%s",
                            camera_alias,
                            "Top" if is_top_cam else "Bottom",
                            current_sock_id)

                # Emit sock detection event to UI
                try:
                    emit_sock_event(current_sock_id, camera_alias, 'detected')
                except Exception as e:
                    logger.warning(f"Failed to emit sock detection event: {e}")

            # -------------------------------  EVENT BODY  -------------------
            if recording:
                saved_lines.append(line.copy())
                if not trigger:
                    below_thr_cnt += 1

                # early full analysis
                if (
                    not immediate_done
                    and below_thr_cnt >= FRAME_SIZE
                    and len(saved_lines) >= FRAME_SIZE * 2
                ):
                    immediate_done = True
                    record_servo_milestone(current_sock_id,
                                           "immediate_full_analysis_trigger",
                                           camera_alias)
                    full_img = np.vstack(saved_lines)
                    threading.Thread(
                        target=perform_immediate_full_analysis,
                        args=(full_img,
                              current_sock_id,
                              camera_alias,
                              pixel_ratio,
                              ob_predictor,
                              results_folder),
                        daemon=True,
                    ).start()

                # exit hysteresis not yet reached
                if below_thr_cnt < exit_hysteresis:
                    continue

                # -------------------------  EVENT END  ----------------------
                full_img = np.vstack(saved_lines)
                record_servo_milestone(current_sock_id, "event_end", camera_alias)

                # if early analysis never ran, do it now (blocking)
                if not immediate_done:
                    perform_immediate_full_analysis(
                        full_img,
                        current_sock_id,
                        camera_alias,
                        pixel_ratio,
                        ob_predictor,
                        results_folder,
                    )

                

                # reset for next event
                recording = False
                saved_lines.clear()
                pre_buffer.clear()
                below_thr_cnt = 0
                immediate_done = False

# On program exit, save the last identifier
def save_sock_id_on_exit():
    sock_id_manager.save_current_id()

def get_cameras_frames_parallel(logger, configs, stop_threads, camera_manager, results_folder, ob_predictor):
    # """
    # For each camera queue in camera_manager, spawn a dedicated thread to process frames.
    # """
    # threads = [] 
    # for camera_id, queue in camera_manager.camera_queues.items():
    #     t = threading.Thread(
    #         target=get_frame_single_cam,
    #         args=(camera_id, camera_manager.camera_pause_events, queue, logger, configs, stop_threads, results_folder, ob_predictor),
    #         daemon=True
    #     )
    #     t.start()
    #     threads.append(t)
    """
    For each camera queue in camera_manager, spawn a dedicated thread to process frames.
    """
    # image_processing_threads = [] 
    # for camera in camera_manager.cameras:
    #     #serial_number = camera.serial_number 
    #     #camera_thread = camera_manager.camera_pause_events[serial_number]
    #     #camera_queues = camera_manager.camera_queues[serial_number]
    #     t = threading.Thread(
    #         target=get_frames_single_cam,
    #         args=(camera, logger, configs, stop_threads, results_folder, ob_predictor),
    #         daemon=True
    #     )

    #     t.start()
    #     image_processing_threads.append(t)
    threads = []
    for camera in camera_manager.cameras:
        t = threading.Thread(
            target=get_frames_single_cam,
            args=(camera, logger, configs, stop_threads, results_folder, ob_predictor),
            daemon=True
        )
        t.start()
        threads.append(t)