import torch
from torch.nn import functional as F
import numpy as np
import warnings

warnings.filterwarnings('ignore')


def cal_anomaly_map(fs_list, ft_list, out_size=224, amap_mode='mul'):
    if amap_mode == 'mul':
        anomaly_map = np.ones([out_size, out_size])
    else:
        anomaly_map = np.zeros([out_size, out_size])
    a_map_list = []
    for i in range(len(ft_list)):
        fs = fs_list[i]
        ft = ft_list[i]
        ft = F.interpolate(ft, size=fs.shape[-1], mode='bilinear', align_corners=True)
        fs = F.interpolate(fs, size=ft.shape[-1], mode='bilinear', align_corners=True)
        # fs_norm = F.normalize(fs, p=2)
        # ft_norm = F.normalize(ft, p=2)
        a_map = 1 - F.cosine_similarity(fs, ft)
        a_map = torch.unsqueeze(a_map, dim=1)
        a_map = F.interpolate(a_map, size=out_size, mode='bilinear', align_corners=True)
        a_map = a_map[0, 0, :, :].to('cpu').detach().numpy()
        a_map_list.append(a_map)
        if amap_mode == 'mul':
            anomaly_map *= a_map
        else:
            anomaly_map += a_map
    return anomaly_map, a_map_list


def min_max_norm(image):
    """
    Normalize a numpy array image to the range [0, 1].

    Parameters:
    - image: numpy array of the image.

    Returns:
    - Normalized numpy array of the image.
    """
    a_min, a_max = image.min(), image.max()
    return (image - a_min) / (a_max - a_min)
