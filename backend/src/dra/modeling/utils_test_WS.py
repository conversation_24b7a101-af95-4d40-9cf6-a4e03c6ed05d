import torch
from torch.nn import functional as F
import cv2
import numpy as np
from numpy import ndarray
import pandas as pd
from sklearn.metrics import roc_auc_score, auc, roc_curve
from skimage import measure
from statistics import mean
from scipy.ndimage import gaussian_filter
import warnings
import os
import matplotlib.pyplot as plt
from tqdm import tqdm



warnings.filterwarnings('ignore')
def cal_anomaly_map(fs_list, ft_list, out_size=224, amap_mode='mul'):
    if amap_mode == 'mul':
        anomaly_map = np.ones([out_size, out_size])
    else:
        anomaly_map = np.zeros([out_size, out_size])
    a_map_list = []
    for i in range(len(ft_list)):
        fs = fs_list[i]
        ft = ft_list[i]
        ft = F.interpolate(ft, size=fs.shape[-1], mode='bilinear', align_corners=True)
        fs = F.interpolate(fs, size=ft.shape[-1], mode='bilinear', align_corners=True)
        #fs_norm = F.normalize(fs, p=2)
        #ft_norm = F.normalize(ft, p=2)
        a_map = 1 - F.cosine_similarity(fs, ft)
        a_map = torch.unsqueeze(a_map, dim=1)
        a_map = F.interpolate(a_map, size=out_size, mode='bilinear', align_corners=True)
        a_map = a_map[0, 0, :, :].to('cpu').detach().numpy()
        a_map_list.append(a_map)
        if amap_mode == 'mul':
            anomaly_map *= a_map
        else:
            anomaly_map += a_map
    return anomaly_map, a_map_list

def show_cam_on_image(img, anomaly_map):
    if anomaly_map.shape != img.shape:
        anomaly_map = cv2.applyColorMap(np.uint8(anomaly_map), cv2.COLORMAP_JET)
    cam = np.float32(anomaly_map)/255 + np.float32(img)/255
    cam = cam / np.max(cam)
    return np.uint8(255 * cam)

def min_max_norm(image):
    a_min, a_max = image.min(), image.max()
    return (image-a_min)/(a_max - a_min)

def cvt2heatmap(gray):
    heatmap = cv2.applyColorMap(np.uint8(gray), cv2.COLORMAP_JET)
    return heatmap

def evaluation_multi_proj(encoder, proj, bn, decoder, dataloader, device, th=0.5, save_path=None, eval_mode=True):
    encoder.eval()
    proj.eval()
    bn.eval()
    decoder.eval()
    gt_list_px = []
    pr_list_px = []
    gt_list_sp = []
    pr_list_sp = []
    aupro_list = []

    tp, tn, fp, fn = 0, 0, 0, 0
    with torch.no_grad():
        for idx, data in tqdm(enumerate(dataloader), total=len(dataloader), desc='Evaluation'):
            print(f"Processing data {idx}...")
            img = data[0][0].to(device)  # Accessing the first tensor in the list and moving it to the device
            gt = data[0][1].to(device)   # Accessing the second tensor in the list and moving it to the device
            img_original = data[0][5]     # Accessing the sixth tensor in the list (assuming it contains the original image)

            patch_size = 224  
            stride = 32  
            anomaly_maps = []
            for i in range(0, img.shape[2] - patch_size + 1, stride):
                for j in range(0, img.shape[3] - patch_size + 1, stride):
                    img_patch = img[:, :, i:i+patch_size, j:j+patch_size]
                    inputs = encoder(img_patch)
                    features = proj(inputs)
                    outputs = decoder(bn(features))
                    anomaly_map, _ = cal_anomaly_map(inputs, outputs, patch_size, amap_mode='a')
                    anomaly_map = gaussian_filter(anomaly_map, sigma=4)
                    anomaly_maps.append(anomaly_map)
                    print(f"Anomaly map shape: {anomaly_map.shape}")

            if len(anomaly_maps) == 0:
                print("Warning: No anomaly maps generated for this data point.")
                continue

            anomaly_map_aggregated = np.zeros_like(anomaly_maps[0])
            for anomaly_map in anomaly_maps:
                anomaly_map_aggregated += anomaly_map
            anomaly_map_aggregated /= len(anomaly_maps)  

            _, thresh = cv2.threshold(anomaly_map_aggregated, th, 1, cv2.THRESH_BINARY)

            print(f"Data {idx} processed successfully.")

            if eval_mode:
                gt_list_px.extend(gt.cpu().numpy().astype(int).ravel())
                pr_list_px.extend(anomaly_map_aggregated.ravel())
                gt_list_sp.append(np.max(gt.cpu().numpy().astype(int)))
                pr_list_sp.append(np.max(anomaly_map_aggregated))

                if 1.0 in np.unique(thresh):
                    tp += 1
                else:
                    fn += 1

            # Save the image with anomaly highlighted
            if save_path is not None:
                for i in range(img.shape[0]):
                    save_image_with_transparent_anomaly_overlay(img_original[i], thresh[i], save_path, f"data_{idx}_{i}")

        if eval_mode:
            # Convert ground truth to binary (0 or 1)
            gt_list_sp_binary = np.where(np.array(gt_list_sp) > 0.5, 1, 0)
            fpr, tpr, thresholds_sorted = roc_curve(gt_list_sp_binary, pr_list_sp, pos_label=1)

            if save_path is not None:
                roc_save_path = os.path.join(save_path, "results")
                os.makedirs(roc_save_path, exist_ok=True)  # Ensure the directory exists

                # Plot and save the ROC curve
                plt.figure()
                plt.plot(fpr, tpr, label='ROC Curve')
                
                # Annotate specific points with the corresponding threshold values
                # Here we're annotating every 5th point to avoid clutter
                for i in range(0, len(thresholds_sorted), 5):
                    plt.annotate(f'{thresholds_sorted[i]:.2f}', (fpr[i], tpr[i]), textcoords="offset points", xytext=(2, -10))
                
                plt.xlabel('False Positive Rate')
                plt.ylabel('True Positive Rate')
                plt.title('ROC Curve')
                plt.legend(loc="lower right")
                
                # Save the figure
                plt.savefig(os.path.join(roc_save_path, 'roc_curve_with_thresholds.png'))
                plt.close()

            auroc_px = round(roc_auc_score(gt_list_px, pr_list_px), 4)
            auroc_sp = round(roc_auc_score(gt_list_sp, pr_list_sp), 4)
            return auroc_px, auroc_sp, round(np.mean(aupro_list), 4)
        else:
            return 0, 0, 0

# def evaluation_multi_proj(encoder, proj, bn, decoder, dataloader, device, th=0.5, save_path=None, eval_mode=True):
#     encoder.eval()
#     proj.eval()
#     bn.eval()
#     decoder.eval()
#     gt_list_px = []
#     pr_list_px = []
#     gt_list_sp = []
#     pr_list_sp = []
#     aupro_list = []

#     tp, tn, fp, fn = 0, 0, 0, 0
#     with torch.no_grad():
#         for idx, data in tqdm(enumerate(dataloader), total=len(dataloader), desc='Evaluation'):
#             print(f"Processing data {idx}...")
#             img = data[0][0].to(device)  # Accessing the first tensor in the list and moving it to the device
#             gt = data[0][1].to(device)   # Accessing the second tensor in the list and moving it to the device
#             img_original = data[0][5]     # Accessing the sixth tensor in the list (assuming it contains the original image)

#             patch_size = 224  
#             stride = 32  
#             anomaly_maps = []
#             for i in range(0, img.shape[2] - patch_size + 1, stride):
#                 for j in range(0, img.shape[3] - patch_size + 1, stride):
#                     img_patch = img[:, :, i:i+patch_size, j:j+patch_size]
#                     inputs = encoder(img_patch)
#                     features = proj(inputs)
#                     outputs = decoder(bn(features))
#                     anomaly_map, _ = cal_anomaly_map(inputs, outputs, patch_size, amap_mode='a')
#                     anomaly_map = gaussian_filter(anomaly_map, sigma=4)
#                     anomaly_maps.append(anomaly_map)
#                     print(f"Anomaly map shape: {anomaly_map.shape}")

#             if len(anomaly_maps) == 0:
#                 print("Warning: No anomaly maps generated for this data point.")
#                 continue

#             anomaly_map_aggregated = np.zeros_like(anomaly_maps[0])
#             for anomaly_map in anomaly_maps:
#                 anomaly_map_aggregated += anomaly_map
#             anomaly_map_aggregated /= len(anomaly_maps)  

#             _, thresh = cv2.threshold(anomaly_map_aggregated, th, 1, cv2.THRESH_BINARY)

#             print(f"Data {idx} processed successfully.")

#             if eval_mode:
#                 gt_list_px.extend(gt.cpu().numpy().astype(int).ravel())
#                 pr_list_px.extend(anomaly_map_aggregated.ravel())
#                 gt_list_sp.append(np.max(gt.cpu().numpy().astype(int)))
#                 pr_list_sp.append(np.max(anomaly_map_aggregated))

#                 if 1.0 in np.unique(thresh):
#                     tp += 1
#                 else:
#                     fn += 1

#             # Save the image with anomaly highlighted
#             if save_path is not None:
#                 for i in range(img.shape[0]):
#                     save_image_with_transparent_anomaly_overlay(img_original[i], thresh[i], save_path, f"data_{idx}_{i}")

#         if eval_mode:
#             # Convert ground truth to binary (0 or 1)
#             gt_list_sp_binary = np.where(np.array(gt_list_sp) > 0.5, 1, 0)
#             fpr, tpr, thresholds_sorted = roc_curve(gt_list_sp_binary, pr_list_sp, pos_label=1)

#             if save_path is not None:
#                 roc_save_path = os.path.join(save_path, "results")
#                 os.makedirs(roc_save_path, exist_ok=True)  # Ensure the directory exists

#                 # Plot and save the ROC curve
#                 plt.figure()
#                 plt.plot(fpr, tpr, label='ROC Curve')
                
#                 # Annotate specific points with the corresponding threshold values
#                 # Here we're annotating every 5th point to avoid clutter
#                 for i in range(0, len(thresholds_sorted), 5):
#                     plt.annotate(f'{thresholds_sorted[i]:.2f}', (fpr[i], tpr[i]), textcoords="offset points", xytext=(2, -10))
                
#                 plt.xlabel('False Positive Rate')
#                 plt.ylabel('True Positive Rate')
#                 plt.title('ROC Curve')
#                 plt.legend(loc="lower right")
                
#                 # Save the figure
#                 plt.savefig(os.path.join(roc_save_path, 'roc_curve_with_thresholds.png'))
#                 plt.close()

#             auroc_px = round(roc_auc_score(gt_list_px, pr_list_px), 4)
#             auroc_sp = round(roc_auc_score(gt_list_sp, pr_list_sp), 4)
#             return auroc_px, auroc_sp, round(np.mean(aupro_list), 4)
#         else:
#             return 0, 0, 0


# # Window Sliding
# def evaluation_multi_proj(encoder, proj, bn, decoder, dataloader, device, th=0.5, save_path=None, eval_mode=True):
#     encoder.eval()
#     proj.eval()
#     bn.eval()
#     decoder.eval()
#     gt_list_px = []
#     pr_list_px = []
#     gt_list_sp = []
#     pr_list_sp = []
#     aupro_list = []

#     tp, tn, fp, fn = 0, 0, 0, 0
#     with torch.no_grad():
#         for idx, data in tqdm(enumerate(dataloader), total=len(dataloader), desc='Evaluation'):
#             print(f"Processing data {idx}...")
#             img = data[0][0].to(device)  # Accessing the first tensor in the list and moving it to the device
#             gt = data[0][1].to(device)   # Accessing the second tensor in the list and moving it to the device
#             img_original = data[0][5]     # Accessing the sixth tensor in the list (assuming it contains the original image)

#     # 
#     #     for idx, data in tqdm(enumerate(dataloader), total=len(dataloader), desc='Evaluation'):
#     #         print(f"Processing data {idx}...")
#     #         img = data[0].to(device)   #######
#     #         gt = data[1].to(device)
#     #         img_original = data[5]  

#             patch_size = 224  
#             stride = 32  
#             anomaly_maps = []
#             for i in range(0, img.shape[2] - patch_size + 1, stride):
#                 for j in range(0, img.shape[3] - patch_size + 1, stride):
#                     img_patch = img[:, :, i:i+patch_size, j:j+patch_size]
#                     inputs = encoder(img_patch)
#                     features = proj(inputs)
#                     outputs = decoder(bn(features))
#                     anomaly_map, _ = cal_anomaly_map(inputs, outputs, patch_size, amap_mode='a')
#                     anomaly_map = gaussian_filter(anomaly_map, sigma=4)
#                     anomaly_maps.append(anomaly_map)

#             anomaly_map_aggregated = np.zeros_like(anomaly_maps[0])
#             for anomaly_map in anomaly_maps:
#                 anomaly_map_aggregated += anomaly_map
#             anomaly_map_aggregated /= len(anomaly_maps)  

#             _, thresh = cv2.threshold(anomaly_map_aggregated, th, 1, cv2.THRESH_BINARY)

#             print(f"Data {idx} processed successfully.")

#             if eval_mode:
#                 gt_list_px.extend(gt.cpu().numpy().astype(int).ravel())
#                 pr_list_px.extend(anomaly_map_aggregated.ravel())
#                 gt_list_sp.append(np.max(gt.cpu().numpy().astype(int)))
#                 pr_list_sp.append(np.max(anomaly_map_aggregated))

#                 if 1.0 in np.unique(thresh):
#                     tp += 1
#                 else:
#                     fn += 1

#             # Save the image with anomaly highlighted
#             if save_path is not None:
#                 for i in range(img.shape[0]):
#                     save_image_with_transparent_anomaly_overlay(img_original[i], thresh[i], save_path, f"data_{idx}_{i}")

#         if eval_mode:
#             # Convert ground truth to binary (0 or 1)
#             gt_list_sp_binary = np.where(np.array(gt_list_sp) > 0.5, 1, 0)
#             fpr, tpr, thresholds_sorted = roc_curve(gt_list_sp_binary, pr_list_sp, pos_label=1)

#             if save_path is not None:
#                 roc_save_path = os.path.join(save_path, "results")
#                 os.makedirs(roc_save_path, exist_ok=True)  # Ensure the directory exists

#                 # Plot and save the ROC curve
#                 plt.figure()
#                 plt.plot(fpr, tpr, label='ROC Curve')
                
#                 # Annotate specific points with the corresponding threshold values
#                 # Here we're annotating every 5th point to avoid clutter
#                 for i in range(0, len(thresholds_sorted), 5):
#                     plt.annotate(f'{thresholds_sorted[i]:.2f}', (fpr[i], tpr[i]), textcoords="offset points", xytext=(2, -10))
                
#                 plt.xlabel('False Positive Rate')
#                 plt.ylabel('True Positive Rate')
#                 plt.title('ROC Curve')
#                 plt.legend(loc="lower right")
                
#                 # Save the figure
#                 plt.savefig(os.path.join(roc_save_path, 'roc_curve_with_thresholds.png'))
#                 plt.close()

#             auroc_px = round(roc_auc_score(gt_list_px, pr_list_px), 4)
#             auroc_sp = round(roc_auc_score(gt_list_sp, pr_list_sp), 4)
#             return auroc_px, auroc_sp, round(np.mean(aupro_list), 4)
#         else:
#             return 0, 0, 0


def save_concatenated_image(img, gt, thresh, save_path, filename):
    """
    Concatenates the original image, ground truth, and thresholded anomaly map side by side and saves the result.

    Parameters:
    - img: The original image as a tensor.
    - gt: The ground truth anomaly map as a tensor.
    - thresh: The thresholded anomaly map as a numpy array.
    - save_path: Directory path where the image will be saved.
    - filename: Name of the file under which the image will be saved.
    """
    # Convert tensors to numpy arrays if they are not already
    img_np = img.squeeze().cpu().numpy()
    gt_np = gt.squeeze().cpu().numpy()

    image_height, image_width = img_np.shape[1], img_np.shape[2]
    
    # Ensure the anomaly map is correctly scaled (0 to 255)
    thresh_scaled = (thresh * 255).astype(np.uint8)
    if len(thresh_scaled.shape) == 2:  # If ground truth is single-channel, convert to 3-channel
        thresh_scaled = cv2.cvtColor(thresh_scaled, cv2.COLOR_GRAY2BGR)
        thresh_scaled = cv2.resize(thresh_scaled, (image_width, image_height), interpolation=cv2.INTER_NEAREST)

    # Convert single-channel images to 3-channel images for visual consistency
    if img_np.ndim < 3 or img_np.shape[0] == 1:  # Assuming the image is grayscale
        img_np = np.repeat(img_np, 3, axis=0)
    if gt_np.ndim < 3 or gt_np.shape[0] == 1:
        gt_np = np.repeat(gt_np, 3, axis=0)
    
    # Convert channels from first to last if necessary
    if img_np.shape[0] == 3:  # Assuming channel-first format
        img_np = np.transpose(img_np, (1, 2, 0))
    if gt_np.shape[0] == 3:
        gt_np = np.transpose(gt_np, (1, 2, 0))
    
    # Normalize and scale the input image for display if necessary
    img_display = ((img_np - img_np.min()) / (img_np.ptp() + 1e-5)) * 255
    img_display = img_display.astype(np.uint8)
    
    # Convert ground truth to color for visualization
    gt_display = (gt_np * 255).astype(np.uint8)
    if len(gt_display.shape) == 2:  # If ground truth is single-channel, convert to 3-channel
        gt_display = cv2.cvtColor(gt_display, cv2.COLOR_GRAY2BGR)
        gt_display = cv2.resize(gt_display, (image_width, image_height), interpolation=cv2.INTER_NEAREST)

    # Stack the images horizontally for comparison
    concatenated_image = np.hstack((img_display, gt_display, thresh_scaled))

    # Save the concatenated image
    full_save_path = f"{save_path}/{filename}.png"
    cv2.imwrite(full_save_path, concatenated_image)

    print(f"Image saved at {full_save_path}")

def save_image_with_transparent_anomaly_overlay(img_original, thresh, save_path, filename, alpha=0.4, crop_left=0.0, crop_right=0.0):
    """
    Creates a transparent overlay of the thresholded anomaly map on the original image, crops the final overlayed image
    from the left and right by specified percentages, and saves the result.

    Parameters:
    - img_original: The original image as a tensor.
    - thresh: The thresholded anomaly map as a numpy array.
    - save_path: Directory path where the image will be saved.
    - filename: Name of the file under which the image will be saved.
    - alpha: Transparency factor for the overlay. 0 is fully transparent, 1 is fully opaque.
    - crop_left: Percentage of the final image to crop from the left side.
    - crop_right: Percentage of the final image to crop from the right side.
    """
    # Convert the original image tensor to a numpy array
    img_np = img_original.squeeze().numpy()

    # Ensure the anomaly map is correctly scaled (0 to 255)
    thresh_scaled = (thresh * 255).astype(np.uint8)
    if len(thresh_scaled.shape) == 2:  # If the anomaly map is single-channel, convert to 3-channel
        thresh_scaled = cv2.cvtColor(thresh_scaled, cv2.COLOR_GRAY2BGR)

    # Convert single-channel images to 3-channel images for visual consistency
    if img_np.ndim < 3 or img_np.shape[0] == 1:
        img_np = np.repeat(img_np, 3, axis=0)
    
    img_display = img_np

    # Resize the thresholded anomaly map to match the original image dimensions
    image_height, image_width = img_display.shape[:2]
    thresh_resized = cv2.resize(thresh_scaled, (image_width, image_height), interpolation=cv2.INTER_NEAREST)

    # Create an overlay of the thresholded anomaly map on the original image with transparency
    overlay = img_display.copy()
    mask = thresh_resized[:, :, 0] > 0  # Create a mask where thresholded anomalies are detected
    overlay[mask] = (0, 0, 255)  # Color anomalies with red

    # Blend the original image with the overlay using the alpha parameter for transparency
    transparent_overlay = cv2.addWeighted(img_display, 1 - alpha, overlay, alpha, 0)

    # Crop the overlayed image based on specified percentages
    crop_width_left = int(image_width * crop_left)
    crop_width_right = int(image_width * crop_right)
    cropped_overlay = transparent_overlay[:, crop_width_left:image_width - crop_width_right]

    # Save the cropped overlay image with transparency
    full_save_path = f"{save_path}/{filename}.png"
    cv2.imwrite(full_save_path, cropped_overlay)

    print(f"Image saved at {full_save_path}")


def compute_pro(masks: ndarray, amaps: ndarray, num_th: int = 200) -> None:

    """Compute the area under the curve of per-region overlaping (PRO) and 0 to 0.3 FPR
    Args:
        category (str): Category of product
        masks (ndarray): All binary masks in test. masks.shape -> (num_test_data, h, w)
        amaps (ndarray): All anomaly maps in test. amaps.shape -> (num_test_data, h, w)
        num_th (int, optional): Number of thresholds
    """

    assert isinstance(amaps, ndarray), "type(amaps) must be ndarray"
    assert isinstance(masks, ndarray), "type(masks) must be ndarray"
    assert amaps.ndim == 3, "amaps.ndim must be 3 (num_test_data, h, w)"
    assert masks.ndim == 3, "masks.ndim must be 3 (num_test_data, h, w)"
    assert amaps.shape == masks.shape, "amaps.shape and masks.shape must be same"
    assert set(masks.flatten()) == {0, 1}, "set(masks.flatten()) must be {0, 1}"
    assert isinstance(num_th, int), "type(num_th) must be int"

#     df = pd.DataFrame([], columns=["pro", "fpr", "threshold"])
    d = {'pro':[], 'fpr':[],'threshold': []}
    binary_amaps = np.zeros_like(amaps, dtype=np.bool_)

    min_th = amaps.min()
    max_th = amaps.max()
    delta = (max_th - min_th) / num_th

    for th in np.arange(min_th, max_th, delta):
        binary_amaps[amaps <= th] = 0
        binary_amaps[amaps > th] = 1

        pros = []
        for binary_amap, mask in zip(binary_amaps, masks):
            for region in measure.regionprops(measure.label(mask)):
                axes0_ids = region.coords[:, 0]
                axes1_ids = region.coords[:, 1]
                tp_pixels = binary_amap[axes0_ids, axes1_ids].sum()
                pros.append(tp_pixels / region.area)

        inverse_masks = 1 - masks
        fp_pixels = np.logical_and(inverse_masks, binary_amaps).sum()
        fpr = fp_pixels / inverse_masks.sum()

#         df = df.append({"pro": mean(pros), "fpr": fpr, "threshold": th}, ignore_index=True)
        d['pro'].append(mean(pros))
        d['fpr'].append(fpr)
        d['threshold'].append(th)
    df = pd.DataFrame(d)
    # Normalize FPR from 0 ~ 1 to 0 ~ 0.3
    df = df[df["fpr"] < 0.3]
    df["fpr"] = df["fpr"] / df["fpr"].max()

    pro_auc = auc(df["fpr"], df["pro"])
    return pro_auc
