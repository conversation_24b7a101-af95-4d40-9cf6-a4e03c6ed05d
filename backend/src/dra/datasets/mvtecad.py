import numpy as np
import os, sys
from backend.src.dra.datasets.base_dataset import BaseADDataset
from PIL import Image
from torchvision import transforms
from backend.src.dra.datasets.cutmix import CutMix
import random


def unstack(a, axis=0):
    return np.moveaxis(a, axis, 0)


class MVTecAD(BaseADDataset):

    def __init__(self, args, train=True):
        super(MVTecAD).__init__()
        self.args = args
        self.train = train
        # self.classname = self.args.classname
        self.know_class = self.args.know_class
        self.pollution_rate = self.args.cont_rate
        if self.args.test_threshold == 0 and self.args.test_rate == 0:
            self.test_threshold = self.args.nAnomaly
        else:
            self.test_threshold = self.args.test_threshold

        # self.root = os.path.join(self.args.dataset_root, self.classname)
        self.root = self.args.dataset_root
        self.transform = self.transform_train() if self.train else self.transform_test()
        self.transform_pseudo = self.transform_pseudo()

        normal_id = list()
        normal_data = list()
        split = 'train'
        normal_files = os.listdir(os.path.join(self.root, split, 'good'))
        for file in normal_files:
            if 'png' in file[-3:] or 'PNG' in file[-3:] or 'jpg' in file[-3:] or 'npy' in file[-3:]:
                normal_data.append(split + '/good/' + file)
                normal_id.append(file.split('.')[0])

        self.nPollution = int((len(normal_data) / (1 - self.pollution_rate)) * self.pollution_rate)
        if self.test_threshold == 0 and self.args.test_rate > 0:
            self.test_threshold = int(
                (len(normal_data) / (1 - self.args.test_rate)) * self.args.test_rate) + self.args.nAnomaly

        self.ood_data, self.ood_id = self.get_ood_data()

        if self.train is False:
            normal_id = list()
            normal_data = list()
            split = 'test'
            normal_files = os.listdir(os.path.join(self.root, split, 'good'))
            for file in normal_files:
                if 'png' in file[-3:] or 'PNG' in file[-3:] or 'jpg' in file[-3:] or 'npy' in file[-3:]:
                    normal_data.append(split + '/good/' + file)
                    normal_id.append(file.split('.')[0])

        outlier_data, pollution_data, outlier_id, pollution_id = self.split_outlier()
        # outlier_data.sort()
        if len(outlier_data) > 0:
            outlier_sorted = sorted(zip(outlier_data, outlier_id))
            outlier_data, outlier_id = zip(*outlier_sorted)
            outlier_data, outlier_id = list(outlier_data), list(outlier_id)

        normal_data = normal_data + pollution_data
        normal_id = normal_id + pollution_id

        normal_label = np.zeros(len(normal_data)).tolist()
        outlier_label = np.ones(len(outlier_data)).tolist()

        self.images = normal_data + outlier_data
        self.ids = np.array(normal_id + outlier_id)
        self.labels = np.array(normal_label + outlier_label)
        self.normal_idx = np.argwhere(self.labels == 0).flatten()
        self.outlier_idx = np.argwhere(self.labels == 1).flatten()

    def get_ood_data(self):
        ood_data = list()
        ood_data_id = list()
        if self.args.outlier_root is None:
            return None, None
        dataset_classes = os.listdir(self.args.outlier_root)
        for cl in dataset_classes:
            if cl == self.args.classname:
                continue
            cl_root = os.path.join(self.args.outlier_root, cl, 'train', 'good')
            ood_file = os.listdir(cl_root)
            for file in ood_file:
                if 'png' in file[-3:] or 'PNG' in file[-3:] or 'jpg' in file[-3:] or 'npy' in file[-3:]:
                    ood_data.append(os.path.join(cl_root, file))
                    ood_data_id.append(file.split('.')[0])
        return ood_data, ood_data_id

    def split_outlier(self):
        outlier_data_dir = os.path.join(self.root, 'test')
        outlier_classes = os.listdir(outlier_data_dir)
        if self.know_class in outlier_classes:
            print("Know outlier class: " + self.know_class)
            outlier_data = list()
            know_class_data = list()
            outlier_id = list()
            know_class_id = list()
            for cl in outlier_classes:
                if cl == 'good':
                    continue
                outlier_file = os.listdir(os.path.join(outlier_data_dir, cl))
                for file in outlier_file:
                    if 'png' in file[-3:] or 'PNG' in file[-3:] or 'jpg' in file[-3:] or 'npy' in file[-3:]:
                        if cl == self.know_class:
                            know_class_data.append('test/' + cl + '/' + file)
                            know_class_id.append(file.split('.')[0])
                        else:
                            outlier_data.append('test/' + cl + '/' + file)
                            outlier_id.append(file.split('.')[0])
            know_class_stack = np.stack((know_class_data, know_class_id), axis=-1)
            # np.random.RandomState(self.args.ramdn_seed).shuffle(know_class_data)
            np.random.RandomState(self.args.ramdn_seed).shuffle(know_class_stack)
            know_class_data, know_class_id = unstack(know_class_stack, axis=1)
            know_outlier = know_class_data[0:self.args.nAnomaly]
            know_outlier_id = know_class_id[0:self.args.nAnomaly]
            unknow_outlier = outlier_data
            unknow_outlier_id = outlier_id
            if self.train:
                return know_outlier, list(), know_outlier_id, list()
            else:
                return unknow_outlier, list(), unknow_outlier_id, list()

        outlier_data = list()
        outlier_id = list()
        for cl in outlier_classes:
            if cl == 'good':
                continue
            outlier_file = os.listdir(os.path.join(outlier_data_dir, cl))
            for file in outlier_file:
                if 'png' in file[-3:] or 'PNG' in file[-3:] or 'jpg' in file[-3:] or 'npy' in file[-3:]:
                    outlier_data.append('test/' + cl + '/' + file)
                    outlier_id.append(file.split('.')[0])
        outlier_stack = np.stack((outlier_data, outlier_id), axis=-1)
        # np.random.RandomState(self.args.ramdn_seed).shuffle(outlier_data)
        np.random.RandomState(self.args.ramdn_seed).shuffle(outlier_stack)
        outlier_data, outlier_id = unstack(outlier_stack, axis=1)
        outlier_data, outlier_id = list(outlier_data), list(outlier_id)
        if self.train:
            return outlier_data[0:self.args.nAnomaly], outlier_data[
                                                       self.args.nAnomaly:self.args.nAnomaly + self.nPollution], \
                   outlier_id[0:self.args.nAnomaly], outlier_id[self.args.nAnomaly:self.args.nAnomaly + self.nPollution]
        else:
            return outlier_data[self.test_threshold:], list(), \
                   outlier_id[self.test_threshold:], list()

    def load_image(self, path):
        if 'npy' in path[-3:]:
            img = np.load(path).astype(np.uint8)
            img = img[:, :, :3]
            return Image.fromarray(img)
        return Image.open(path).convert('RGB')

    def transform_train(self):
        composed_transforms = transforms.Compose([
            transforms.Resize((self.args.img_size, self.args.img_size)),
            transforms.RandomRotation(180),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
        return composed_transforms

    def transform_pseudo(self):
        composed_transforms = transforms.Compose([
            transforms.Resize((self.args.img_size, self.args.img_size)),
            CutMix(),
            transforms.RandomRotation(180),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
        return composed_transforms

    def transform_test(self):
        composed_transforms = transforms.Compose([
            transforms.Resize((self.args.img_size, self.args.img_size)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
        return composed_transforms

    def __len__(self):
        return len(self.images)

    def random_choice(self):
        ood_zip = list(zip(self.ood_data, self.ood_id))
        ood_path, ood_id = random.choice(ood_zip)
        return ood_path, ood_id

    def __getitem__(self, index):
        rnd = random.randint(0, 1)
        if index in self.normal_idx and rnd == 0 and self.train:
            if self.ood_data is None:
                index = random.choice(self.normal_idx)
                image = self.load_image(os.path.join(self.root, self.images[index]))
                id = self.ids[index]
                transform = self.transform_pseudo
            else:
                # image = self.load_image(random.choice(self.ood_data))
                ood_path, ood_id = self.random_choice()
                image = self.load_image(ood_path)
                id = ood_id
                transform = self.transform
            label = 2
        else:
            image = self.load_image(os.path.join(self.root, self.images[index]))
            id = self.ids[index]
            transform = self.transform
            label = self.labels[index]
        sample = {'image': transform(image), 'label': label, 'id': id}
        return sample
