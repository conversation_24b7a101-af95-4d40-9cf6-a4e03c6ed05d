from backend.src.setup_stk import *
from flask_socketio import <PERSON><PERSON><PERSON>
from backend.src.network.ui_bridge import (
    ui_bridge_init, update_status, push_new_result, start_status_monitor
)

socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    async_mode="threading",   # keep threading
    logger=True,              # helpful until it works
    engineio_logger=True
)
ui_bridge_init(app, socketio)

import os
import signal
import sys
from datetime import datetime

from backend.src.camera_manager.camera_manager_setup import CameraSetupManager
from backend.src.license import *

from backend.src.utils.files_manager import *
from backend.src.utils.logger_setup import *
from pathlib import Path
from backend.src.utils.color_configuration_selector import *
from backend.src.utils.sock_id_manager import manager as sock_id_manager
from backend.src.stockings_analysis.stocking_analysis import RESULTS_ROOT






if __name__ == '__main__':

    # Apply servo optimization preset at startup
    # Options: 'conservative', 'balanced', 'aggressive', 'production'


    if args.mode.lower() == "simulator":
        os.environ["SIMULATOR_MODE"] = "1"   # tell downstream code not to drive hardware
    else:
        os.environ.pop("SIMULATOR_MODE", None)
        from backend.config.servo_optimization_config import apply_preset
        apply_preset('immediate_full_analysis')  # Applied via apply_immediate_full_analysis.py

    from backend.src.stockings_analysis.stocking_analysis import *

    stop_threads = False
    detectron2_config = config_loader(args.configs, "config.yaml")
    cameras_config =  config_loader(args.configs, "cameras.yaml")
    log_file_directory = detectron2_config.get("LOG_PATH", "logs")
    
    os.makedirs(log_file_directory, exist_ok=True)

    logger = create_custom_logging(log_file_directory)    
    #Create the camera setup manager
    camera_setup = CameraSetupManager(cameras_config, logger)
    
    #Load the cameras configuration
    #Setup Output Folder
    results_folder = Path(RESULTS_ROOT) / datetime.now().strftime("%Y%m%d")
    os.makedirs(results_folder, exist_ok=True)

    #Create the camera manager initializing all the cameras given in the selected_camaras
    camera_manager = camera_setup.setup_camera_system(args.mode, selected_cameras="all")
 
    select_color_and_update(camera_manager, cameras_config, logger)
    

    #check_license_availability ("license-cizeta.lic", logger)
    ob_predictor = load_predictors()
    camera_manager.start_image_retrieval()

    get_cameras_frames_parallel(logger, cameras_config, stop_threads, camera_manager,  results_folder, ob_predictor)
    #target=get_frames(logger, cameras_config, stop_threads, camera_manager.camera_queues,  results_folder, ob_predictor=ob_predictor), 
    
    

    start_status_monitor(socketio, camera_manager, interval=2)
    
    def signal_handler(signum, frame):
        logger.info("Shutting down...", title="Exit")
        camera_manager.stop_all_cameras()
        sock_id_manager.save_current_id()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGTSTP, signal_handler)

    socketio.run(app,
             host=args.ip,
             port=int(args.port),
             debug=False,                # no Werkzeug debugger
             allow_unsafe_werkzeug=True) # kills the assert completely