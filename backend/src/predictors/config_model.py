from detectron2.config import get_cfg
from detectron2.model_zoo import model_zoo
from detectron2.data.datasets import register_coco_instances
import os


def setup_cfg(config, dataset=None, mode="train"):
    """
    Configures and returns the Detectron2 configuration for a given model, dataset, and mode.
    This function initializes the model configuration based on a predefined architecture
    and updates it with specific training, evaluation, and dataset parameters.

    Args:
        config (dict): Dictionary containing model and training-specific settings.
        dataset (dict, optional): Dictionary containing dataset paths and names. Required for training.
        mode (str, optional): Operation mode, either 'train' or any other string for testing configurations.

    Returns:
        cfg (detectron2.config.CfgNode): Configured Detectron2 configuration object.
    """

    # Initialize configuration with a base model configuration
    cfg = get_cfg()
    architecture = f"{config['detectron_mode']}/{config['coco_model']}"
    cfg.merge_from_file(model_zoo.get_config_file(architecture))

    
    # Set testing-specific configurations
    cfg.INPUT.MIN_SIZE_TEST = 0
    cfg.INPUT.MAX_SIZE_TEST = 0
    cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = config.get("confidence_threshold", 0.5)
    cfg.MODEL.ROI_HEADS.NMS_THRESH_TEST = config.get("nms_threshold", 0.1)

    # Common model settings
    cfg.MODEL.DEVICE = f'cuda:{config.get("gpu_to_use", 0)}'
    cfg.MODEL.WEIGHTS = (config["resume_checkpoint"]
                         if config["resume_checkpoint"]
                         else model_zoo.get_checkpoint_url(architecture))
    cfg.MODEL.ROI_HEADS.BATCH_SIZE_PER_IMAGE = 512
    cfg.MODEL.ROI_HEADS.NUM_CLASSES = len(config["class_names"])-1

    # Optional sliding window settings
    cfg.SLIDING_WINDOW = config.get("sliding_window", False)
    cfg.WINDOW_SIZE = config.get("window_size", 200)
    cfg.WINDOW_OVERLAPPING = config.get("window_overlapping", 0.1)

    return cfg
