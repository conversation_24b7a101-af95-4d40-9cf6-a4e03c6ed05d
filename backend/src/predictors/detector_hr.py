"""
# File: object_masker.py
# Wrapper class for mask detection.
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
# - The ObjectMasker class in Python is an utility designed for object detection and visualization using a pre-trained
# instance segmentation model. This class can detect and visualize multiple object classes in images, and allows
# customization of the detection threshold and visualization style.
# - Key methods in this class are detect to perform object detection, _draw_detections to overlay masks on detected
# objects, and print_detections to print details of detections. The class also maintains a record of detections, and
# supports processing on a GPU.
# - It requires a pre-trained model, a class map for object classes, and relevant image data
# for detection tasks.
# Changelog
#   04.11.2022. Javier <PERSON>. Update display setting to allows print text near to mask
#   11.11.2022. <PERSON>. Added minimum confidence in constructor
#   07.07.2023. <PERSON>. Review to make it more robust, including error handling.
#   11.04.2024. <PERSON>. Renamed to DetectorHR
"""
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed

import numpy as np

from backend.libs.hrnet.hrnet import Hrnet
from backend.src.utils.data_process import yaml_to_dict
from backend.src.utils.image_processing import read_images


warnings.simplefilter(action='ignore', category=FutureWarning)

import logging
import cv2

logger = logging.getLogger("DET")

import os


class DetectorHR:

    def __init__(self, class_map, model_path, min_confidence=0.5, image_size=None, gpu=0, model_args={}):
        """
        Default constructor
        :param class_map: Dictionary that defines the style of each class when visualizing detection. Example
                        "<class_name>":{
                                "color_bgr": array[3] #array of 3 integers from 0 to 255
                                "thickness": float, #Negative value fills rectangle; positive draws empty rectangle
                                "display_text": bool, # TRUE if display class name and score; FALSE otherwise
                                "min_score": float, #The minimum score to be displayed
                        }
        :param min_confidence: The threshold used to filter out low-scored bounding boxes predicted
        :param model_path: Path to trained model
        :param image_size: Default image size. Prior to the prediction images will be resized
        :param gpu: Gpu id where the model is going to be loaded.
        :param args: (dict) Project specific parameter. For hrnet:
            - scales (list)
        """

        self.detections = []
        self.mode = "segmentation"

        model_path =  model_path or model_args.get("path_hr")


        if not os.path.isfile(model_path):
            raise IOError(f"'{model_path}' file not found!")

        # Set local variables
        self._image_size = image_size
        self.gpu = gpu
        self._min_confidence = min_confidence

        # Model specific
        scales = model_args.get("scales", [0.5])

        # Initialize color map
        if len(class_map) <= 1:
            raise ValueError("Not enough classes in class_map")

        self._class_map = class_map  # Color, type, thickness [>0; contour] [<0 mask], draw text (score), min confidence
        palette = [c for color, _, _, _, _ in self._class_map.values() for c in color] + [0] * (
                256 * 3 - len(self._class_map) * 3)

        self._model = Hrnet(model_path=model_path, palette=palette, scales=scales, num_classes=len(self._class_map)+1,
                            gpu_id=gpu)

        print(f'[INFO] Loaded network {model_path}')

    def _calculate_scores(self, mask, probs) -> dict:
        """
        Calculate the scores of a given mask
        :param mask: image mask returned by the NeuronalNet.
        :param probs: Per pixel probabilities.
        :return: The average scores of a given masked ared
        """
        mask_probs = np.ma.array(probs, mask=mask)
        r = 0
        try:
            r = round(mask_probs.mean(), 3)
        except Exception as ex:
            print(f"{ex}\n{mask_probs}")

        return r

    def detect(self, images, class_names=[], images_in_memory=True, include_no_detections=True,
               store_visualizations=False, draw_masks=False, draw_bbox=False, store_bbox=False, store_scores=False):
        """
        This method stores a list of detections from a given set of images.
        :param store_visualizations: Store visualization (image+mask overlap) on self. Detections
        :param include_no_detections: Include entry in detection even if nothing was detected
        :param images: Path to each image or cv2 image object list to be processed
        :param class_names: Name of the classes to be detected. Empty will detect all the classes.
        :param images_in_memory: True if you want to store original images in memory; False otherwise
        :param store_visualizations: Whether to store visualization images
        :param draw_masks: Whether to draw masks on the visualization
        :param draw_bbox: Whether to draw bounding boxes on the visualization
        :param store_bbox: Whether to store bounding boxes in the detection results
        :param store_scores: Whether to store scores in the detection results
        :returns detections dictionary with enhanced visualization
        """
        detections = []
        for i, img in enumerate(images):
            try:
                image, path = (cv2.imread(img), img) if isinstance(img, str) else (img, None)

                # Raw detections
                masks, max_pro = self._model.evaluate([image])
                mask = masks[0]

                aux_classes, aux_masks = self._extract_masks(mask, min_area=0, merge_garment=False)
                aux_scores = None if not store_scores else [self._calculate_scores(m, max_pro) for m in aux_masks]
                aux_boxes = None if not store_bbox else [self.mask_to_bbox(m) for m in aux_masks]

                image_vis = image.copy() if store_visualizations else None

                if store_visualizations:
                    for j, msk in enumerate(aux_masks):
                        cls_name = aux_classes[j]
                        cls_score = aux_scores[j] if aux_scores else None
                        text = f"{cls_name}: {cls_score:.2f}" if cls_score else cls_name
                        bbox = aux_boxes[j][0] if aux_boxes else None

                        color = self._class_map.get(cls_name, [[255, 255, 255], "unknown", 2, True, 0.5])[0]
                        image_vis = self._draw_detections(
                            img=image_vis,
                            mask=msk if draw_masks else None,
                            bbox=bbox if draw_bbox else None,
                            text=text,
                            thickness=self._class_map[cls_name][2],
                            color=color
                        )

                # Visualize detections for each class
                if not images_in_memory:
                    image = None

                # Will be stored only if any detection that match the requirement is found
                # This will save memory
                detection_found = False
                if len(aux_classes) > 0 or include_no_detections:
                    detection_found = True

                    detection = {
                        "path": path,
                        "masks": aux_masks,
                        "boxes": aux_boxes,
                        "scores": aux_scores,
                        "classes": aux_classes,
                        "image": image,
                        "visualization": image_vis if store_visualizations else None
                    }

                    detections.append(detection)

                del detection

            except Exception as ex:
                print("[ERROR] %s" % ex)
                raise ex

        return detections


    def _extract_masks(self, mask, merge_garment=True, min_area=10000, workers=8) -> tuple:
        """
        Split a given merged mask into single masks.

        :param mask: image mask returned by the NeuronalNet.
        :param merge_garment: whether to merge the masks to generate a single garment mask
        :param min_area: minimum area to consider.
        :param workers: number of concurrent workers.
        :return: (list of classes, list of masks). The masks are in grayscale.
        """

        def process_class(c_name, c_color, c_type):
            if c_type == "background":
                return []
            if "defect" in c_type:
                used_min_area = 0
            else:
                used_min_area = min_area

            # Extract all the classes. e.g. all the stains
            c_color_a = np.array(c_color, dtype=np.uint8)
            merged_mask = cv2.inRange(mask, c_color_a, c_color_a)

            if merge_garment and c_type == "garment":
                return [[c_name, merged_mask, c_type, cv2.countNonZero(merged_mask)]]

            result = []

            # Split all the defects in individual binary masks
            contours, _ = cv2.findContours(merged_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for c in contours:
                area = cv2.contourArea(c)
                if area > used_min_area:
                    binary_mask = np.zeros_like(merged_mask)
                    cv2.drawContours(binary_mask, [c], -1, 255, thickness=cv2.FILLED)
                    result.append((c_name, binary_mask, c_type, area))

            return result

        aux_masks = []
        aux_classes = []
        h, w, _ = mask.shape

        garment_cls = {}
        with ThreadPoolExecutor(max_workers=workers) as executor:
            futures = [executor.submit(process_class, c_name, c_info[0], c_info[1]) for c_name, c_info in
                       self._class_map.items()]
            for future in as_completed(futures):
                result = future.result()
                if result:
                    for c_name, binary_mask, c_type, area in result:
                        if c_type == "garment":
                            garment_cls[c_name] = garment_cls.get(c_name, 0) + area
                        else:
                            aux_classes.append(c_name)
                            aux_masks.append(binary_mask)

        if merge_garment:
            c_name = max(garment_cls, key=garment_cls.get)
            _, binary_mask = cv2.threshold(cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY), 1, 255, cv2.THRESH_BINARY)
            c_mask = cv2.dilate(binary_mask, np.ones((20, 20), np.uint8), iterations=1)

            aux_classes.append(c_name)
            aux_masks.append(c_mask)

        return aux_classes, aux_masks

    def mask_to_bbox(self, mask):
        contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        bbox = []
        for c in contours:
            x, y, w, h = cv2.boundingRect(c)
            # (xmin, ymin, xmax, ymax)
            bbox.append([x, y, x + w, y + h])
        return bbox

    def _draw_detections(self, img, mask=None, bbox=None, text="", thickness=2, color=[255, 255, 255]):
        """
        Draw detected mask on the original image.
        :param img: Source image where the bounding boxes will be drawn
        :param mask: Detection mask
        :param text: Text to be written over the mask. Usually 'class score'
        :param thickness: The size of the mask (>0) or contour (<0)
        :param color: The color of the mask
        :return: The original image with the mask overlap
        """

        im = img.copy()

        if bbox is not None:
            if thickness < 0:
                overlay = img.copy()
                im = cv2.rectangle(img, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, thickness)
                cv2.addWeighted(overlay, -1 * thickness, im, 1 + thickness, 0, im)
            else:
                im = cv2.rectangle(img, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, thickness)

            if text != "":
                im = cv2.putText(img, '{:s}'.format(text), (int(bbox[0] - 2), int(bbox[1]) - 2),
                                 cv2.FONT_HERSHEY_SIMPLEX, 1, [255, 255, 255], 2)

        if mask is not None:
            # Ensure the mask is single-channel for proper processing:
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)

            if bbox is None and (text != "" or thickness >= 0):
                contours = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)[0]

            if thickness < 0:
                _, binary_mask = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY)
                color_layer = np.full(img.shape, color, dtype=np.uint8)
                blended = cv2.addWeighted(img, 0.5, color_layer, 0.5, 0)

                # Copy only the blended areas where the binary mask is not zero
                im[binary_mask > 0] = blended[binary_mask > 0]
            elif len(contours) > 0:
                cv2.drawContours(im, contours, -1, color, int(thickness))

                if bbox is None:
                    bbox = cv2.boundingRect(contours[0])

                im = cv2.putText(im, '{:s}'.format(text), (int(bbox[0] - 2), int(bbox[1]) - 2),
                                 cv2.FONT_HERSHEY_SIMPLEX, 0.5, [255, 255, 255], 1)

        return im

    @staticmethod
    def generate_annotation(detection: dict, epsilon_factor=0.005, mode="segmentation"):
        """
        Generate annotation data from detection results.

        :param detection: Dictionary containing detection results, including image path, image data,
                          bounding boxes, scores, masks, and classes.
        :param epsilon_factor: (int) Increase this value to reduce the number of points

        :return: List of annotations in JSON format.
        """
        img_filename = None if detection["path"] is None else os.path.basename(detection["path"])
        img_width, img_height = detection["image"].shape[1], detection["image"].shape[0]
        shapes = []

        for i in reversed(range(len(detection["classes"]))):

            mask = detection.get("masks", [])[i] if detection.get("masks") else None
            box = detection.get("boxes", [])[i] if detection.get("boxes") else None
            class_name = detection["classes"][i]

            if mode == "detection" and box is not None:
                points = [
                    [int(box.xyxy[0][0]), int(box.xyxy[0][1])],
                    [int(box.xyxy[1][0]), int(box.xyxy[0][1])],
                    [int(box.xyxy[1][0]), int(box.xyxy[1][1])],
                    [int(box.xyxy[0][0]), int(box.xyxy[1][1])]
                ]
                shape_type = "rectangle"
            elif mode == "segmentation" and mask is not None:
                # Approximate the contour
                approx_contour = cv2.approxPolyDP(mask, epsilon_factor * cv2.arcLength(mask, True), True)

                points = []
                # Iterate over each point in the approximated contour
                for pt in approx_contour:
                    # Convert the point to integers and add to the points list
                    points.append(list(map(int, pt[0])))

                shape_type = "polygon"
            else:
                continue

            shapes.append({
                "label": class_name,
                "points": points,
                "group_id": None,
                "shape_type": shape_type
            })

        return {
            "version": "5.0.1",
            "flags": {},
            "imageData": None,
            "shapes": shapes,
            "imagePath": img_filename if mode == "segmentation" else detection["path"],
            "imageHeight": img_height,
            "imageWidth": img_width

        }


if __name__ == '__main__':

    # conf = yaml_file_to_dict("/mnt/data/projects/hrnet/configs/eval_nips.yml / /mnt/data/projects/hrnet/configs/train_nips.yml")

    # Extract color palette from color map
    class_map = {
        "background": [[0, 0, 0], "background", 0, False, 0.9],
        "pants": [[128, 0, 0], "textile", 50, False, 0.4],
        "jacket": [[0, 128, 0], "textile", 50, False, 0.4],
        "overall": [[0, 0, 128], "textile", 50, False, 0.4],
        "t-shirt": [[128, 128, 0], "textile", 50, False, 0.4],
        "vest": [[128, 0, 128], "textile", 50, False, 0.4],
        "polo": [[0, 128, 128], "textile", 50, False, 0.4],
        "apron": [[128, 128, 128], "textile", 50, False, 0.4],
        "shirt": [[255, 0, 0], "textile", 50, False, 0.4],
        "pullover": [[0, 255, 0], "textile", 50, False, 0.4],
        "stain": [[0, 0, 255], "defect", 50, False, 0.4],
        "hole": [[255, 255, 0], "defect", 50, False, 0.4],
        "patch": [[255, 0, 255], "defect", 50, False, 0.4],
        "reflex_stripe": [[0, 255, 255], "defect", 50, False, 0.4],
        "reflex_normal": [[255, 128, 0], "defect", 50, False, 0.4],
        "damaged": [[128, 255, 0], "defect", 50, False, 0.4],
        "falloff": [[128, 0, 255], "defect", 50, False, 0.4],
        "reflex_prismen": [[255, 0, 128], "defect", 50, False, 0.4],
        "reflex_fabric": [[128, 128, 255], "defect", 50, False, 0.4]
    }
    class_map = yaml_file_to_dict("configs/class_map.yaml")

    model_path = "models/2024-07-16_nips_3335.pth"
    imgs_folder_path = "/mnt/datasets/nips/raw_bielefeld/2024-08-06_report/n1/"

    output_dir = "static/hrnet_detector/"

    configs = {
        "scales": [0.5]
    }
    extractor = DetectorHR(class_map, model_path, model_args=configs)



    print("Detector ready")

    print("Reading images from disk")
    images, paths = read_images(imgs_folder_path, depth=1, max_img=1000, load_images=True, format="jpg")
    assert len(paths) > 0, "Not enough images!"
    
    det = extractor.detect([images[0]], images_in_memory=False, store_visualizations=False)[0]

    print("Images ready")

    # Perform detection on each image
    os.makedirs(output_dir, exist_ok=True)
    for image, path in zip(images, paths):

        print(f"Processing image: {path}")
        det = extractor.detect([image], images_in_memory=False, store_visualizations=False)[0]

        if len(det) > 0:
            # Save the detection image
            base_name, ext = os.path.splitext(os.path.basename(path))
            # cv2.imwrite(os.path.join(output_dir, base_name + "-src" + ext), image)
            cv2.imwrite(os.path.join(output_dir, base_name + "-vis-0" + ext), det["visualization"])
            #cv2.imwrite(os.path.join(output_dir, base_name + ext), image)

        # createJSON(paths[i], det["masks"], det["classes"], out_dir)

