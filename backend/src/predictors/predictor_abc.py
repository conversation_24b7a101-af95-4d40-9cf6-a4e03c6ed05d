"""
# File: todo.py
# Copyright: Desion GmbH
# Description:
# Changelog:
"""

import time
from abc import ABC, abstractmethod

from detectron2.config import get_cfg
from detectron2.engine import DefaultPredictor
from detectron2 import model_zoo

import numpy as np
import cv2
import copy
import os
from PIL import Image

from backend.src.dra.dataloaders.dataloader import initDataloader
from backend.src.dra.modeling.net import DRA

import types
import torch
from torchvision import transforms


class Predictor(ABC):

    def __init__(self,config:dict):
        pass

    @abstractmethod
    def predict(self, im,  *args)->dict:
        """
        Perform detections and return results dict()
        """
        pass