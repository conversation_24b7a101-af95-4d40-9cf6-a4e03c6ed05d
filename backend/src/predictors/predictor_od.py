"""
# File: todo.py
# Copyright: Desion GmbH
# Description:
# Changelog:
"""

from abc import ABC, abstractmethod

from backend.src.predictors.detector_hr import DetectorHR
from backend.src.predictors.predictor_abc import Predictor


class PredictorOB(Predictor):

    def __init__(self, config: dict):

        # self._model = ObjectFinder(config["class_map"], config["model_path"], config["image_size"], config["gpu"])
        model_args = {}
        model_args["scales"] = config["SCALES"]
        self._model = DetectorHR(config["CLASS_MAP"], config["WEIGHTS"], model_args=model_args)

    def predict(self, im)->dict:
        """
        This method stores a list of detections from a given set of images.
        :param: im Image to be processed
        :returns: detections dictionary. It is composed by a common entries such as original image or visualization
        image as well as path to the file and a set of arrays where each position of the array correspond with a single
        detection. For example, classes[0] -> boxes[0] -> scores[0] belong to the same detection.
        self.detections{
        "classes"       -> Array of classes
        "boxes"         -> Array of bounding boxes (xmin, ymin, xmax, max)
                     "scores"        -> Array of scores
                     "image"         -> Original image if flag images_in_memory is TRUE; "None" otherwise
                     "visualization" -> Image with bounding boxes, classes and scored drawn
                     "path"          -> path to the original files if @images are path; None otherwise
                     }
        """

        return self._model.detect([im],store_bbox=True, store_visualizations=True, draw_bbox=True)[0]