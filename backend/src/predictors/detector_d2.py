"""
# File: d2_finder.py
# Wrapper class for detectron2 object and mask detector
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
#   - Wrapper class for object location.
# Changelog:
#   04.11.2022. <PERSON>. Update display setting to allows print text near to mask
#   11.11.2022. <PERSON>. Added minimum confidence in constructor
#   13.03.2024. <PERSON><PERSON>. Merged object_masker and object_detection.
#   14.04.2024. <PERSON>. Hierarchy from Predictor. Fixed issued with draw_detections
"""
import os
import warnings

from backend.src.predictors.config_model import setup_cfg
from backend.src.utils.image_processing import read_images, calculate_hsv

warnings.simplefilter(action='ignore', category=FutureWarning)

from detectron2.engine import DefaultPredictor

import logging
import time
import yaml
import cv2

logger = logging.getLogger("OBJ")

""" This class represent a detection. """
""" This class provides methods for getting the masks of images. """


class DetectorD2():

    def __init__(self, class_map, model_path, image_size=(600, 450), gpu=0, mode="segmentation"):
        """
        Default constructor
        :param class_map: Dictionary that defined the style of each class when visualize detection. Example
                        "<class_name>:{
                                "color_bgr": array[3] #array of 3 integer from 0 to  255
                                "thickness": int, #Negative value Fill rectangle; positive empty rectangle
                                "display_text" bool, # TRUE if display class name and score; FALSE otherwise
                        }
        :param model_path: Path to trained model
        :param image_size: Default image size. Prior to the prediction images will be resized
        :param gpu: Gpu id where the model is going to be loaded.
        """

        self.detections = []
        self.mode = mode
        if not os.path.isfile(model_path):
            raise IOError("'%s' file not found!" % model_path)

        # Set local variables
        self._image_size = image_size
        self.gpu = gpu

        # Initialize color map
        assert len(class_map) > 1, "Not enough classes on class_map"
        #with open(class_map) as stream:
            # Color, type, thickness [>0; contour] [<0 mask], draw text (score), min confidence
        #   0 self._class_map = yaml.safe_load(stream)
        self._class_map = class_map

        self._classes = list(self._class_map.keys())
        #if "default" not in self._class_map:
        #    self._class_map["default"] = [[255, 255, 0], "default", 1, False, 1]

        # Initialize configuration from config.py
        config = {}
        config["resume_checkpoint"] = model_path
        config["class_names"] = self._classes
        config["gpu_to_use"] = gpu
        if mode == "segmentation":
            config["detectron_mode"] = "COCO-InstanceSegmentation"
            config["coco_model"] = "mask_rcnn_X_101_32x8d_FPN_3x.yaml"
        else:
            config["detectron_mode"] = "COCO-Detection"
            config["coco_model"] = "faster_rcnn_X_101_32x8d_FPN_3x.yaml"
        cfg = setup_cfg(config, mode="test")
        self._predictor = DefaultPredictor(cfg)

        print('[INFO] Loaded network {:s}'.format(model_path))

    def _draw_detections(self, im, mask=None, bbox=None, text="", thickness=1, color=(255, 255, 255)):
        """
        Draw detected mask or bounding box on the image based on the mode.
        :param im: Source image where the bounding boxes will be drawn.
        :param mask: Detection mask.
        :param bbox: Bounding box coordinates (x1, y1, x2, y2).
        :param text: Text to be written over the mask or bounding box.
        :param thickness: Thickness of lines for mask or bounding box; if negative, fills the area.
        :param color: Color for drawing (B, G, R).
        :return: Image with the mask or bounding box drawn on it.
        """
        if mask is not None:
            contours, _ = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
            if thickness < 0:
                mask = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
                im_aux = im.copy()
                im_aux[mask == 255] = color
                im = cv2.addWeighted(im, 1 + thickness, im_aux, -1 * thickness, 0)
            else:
                cv2.drawContours(im, contours, -1, color, int(thickness))

            if text and contours:
                bbox = cv2.boundingRect(contours[0])
                cv2.putText(im, text, (bbox[0] - 2, bbox[1] - 2), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        if bbox is not None:
            p1 = (int(bbox[0]), int(bbox[1]))
            p2 = (int(bbox[2]), int(bbox[3]))

            if thickness < 0:
                overlay = im.copy()
                cv2.rectangle(overlay, p1, p2, color, -1)
                im = cv2.addWeighted(overlay, -1 * thickness, im, 1 + thickness, 0)
            else:
                cv2.rectangle(im, p1, p2, color, thickness)

            if text:
                cv2.putText(im, text, (bbox[0] - 2, bbox[1] - 2), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        return im

    def detect_and_cut(self, images, output_path, threshold=0.95, padding=0, class_names=[]):

        for image in images:
            self.detect([image], threshold, class_names=class_names)
            self.cut_roi(output_path=output_path, padding=padding)

    def detect(self, images, class_names=[], images_in_memory=False, include_no_detections=False,
               store_visualizations=True):
        """
        This method stores a list of detections from a given set of images.
        :param store_visualizations: Store visualization (image+mask overlap) on self.detections
        :param include_no_detections: Include entry in detection even if nothing was detected
        :param images: Path to each image or cv2 image object list to be processed
        :param threshold: Minimum score of a detection to be stored
        :param class_names: Name of the classes to be detected. Empty will detect all the classes.
        :param images_in_memory: True if you want to store original images in memory; False otherwise
        :returns detections dictionary. It is composed by a common entries such as original image or previsualization
         image as well as path to the file and a set of arrays where each position of the array correspond with a single
        detection. For example, classes[0] -> boxes[0]••••••••••••••••••••••••••••••• -> scores[0] belong to the same detection.
            self.detections{
                    "classes"       -> Array of classes
                    "boxes"         -> Array of bounding boxes (xmin, ymin, xmax, max)
                    "scores"        -> Array of scores
                    "image"         -> Original image if flag images_in_memory is TRUE; "None" otherwise
                    "visualization" -> Image with bounding boxes, classes and scored drawn
                    "path"          -> path to the original files if @images are path; None otherwise
        """
        detections = []

        for i, img in enumerate(images):
            try:
                image, path = (cv2.imread(img), img) if isinstance(img, str) else (img, None)
                image = cv2.resize(image, self._image_size)
                im_height, im_width, _ = image.shape
                # Detect all object classes and regress object bounds
                pred = self._predictor(image)["instances"].to("cpu")
                pred_masks = pred.pred_masks.numpy().astype('uint8')
                pred_scores = pred.scores.numpy()
                pred_classes = pred.pred_classes.numpy()
                pred_boxes = pred.pred_boxes.tensor.numpy().astype(int)

                assert len(pred_scores) == len(pred_masks) == len(pred_classes), "Inconsistent predictions size!"

                image_visualization = image.copy() if store_visualizations else None

                aux_masks = []
                aux_boxes = []
                aux_scores = []
                aux_classes = []
                detection_found = False

                t = time.time()
                for cls_inx, score, mask, box in zip(pred_classes, pred_scores, pred_masks, pred_boxes):
                    cls = self._classes[cls_inx + 1]

                    if score >= 0.2:
                        if len(class_names) == 0 or cls in class_names:
                            aux_classes.append(cls)
                            aux_masks.append(mask)
                            aux_scores.append(score)
                            aux_boxes.append(box)

                        if store_visualizations:
                            cmap = self._class_map["default"] if cls not in self._class_map else self._class_map[
                                cls]

                            text = "%.2f" % score if cmap[3] else ""
                            image_visualization = self._draw_detections(im=image_visualization, mask=mask, bbox=box, text=text,
                                                                        color=cmap[0], thickness=cmap[2])

                # Visualize detections for each class
                if not images_in_memory:
                    image = None

                # Will be stored only if any detection that match the requirement is found
                # This will save memory
                if len(aux_classes) > 0 or include_no_detections:
                    detection_found = True

                    detection = {
                        "path": None,
                        "masks": aux_masks,
                        "boxes": aux_boxes,
                        "scores": aux_scores,
                        "classes": aux_classes,
                        "image": image,
                        "visualization": image_visualization
                    }

                if detection_found:
                    self.detections.append(detection)
                    detections.append(detection)

            except Exception as ex:
                print("[ERROR] %s" % ex)
                raise ex

        return detections

    def cut_roi(self, output_path=None, padding=0, threshold=0.85, class_names=[]):
        """
        This function cuts an image by a specific ROIs and store the ROIS in a given path.
        :param: output_path Path where all cuts will be stored. The folder is created if not exist.
        :param: padding Size in pixels of the padding to fit better to the object
        :param: threshold Cuts only scored is higher than threshold
        :param: class_names Set of classes to be cut. If empty, all classes will be cut.
        :return: Dictionary <filename> -> (<class_name>, <img_roi>)>
        """

        rois = {}
        for j, detection in enumerate(self.detections):
            image = cv2.imread(detection["path"]) if detection["image"] is None else detection["image"]
            im_height, im_width, _ = image.shape
            crops = []
            for i in range(len(detection["scores"])):
                if detection["scores"][i] > threshold:
                    if len(class_names) == 0 or detection["classes"][i] in class_names:
                        xmin = detection["boxes"][i][0] + padding
                        ymin = detection["boxes"][i][1] + padding
                        xmax = detection["boxes"][i][2] - padding
                        ymax = detection["boxes"][i][3] - padding

                        crop = image[int(ymin):int(ymax), int(xmin):int(xmax)]

                        indexed_name = "%s_%d_%d.jpg" % (time.strftime("%Y%m%d-%H%M%S"), j, i)
                        if detection["path"] is not None:
                            indexed_name = "%s_%d.jpg" % (detection["path"][:-4], i)

                        if output_path:
                            os.makedirs(output_path, exist_ok=True)
                            cv2.imwrite(os.path.join(output_path, os.path.basename(indexed_name)), crop)
                            print("[INFO] Writing image in %s" % os.path.join(output_path,
                                                                              os.path.basename(indexed_name)))

                        crops.append((detection["classes"][i], detection["boxes"][i], crop))

            if len(crops) > 0:
                rois[indexed_name] = crops

        return rois

    def print_detections(self, threshold=0.95, class_names=[], only_detections=True):
        """
        Display detections filtered by score and/or class_names. Only display
        @:param threshold Minimum scored of the detection
        @:param class_names Name of the classes to be displayed. If empty then all classes will be displayed
        @:param only_detections Display only the images with at least one detection. Default value is False
        """
        n = 0
        for detection in self.detections:
            for i in range(len(detection.scores)):
                if detection.scores[i] > threshold:
                    if len(class_names) == 0 or detection.classes[i] in class_names:
                        output = []
                        for j, c in enumerate(detection.classes):
                            output.append("\t [" + str(j) + "]: " + c + " - " + str(detection.scores[i]))

                        if len(output) > 0 or only_detections is False:
                            print("[%d] IMAGE_NAME: %s " % (n, detection.image_name))

                            for o in output:
                                print(o)

                            n += 1

    @property
    def image_size(self):
        return self._image_size


if __name__ == '__main__':

    #with open("config/config_default.json") as f:
    #    conf = json.load(f)

    conf = {}
    conf["processor"] = {}
    conf["camera"] = {}
    conf["processor"]["class_map"] = "/mnt/data/projects/ptp/configs/class_map.yaml"
    conf["processor"]["model_path"] = "/mnt/data/models/t3s_c180/output/006_8c_clean_notop/best_model.pth"
    conf["camera"]["cam_emu_path"] = "/mnt/data/datasets/t3s/classifly_180/c180_detectron_test/data/images"

    # Extract color palette from color map
    class_map = conf["processor"]["class_map"]
    model_path = conf["processor"]["model_path"]
    imgs_folder_path = conf["camera"]["cam_emu_path"]

    extractor = DetectorD2(class_map, model_path)

    print("Reading images from disk")
    images, paths = read_images(imgs_folder_path, depth=2, max_img=1000, load_images=True, format="jpg")
    assert len(paths) > 0, "Not enough images!"

    for i, img in enumerate(images):
        det = extractor.detect([img], images_in_memory=True, store_visualizations=True)[0]

        print(paths[i])
        b3 = calculate_hsv(img)
        for i in range(len(det["classes"])):
            class_name = det["classes"][i]

            roi = det["boxes"][i]  # xmin,ymin,xmax,ymax
            mask = det["masks"][i]

            b1 = calculate_hsv(img, mask=mask)
            b2 = calculate_hsv(img, roi=roi)

            print("%s[%d px]> m:(%.3f, %.3f) - r:(%.3f, %.3f) - t:(%.3f, %.3f)" % (class_name,
            cv2.countNonZero(mask), b1[0], b1[2], b2[0], b2[2], b3[0], b3[2]))

        print("\n")

    print("Done")
