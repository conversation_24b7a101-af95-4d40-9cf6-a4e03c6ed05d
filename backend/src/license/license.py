import argparse
import os
from datetime import datetime

from Crypto.Cipher import AES
import platform
import uuid
import struct
import hashlib
import sys
from backend.src.utils.logger_setup import FlexibleLoggerAdapter    
try:
    import wmi
    operating_system = "win"
except:
    import subprocess
    import pyudev
    operating_system = "lin"

def get_system_information(cpu=False, mac=False, disk=False, os_id=False):
    """
    Retrieves system information such as CPU ID, MAC address, serial number, and UUID of the system.

    Returns:
        bytes: SHA256 digest of the system information, truncated to 16 bytes.
    """
    system_info = []

    if cpu:
        try:
            cpu_id = subprocess.check_output("lscpu | grep 'Model name' | awk -F ':' '{print $2}'", shell=True).decode().strip()
        except subprocess.CalledProcessError:
            cpu_id = platform.processor()
        system_info.append(cpu_id)
    
    if mac:
        try:
            mac_address = subprocess.check_output("cat /sys/class/net/eth0/address", shell=True).decode().strip()
        except subprocess.CalledProcessError:
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> ele) & 0xff) for ele in range(0, 8 * 6, 8)][::-1])
        system_info.append(mac_address)
    
    if disk:
        try:
            lsblk_output = subprocess.check_output(['lsblk', '-no', 'NAME']).decode('utf-8').split()
            for device_name in lsblk_output:
                try:
                    blkid_output = subprocess.check_output(['blkid', '-s', 'UUID', '-o', 'value', f'/dev/{device_name}']).decode('utf-8').strip()
                    if blkid_output:
                        system_info.append(blkid_output)
                        break
                except subprocess.CalledProcessError:
                    continue
        except subprocess.CalledProcessError:
            print("Failed to retrieve disk serial number")
    
    if os_id:
        try:
            with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                system_uuid = f.read().strip()
                system_info.append(system_uuid)
        except FileNotFoundError:
            print("System UUID not found")

    system_string = "_".join(system_info).replace(" ", "")
    system_string = hashlib.sha256(system_string.encode('utf-8')).digest()[:16]
    return system_string

def create_encrypted_license_file(date, filename_license):
    """
        Creates an encrypted license file for a given date and filename.

        Args:
            date (str): The date string to include in the license file.
            filename_license (str): The name of the license file to create.
        """
    # Get the current system string as a string
    system_string = get_system_information(cpu=True)

    iv = os.urandom(16)
    cipher = AES.new(system_string, AES.MODE_CBC, iv)
    license_string = "License_valid:{}".format(date)
    license_string = license_string.encode("utf8")
    padding = 16 - len(license_string) % 16
    license_bytes = license_string + bytes([padding]) * padding
    encrypted_string = cipher.encrypt(license_bytes)
    encrypted_string += iv
    binary_data = struct.pack('i', len(encrypted_string)) + encrypted_string

    # Create a file with the encrypted date as its name

    with open(filename_license, 'wb') as file:
        file.write(binary_data)


def validate_license(filename_license):
    """
       Validates a license file and prints the license string if the file is valid.

       Args:
           filename_license (str): The name of the license file to validate.
       """
    system_string = get_system_information(cpu=True)
    with open(filename_license, 'rb') as file:
        length_data = file.read(4)
        length = struct.unpack('i', length_data)[0]
        encrypted_string = file.read(length)
    iv = encrypted_string[-16:]
    encrypted_string = encrypted_string[:-16]
    cipher = AES.new(system_string, AES.MODE_CBC, iv)
    license_bytes = cipher.decrypt(encrypted_string)
    padding = license_bytes[-1]
    license_bytes = license_bytes[:-padding]
    license_string = license_bytes.decode("utf8")
    if "License_valid" in license_string:
        license_date = license_string.split(":")[1]
    else:
        return False
    datetime_license = datetime.strptime(license_date, "%d-%m-%Y")
    datetime_current = datetime.now()


    if datetime_license.date() >= datetime_current.date():
        print(datetime_license.date())
        print(datetime_current.date())
        return True
    else:
        return False

def check_license_availability(license_name: str, logger: FlexibleLoggerAdapter) -> bool:
    if validate_license(license_name):
        logger.info("License available and up to date", title="license_manager")
        return True
    else:
        logger.error("License not available", alias="license_manager")
        sys.exit('Terminating: License check failed')

    
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Create a license that is valid for a specific PC until a specified date')
    parser.add_argument('--date', type=str, help='A date in the format DD-MM-YYYY.')
    parser.add_argument('--license_file', type=str, help='The filename for the license file.')
    args = parser.parse_args()
    print("Creating license for date: {}".format(args.date))
    create_encrypted_license_file(args.date, args.license_file)

