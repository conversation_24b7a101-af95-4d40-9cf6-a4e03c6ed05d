import os
from pathlib import Path
import cv2
from ultralytics import <PERSON>OL<PERSON>
from logging import Logger
from backend.src.utils.logger_setup import FlexibleLoggerAdapter

#!/usr/bin/env python3

class YOLO_Predictor():
    def __init__(self, model_path: YOLO, logger: FlexibleLoggerAdapter):
        self.model_path = model_path
        self.logger = logger
    def initialize_model(self, model_path: Path):
        """
        Initialize the YOLO model.
        """
        if model_path.exists():
            self.model = YOLO(model_path)
            self.logger.info(f"Model loaded from {self.model_path}", title=f"YOLO Predictor")
        else:
            self.logger.info(f"Model path: {model_path}, does not exist.", title=f"YOLO Predictor")

    def run_prediction_on_stocking(self, image: Path):
        """
        Run the YOLO model on the given image path.
        """
        img = cv2.imread(image)
        if img is None:
            self.logger.error(f"Image not found", title=f"YOLO Predictor")
        result = self.model(image)[0]
        return result


