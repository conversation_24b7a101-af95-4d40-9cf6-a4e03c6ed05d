from flask import Flask
from turbo_flask import Turbo
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import SocketIO
from flask_cors import CORS
import yaml
from datetime import datetime
import argparse
import os
import pkg_resources

from backend.src.predictors.predictor_od import PredictorOB

parser = argparse.ArgumentParser()
parser.add_argument('--mode', default="line", help="Camera mode to use (line, regular, simulator, all)")
parser.add_argument('--cameras', type=str, default="all", 
                   help="Comma-separated list of camera serials to use, or 'all'")
parser.add_argument('--allow-no-cameras', action='store_true',
                   help="Allow application to run even if no cameras are connected")



#parser.add_argument("--mode", type=str, default='line', help='line, regular, simulator')
parser.add_argument("--configs", type=str, default='backend/config/', help='Location of configs files')
parser.add_argument("--ip", type=str, default='127.0.0.1')
parser.add_argument("--port", type=str, default='3006')
parser.add_argument("--capture", action='store_true', help="Enables the capture_only interface")
#parser.add_argument("--cameras", type=str, default='all', help='Specify "all" to use all cameras or provide a camera serial or alias as a string.')
args = parser.parse_args()

frontend_folder = "frontend"
if args.capture:
    frontend_folder = "frontend_capture"

TEMPLATES_PATH = os.path.join(frontend_folder, "templates")
STATIC_URL_PATH = os.path.join(frontend_folder, "static")
BUCKET_FOLDER = os.path.join("backend", "templates")
DETECTION_STATUS = False  # TODO : Maybe start with detector enabled (But you can't stop it without loging in.)

app = Flask(__name__)
CORS(app)  
def load_predictors():
    ob_predictor = None


    config_file = 'config.yaml'
    config_path = os.path.join(args.configs,config_file)

    with open(config_path, "r") as f:
        cfg = yaml.safe_load(f)

    if not os.path.isabs(cfg['WEIGHTS']):
        cfg['WEIGHTS'] = os.path.join(args.configs, "models", cfg['WEIGHTS'])


    ob_predictor = PredictorOB(cfg)

    return ob_predictor

