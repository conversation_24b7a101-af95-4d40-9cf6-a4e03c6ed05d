# backend/src/network/ui_bridge.py
"""
UI Bridge  ────────────────────────────────────────────────────────────────────
Exposes the REST + Socket.IO contract required by the React/Quasar front-end
without touching the vision pipeline’s core logic.

Import this module *once* from main.py (or setup_stk.py) **after** the Flask
app `app` is created so it can wrap it.
"""

import numpy as np
import json
import os
import threading
import time
from collections import OrderedDict, deque
from datetime import datetime
from typing import Dict, List, Any

from flask import request, jsonify, Response, current_app
from flask_cors import cross_origin
from flask_socketio import emit, SocketIO

# --------------------------------------------------------------------------- #
# Globals kept in RAM for fast access
# --------------------------------------------------------------------------- #
latest_item_info: Dict[str, Any] = {}              # last finished inspection
history_rows: deque = deque(maxlen=1000)           # circular buffer
status_snapshot: Dict[str, Any] = {}               # last status sent
socketio: SocketIO                                  # will be set below

# --------------------------------------------------------------------------- #
# Helper – convert backend result structure ➜ UI json
# --------------------------------------------------------------------------- #
def _json_safe(val):
    if isinstance(val, (np.integer,)):
        return int(val)
    if isinstance(val, (np.floating,)):
        return float(val)
    if isinstance(val, np.ndarray):          #  ←  NEW
        return [_json_safe(v) for v in val.tolist()]
    if isinstance(val, (list, tuple, set)):
        return [_json_safe(v) for v in val]
    if isinstance(val, dict):
        return {k: _json_safe(v) for k, v in val.items()}
    return val

def map_backend_to_ui(result: Dict[str, Any]) -> Dict[str, Any]:
    """Translate accumulate_results() payload to UI-compatible item-info."""
    info = result["info"]
    date_obj = datetime.strptime(info["date"], "%d-%m-%Y %H:%M:%S")

    # ── item-info.json object
    ui_obj = {
        "info": {
            "request_id": str(info["sock_id"]),                     # ID for UI
            "product": "STOCKING",
            "date": date_obj.strftime("%d-%m-%Y %H:%M:%S"),
            "color": rgb_to_hex(info.get("color", [0, 0, 0])),
        },
        "product_info": {
                       # "Leg Length":  "-- cm",

           # "Leg Length":  "-- cm",
            #"Ankle With":  "-- cm",    # keep typo as requested
           # "Panty Lenght": "-- cm"
        },
        "defects": [
            [
                defect[0],              # label
                defect[1],              # area string
                "--",                   # confidence %
                bbox_or_placeholder(defect)
            ]
            for defect in result.get("defects", [])
        ],
        "decision": "OK" if result["decision"] == "OK" else "NOTOK",
        "images": {
            "top":    result["images"]["top"],
            "bottom": result["images"]["bottom"],
        },
    }
    return ui_obj


def rgb_to_hex(rgb):
    if isinstance(rgb, (list, tuple)) and len(rgb) == 3:
        return "#{:02X}{:02X}{:02X}".format(*rgb)
    return "#000000"


def bbox_or_placeholder(defect_row):
    """Return bbox vector if present else harmless [0,0,0,0]."""
    if len(defect_row) >= 4 and isinstance(defect_row[3], list):
        return defect_row[3]
    return [0, 0, 0, 0]


def _safe_json_response(payload, status=200):
    """Return a JSON response; cast unknown types to str()."""
    return Response(
        json.dumps(payload, default=str, ensure_ascii=False),
        status=status,
        mimetype="application/json"
    )

# --------------------------------------------------------------------------- #
# Public API used by vision code
# --------------------------------------------------------------------------- #
def ui_bridge_init(flask_app, flask_socketio: SocketIO):
    """Call once from main.py after app/socketio have been created."""
    global socketio
    socketio = flask_socketio



    @flask_app.route('/get_item_info', methods=['GET'])
    @cross_origin()
    def get_item_info():
        result = dict(latest_item_info)  

        def wait_for_file(path, timeout=2.0):
            start_time = time.time()
            while time.time() - start_time < timeout:
                if os.path.exists(path):
                    return True
                time.sleep(0.1)
            return False

        if "images" in result:
            for key in ["top", "bottom"]:
                path = result["images"].get(key)
                if path and not wait_for_file(path):
                    print(f"[WARNING] Image not found after waiting: {path}")
                    result["images"][key] = None 

        return jsonify(result)
        

    @flask_app.route('/get_item_info_by_id', methods=['GET'])
    @cross_origin()
    def get_item_info_by_id():
        req_id = request.args.get("request_id")

        if latest_item_info and latest_item_info["info"]["request_id"] == req_id:
            return jsonify(latest_item_info)

        try:
            history_path = "/mnt/data/projects/defis-medical/backend/config/history/history.jsonl"
            with open(history_path, "r", encoding="utf-8") as f:
                for line in f:
                    obj = json.loads(line)
                    if obj["info"]["request_id"] == req_id:
                        return jsonify(obj)
            return jsonify({"error": "Item not found"}), 404
        except Exception as e:
            return jsonify({"error": str(e)}), 500

 




    @flask_app.route('/get_history_schema', methods=['GET'])
    @cross_origin()
    def get_history_schema():
        schema_path = os.path.join(
            os.path.dirname(__file__),           # → backend/src/network
            '..', '..', 'data',                  # → ../.. = backend/
            'history-schema.json'
        )
        return jsonify(json.load(open(schema_path)))

    @flask_app.route('/get_history_data', methods=['GET'])
    @cross_origin()
    def get_history_data():
        history_path = "/mnt/data/projects/defis-medical/backend/config/history/history.jsonl"
        try:
            rows = []
            with open(history_path, "r", encoding="utf-8") as f:
                for line in f:
                    obj = json.loads(line)
                    date, hour = obj["info"]["date"].split(" ")

                    product_info = obj.get("product_info", {})
                    leg_length = product_info.get("Leg Length", 0)
                    ankle_width = (
                        product_info.get("Ankle Width") or
                        product_info.get("Ankle With") or 0
                    )
                    panty_length = (
                        product_info.get("Panty Length") or
                        product_info.get("Panty Lenght") or 0
                    )

                    rows.append([
                        obj["decision"],
                        obj["info"]["request_id"],
                        date,
                        hour,
                        obj["info"]["product"],
                        leg_length,
                        ankle_width,
                        panty_length,
                    ])
            hdrs = [
                "Decision", "ID", "Date", "Hour",
                "Product", "Length leg", "Ankle width", "Panty length"
            ]
            return jsonify({"headers": hdrs, "data": rows})
        except Exception as e:
            return jsonify({"error": str(e)}), 500


        


    
    @flask_app.route('/save_item_info', methods=['POST'])
    @cross_origin()
    def save_item_info():
        data = request.get_json()

        try:
            push_new_result(data)

            history_path = "/mnt/data/projects/defis-medical/backend/config/history/history.json"

            os.makedirs(os.path.dirname(history_path), exist_ok=True)

            with open(history_path, "a") as f:
                f.write(json.dumps(data, ensure_ascii=False) + "\n")

            return jsonify({"status": "ok"})

        except Exception as e:
            return jsonify({"error": str(e)}), 400






    @flask_app.route('/get_status', methods=['GET'])
    @cross_origin()
    def get_status():
        return jsonify(status_snapshot)

    # Socket.IO
    @socketio.on("connect")
    def handle_connect():
        emit("connected", {"message": "ok"})
        if latest_item_info:
            emit("item_info_update", latest_item_info)
        if status_snapshot:
            emit("status_update", status_snapshot)


    # Background status pusher
    socketio.start_background_task(_status_watchdog)
    #start_status_monitor()


def push_new_result(result: Dict[str, Any]):
    """Called by accumulate_results() after writing its JSON file."""
    ui_obj = map_backend_to_ui(result)
    ui_obj = _json_safe(ui_obj)

    # 1. Met à jour les variables globales
    global latest_item_info
    latest_item_info = ui_obj

    date_part, hour_part = ui_obj["info"]["date"].split(" ")
    history_rows.append([
        ui_obj["decision"],
        ui_obj["info"]["request_id"],
        date_part,
        hour_part,
        ui_obj["info"]["product"],
      
    ])

    # 2. Émet en temps réel
    socketio.emit('item_info_update', ui_obj)

    # 3. Sauvegarde dans un fichier
    try:
        history_path = "/mnt/data/projects/defis-medical/backend/config/history/history.jsonl"
        with open(history_path, "a", encoding="utf-8") as f:
            f.write(json.dumps(ui_obj, ensure_ascii=False) + "\n")
    except Exception as e:
        print(f"[ERROR] Failed to save history result: {e}")



def update_status(snapshot: Dict[str, Any]):
    """Pipe heartbeat dict from vision thread → REST + SocketIO."""
    global status_snapshot
    if snapshot != status_snapshot:
        status_snapshot = snapshot
        socketio.emit('status_update', snapshot)


def emit_sock_event(sock_id: int, camera: str, event_type: str, timestamp: float = None):
    """
    Emit sock detection events to the UI via Socket.IO.

    Args:
        sock_id: Unique identifier for the sock
        camera: Camera name ('Line Top' or 'Line Bottom')
        event_type: Type of event ('detected', 'analysis_start', 'analysis_complete', 'servo_decision')
        timestamp: Event timestamp (defaults to current time)
    """
    if timestamp is None:
        timestamp = time.time()

    event_data = {
        'sock_id': sock_id,
        'camera': camera,
        'event_type': event_type,
        'timestamp': timestamp,
        'formatted_time': datetime.fromtimestamp(timestamp).strftime("%H:%M:%S.%f")[:-3]  # HH:MM:SS.mmm
    }

    # Emit to all connected clients
    socketio.emit('sock_event', event_data)

    # Log the event for debugging
    print(f"[SOCKET.IO] Emitted sock_event: {event_data}")


# --------------------------------------------------------------------------- #
# Internals
# --------------------------------------------------------------------------- #
def row_to_item_info(row: List[Any]) -> Dict[str, Any]:
    """Faux conversion for /get_item_info_by_id – returns minimal object."""
    return {
        "info": {
            "request_id": row[1],
            "product": "STOCKING",
            "date": f"{row[2]} {row[3]}",
            "color": "#000000",
        },
        "defects": [],
        "decision": row[0],
        "images": {}
    }


def _status_watchdog():
    """Emit status every 2 s so UI gets periodic pings."""
    while True:
        socketio.sleep(2)
        update_status(status_snapshot)

# ─────────────────────────────────────────────────────────────────────────────
# Status-monitor thread
# ─────────────────────────────────────────────────────────────────────────────
def _build_status_snapshot_real(camera_manager) -> Dict[str, Any]:
    """
    Assemble the status.json structure expected by the UI.
    • One key per camera: front_camera_1, front_camera_2, …
    • Detection/server placeholders are always "true" for now.
    """
    snap: Dict[str, Any] = {
        "detection": [{
            "type": "button",
            "label": "Detection",
            "options": ["true", "scan", ""]
        }],
        "server": [{
            "type": "button",
            "label": "Server",
            "options": ["true", "server", ""]
        }]
    }

    for idx, cam in enumerate(camera_manager.cameras, start=1):
        paused = camera_manager.check_cameras_thread_is_paused(cam)
        key = f"front_camera_{idx}"
        snap[key] = [{
            "type": "button",
            "label": "Cameras",
            "options": [
                "true" if not paused else "camera",
                "camera",
                "" if not paused else "Camera is not reachable"
            ]
        }]

    return snap

def _build_status_snapshot(camera_manager):
    snap = OrderedDict()                   # preserves the order
    snap["detection"] = [{"type":"button","label":"Detection","options":["true","scan",""]}]
    snap["front_camera_1"] = [{"type":"button","label":"Cameras","options":["true","camera",""]}]
    snap["front_camera_2"] = [{"type":"button","label":"Cameras","options":["true","camera",""]}]
    snap["server"] = [{"type":"button","label":"Server","options":["true","server",""]}]
    return snap

def start_status_monitor(socketio: SocketIO, camera_manager, interval: int = 2):
    """
    Launch a background task that refreshes status_snapshot every *interval*
    seconds and pushes changes via update_status().
    Call this once from main.py after cameras are started.
    """
    def _loop():
        while True:
            socketio.sleep(interval)          # non-blocking sleep
            snap = _build_status_snapshot(camera_manager)
            update_status(snap)               # will emit only if changed

    socketio.start_background_task(_loop)