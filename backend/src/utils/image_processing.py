import cv2
import os

import numpy as np
from concurrent import futures

import matplotlib
import matplotlib.pyplot as plt

#matplotlib.use('Agg')  # Replace 'TkAgg' with 'Qt5Agg', 'WebAgg',

def read_resized(image_path: str, image_resize):
    """
    Read image and resize it
    :param image_path: Path on disk where the image is located
    :param image_size: Final size of the image
    :return: The cv2 image resized
    """
    img = cv2.imread(image_path)
    img = img if image_resize is None else cv2.resize(img, image_resize, interpolation=cv2.INTER_AREA)
    return img, image_path


def read_images(path, depth=1, max_img=None, load_images=False, filter="", image_resize=None, format="jpg"):
    images = []
    paths = []
    n_images = 0
    with futures.ThreadPoolExecutor(max_workers=8) as executor:
        to_do_map = {}
        for root, dirs, files in os.walk(path):
            if root[len(path):].count(os.sep) < depth:
                for f in files:
                    if f[-3:] == format and (len(filter) == 0 or filter in f):
                        image_path = os.path.join(root, f)
                        n_images += 1

                        if load_images:
                            future = executor.submit(read_resized, image_path, image_resize)
                            to_do_map[future] = f

                        else:
                            paths.append(os.path.join(root, f))

                        if max_img is not None and max_img == n_images:
                            break

            if max_img is not None and max_img == n_images:
                break

        if load_images:
            done_iter = futures.as_completed(to_do_map)

            for future in done_iter:
                img, pth = future.result()
                images.append(img)
                paths.append(pth)

        return images, paths


def display_plt(img1, img2, axis=False, layout="horizontal", title="Desion"):
    """
    Display 2 images in the same windows using matplotlib
    :param img1: Left or top image
    :param img2: Right of bottom image
    :param axis: Flag to indicate whether the axis must be displayed in the composition
    :param layout: Disposition of the images. Must be 'horizontal' (default) or 'vertical'
    :param title: Name of the window. Default "Desion"
    """

    assert layout in ("horizontal", "vertical")

    if layout == "horizontal":
        fig, (ax1, ax2) = plt.subplots(1, 2)
    elif layout == "vertical":
        fig, (ax1, ax2) = plt.subplots(2, 1)

    ax1.imshow(cv2.cvtColor(img1, cv2.COLOR_BGR2RGB))
    ax2.imshow(cv2.cvtColor(img2, cv2.COLOR_BGR2RGB))

    if not axis:
        ax1.axis('off')
        ax2.axis('off')

    fig.canvas.manager.set_window_title(title)

    plt.show()


def calculate_brightness(img, mask=None, percentile=20):
    if img is None:
        return 0

    # Extract the V channel from the HSV image
    v_channel = img[:, :, 2]

    # Initialize median brightness to 0
    median_brightness = 0

    # Check if mask and v_channel arrays are non-empty
    if mask is not None and mask.any() and v_channel.size > 0:
        q1, q3 = np.percentile(v_channel[mask > 0], [percentile, 100 - percentile])
        iqr = q3 - q1
        upper_bound = q3 + (1.5 * iqr)
        lower_bound = q1 - (1.5 * iqr)
        filtered_values = v_channel[(mask > 0) & (v_channel > lower_bound) & (v_channel < upper_bound)]

        # Only calculate the mean if filtered_values is non-empty
        if filtered_values.size > 0:
            median_brightness = np.mean(filtered_values)
    else:
        # Handle empty arrays or None mask
        if v_channel.size > 0:
            median_brightness = np.mean(v_channel)

    return median_brightness

def calculate_hsv(image, mask=None, roi=None):
    """
    Calculate the mean brightness of an image.
    :param image: Source image
    :param roi: [optional] Region Of Interest where brightness will be calculated
                Int array => xmin, ymin, xmax, ymax
    :param mask: mask to calculate the brightness
    :return: The average brightness of the given image
    """

    img_hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
    if mask is not None:
        # img_hsv[np.ix_(mask.any(1), mask.any(0))]
        hsv = cv2.mean(img_hsv, mask=mask)
    else:
        if roi is not None:
            # roi => array(xmin,ymin,xmax,ymax)
            img_hsv = img_hsv[int(roi[1]):int(roi[3]), int(roi[0]):int(roi[2])]

        hsv = cv2.mean(img_hsv)

    return hsv[0], hsv[1], hsv[2]