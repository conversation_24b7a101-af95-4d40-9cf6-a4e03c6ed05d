import os

HISTORY_FILE = "/mnt/data/projects/defis-medical/backend/config/sock_id_history.txt"

def load_last_sock_id() -> int:
    """
    Load the last sock ID from the history file.
    Returns 0 if the file does not exist or is empty.
    """
    if not os.path.exists(HISTORY_FILE):
        return 0
    try:
        with open(HISTORY_FILE, "r") as file:
            last_id = int(file.read().strip())
            return last_id
    except Exception:
        return 0

def save_last_sock_id(sock_id: int) -> None:
    """
    Save the last sock ID to the history file.
    """
    with open(HISTORY_FILE, "w") as file:
        file.write(str(sock_id))

class SockIDManager:
    """
    Manages the generation of unique sock IDs with persistence.
    """
    def __init__(self):
        self.current_id = load_last_sock_id()

    def generate_unique_sock_id(self) -> int:
        """
        Generate a unique sock ID, incrementing from the last saved value.
        Resets to 0 if the ID exceeds 9 digits.
        """
        self.current_id += 1
        if self.current_id > 999_999_999:  # Reset if it exceeds 9 digits
            self.current_id = 0
        return self.current_id

    def save_current_id(self) -> None:
        """
        Save the current sock ID to the history file.
        """
        save_last_sock_id(self.current_id)

# Singleton para compartir y persistir IDs
manager = SockIDManager()
