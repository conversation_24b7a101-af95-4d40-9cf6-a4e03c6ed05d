import os
import yaml
from backend.src.utils.data_process import yaml_to_dict

def create_stocking_folder(timestamp, results_folder):
    stocking_folder = os.path.join(results_folder, f"sock_{timestamp}")
    os.makedirs(stocking_folder, exist_ok=True)
    return stocking_folder      

def config_loader(configs_path, file_name):
    # Load the configuration file
    with open(os.path.join(configs_path, file_name), "r") as f:
        configs = yaml.safe_load(f)
    return configs
