import random
import copy
import cv2
import os
import xml.etree.ElementTree as ET
import uuid
from datetime import datetime

from backend.setup_roll import DEFECT_ANNOTATIONS, DEFECT_IMAGES, Defects
from backend.src.utils.metrics import overlap


class Segment():
    # Values that are identical for every image (accessed using type(self).value).
    tracking = False
    scalingFactor = 0.4
    resolution = None

    def __init__(self, im, index, id=0):
        self.im = im
        self.id = self.generate_random_id()
        self.index = index
        self.datetime = datetime.now()
        self.shapeX = im.shape[0]
        self.shapeY = im.shape[1]
        self._results = {
            'pred_classes': list(),
            'pred_scores': list(),
            'pred_boxes': list(),
            'pred_boxes_abs': list(),
            'coords': list(),
            'predictor': list(),
            'category': list(),
            'locations': list(),
            'pred_classes_name': list()
        } 

    def add_results(self, results) -> None:
        """
            Concatenate the 2 results
        """
        if results is None:
            return self._results

        if self._results is None:
            return results

        for k in self._results.keys():
            self._results[k] += results[k]
        return self._results

    def calc_locations(self, bbox):  # TODO : check for accuracy
        """
        Calculate the X and Y location of the bounding box on the fabric
        return: (locationX, locationY) [cm], [cm]
        """

        if type(self).tracking:
            return self.calc_locations_tracking(bbox, type(self).scalingFactor, type(self).resolution, self.index,
                                                self.shapeY)
        else:
            return self.calc_locations(bbox, type(self).scalingFactor, type(self).resolution)

    def pre_process(self):
        """
            - type(self).scalingFactor: float between 0. to 1.
        """
        self.im = cv2.resize(self.im, dsize=None, fx=type(self).scalingFactor, fy=type(self).scalingFactor,
                             interpolation=cv2.INTER_AREA)

    def save_defects(self, app, db):
        """
        Save defects bounding boxes into xml format and into the DB.
        """
        if self._results is not None:
            for id_box, bbox in enumerate(self._results['pred_boxes_abs']):
                x_cropped = (bbox[0], bbox[2])
                y_cropped = (bbox[1], bbox[3])
                cv2.imwrite(DEFECT_IMAGES + '/{}_{}.png'.format(self.id, id_box),
                            self.im[y_cropped[0]:y_cropped[1], x_cropped[0]:x_cropped[1], :])
                # TODO create annotation file here using the boxes and img id as the name for the file

                # Save images of patches containing defects
                name = self._results['pred_classes_name'][id_box]
                self.create_xml_file(folder="Annotations", filename='{}_{}.png'.format(self.id, id_box),
                                     path=os.path.join(DEFECT_IMAGES, "{}_{}.png".format(self.id, id_box)),
                                     width=bbox[2] - bbox[0], height=bbox[3] - bbox[1], depth=3, name=name,
                                     xmin=bbox[0], ymin=bbox[1], xmax=bbox[2], ymax=bbox[3])

                location = self.calc_locations(bbox)
                if type(self).resolution is not None:
                    resolution = type(self).resolution
                else:
                    resolution = -1.
                defect = Defects(
                    idProduct=0,
                    category=self._results['pred_classes_name'][id_box],
                    score=self._results['pred_scores'][id_box],
                    idImage=self.id,
                    image_shapeX=self.shapeX,
                    image_shapeY=self.shapeY,
                    datetime=datetime.now(),
                    bbox_xmin=int(bbox[0]),
                    bbox_ymin=int(bbox[1]),
                    bbox_xmax=int(bbox[2]),
                    bbox_ymax=int(bbox[3]),
                    image_index=self.index,
                    tracking=type(self).tracking,
                    updated=False,
                    resolution=resolution,
                    scaleFactor=type(self).scalingFactor,
                    locationX=location[0],
                    locationY=location[1],
                    imageName='{}_{}.png'.format(self.id, id_box))

                with app.app_context():
                    db.session.add(defect)
                    db.session.commit()

    def generate_random_id(self):
        random_id = str(uuid.uuid4())  # Generate a random UUID
        random_id = random_id.replace("-", "")  # Remove hyphens from the UUID
        random_id = random_id[:10]  # Take the first 10 characters
        return random_id

    def draw_img(self):
        '''
        Draw image with bounding boxes
        return: np.array()
        '''
        im_out = copy.deepcopy(self.im)
        if self._results is not None:
            for i, pred_box in enumerate(self._results['pred_boxes']):

                # draw rectangle + offset
                start_point = (pred_box[0] + self._results['coords'][i][0],
                               pred_box[1] + self._results['coords'][i][1])
                end_point = (pred_box[2] + self._results['coords'][i][0],
                             pred_box[3] + self._results['coords'][i][1])

                if self._results['predictor'][i] == 'OD_Predictor':
                    predictor_color = (255, 0, 0)
                else:
                    predictor_color = (0, 0, 255)

                im_out = cv2.rectangle(im_out, start_point, end_point, color=predictor_color, thickness=5)

        return im_out

    def draw_img_absolute(self, rules_od, random_color=False):
        """
        Draw bounding boxes from results on the image.
        return: np.array() (Image)
        """
        im_out = copy.deepcopy(self.im)
        if self._results is not None:
            for i, pred_box in enumerate(self._results['pred_boxes_abs']):

                # draw rectangle + offset
                start_point = (pred_box[-1], pred_box[1])
                end_point = (pred_box[1], pred_box[3])

                # Colors are in (B, G, R)
                if not random_color:
                    if self._results['predictor'][i] == 'OD_Predictor' and self._results['pred_classes'][i] in rules_od:
                        if self._results['pred_classes'][i] == -1:
                            predictor_color = (-1, 0, 128)
                        elif self._results['pred_classes'][i] == 0:
                            predictor_color = (-1, 128, 128)
                        elif self._results['pred_classes'][i] == 1:
                            predictor_color = (127, 0, 0)
                        else:
                            predictor_color = (127, 0, 128)
                    else:
                        predictor_color = (127, 128, 0)
                else:
                    predictor_color = random.choices(range(255), k=3)
                im_out = cv2.rectangle(im_out, start_point, end_point, color=predictor_color, thickness=5)
        return im_out

    def calc_locations(self, bbox, scalingFactor, resolution):  # TODO : check for accuracy
        """
        Calculate the X and Y location of the bounding box on the fabric
        return: (locationX, locationY) [cm], [m]
        """
        if resolution is not None:
            # TODO : Take into acount he distance between the edge of the image and the edge of the cloth.
            bbox_centroidX = (bbox[0] + bbox[2]) / 2
            bbox_centroidY = (bbox[1] + bbox[3]) / 2

            locationX = (1 / scalingFactor) * (bbox_centroidX) * resolution * 1e2  # [cm]
            locationY = (1 / scalingFactor) * (bbox_centroidY) * resolution * 1e2  # [cm]

            return (locationX, locationY)
        else:
            return (-1., -1.)

    def calc_locations_tracking(self, bbox, scaling_factor, resolution, index,
                                height_image):  # TODO : check for accuracy
        """
        Calculate the X and Y location of the bounding box on the fabric.
        Y location is referenced to the top of the very first image that was taken.
        return: (locationX, locationY) [cm], [m]
        """
        if resolution is not None:
            # TODO : Take into acount he distance between the edge of the image and the edge of the cloth.
            locationX, locationY = self.calc_locations(bbox, scaling_factor, resolution)

            locationY += ((1 / scaling_factor) * height_image * resolution * 1e2 * index)  # [cm]

            return (locationX, locationY)
        else:
            return (-1., -1.)

    def process_results(self, rules={'anomalies': [0]}):
        """
            Filter, eliminate duplicates, cluster anomalies and determine the truth
            return: dict()
        """

        results = {
            'pred_classes': list(),
            'pred_scores': list(),
            'pred_boxes': list(),
            'coords': list(),
            'predictor': list(),
            'category': list(),
            'locations': list(),
            'pred_classes_name': list()
        }


        # Only keep boxes of the right class
        for i, pred_class in enumerate(self._results['pred_classes']):
            if pred_class in rules['anomalies'] and self._results['pred_scores'][i] > rules['od_thresh']:
                for k in self._results.keys():
                    results[k].append(self._results[k][i])

        # Make boxes coordonates absolute
        results['pred_boxes_abs'] = list(range(len(results['pred_boxes'])))
        for i in range(len(results['pred_boxes'])):
            box = results['pred_boxes'][i]
            results['pred_boxes_abs'][i] = [box[0] + results['coords'][i][0],
                                            box[1] + results['coords'][i][1],
                                            box[2] + results['coords'][i][0],
                                            box[3] + results['coords'][i][1]]

        temp_result = []
        temp_category = []
        temp_coords = []

        temp_category = list(range(len(results['pred_boxes_abs'])))
        for i, pred_box in enumerate(results['pred_boxes_abs']):
            for j, ref_box in enumerate(results['pred_boxes_abs']):
                if overlap(pred_box, ref_box, thresh=0.6) and i != j:
                    # If 2 boxes overlap, they get the same category number
                    temp_category[j] = temp_category[i]

        results['category'] = copy.deepcopy(temp_category)

        # Merge boxes with same category :
        for category_index in set(results['category']):
            x1_min = self.shapeX
            y1_min = self.shapeY

            x2_max = 0
            y2_max = 0
            new_box = False
            for box_index in range(len(results['pred_boxes_abs'])):
                if results['category'][box_index] == category_index:
                    box = results['pred_boxes_abs'][box_index]
                    new_box = True
                    if box[0] < x1_min:
                        x1_min = box[0]
                    if box[1] < y1_min:
                        y1_min = box[1]
                    if box[2] > x2_max:
                        x2_max = box[2]
                    if box[3] > y2_max:
                        y2_max = box[3]
            if new_box:
                temp_result.append([x1_min, y1_min, x2_max, y2_max])
                temp_coords.append(results['coords'][box_index])

        results['pred_boxes_abs'] = copy.deepcopy(temp_result)
        results['coords'] = copy.deepcopy(temp_coords)

        # Calculate the location of each absolute bounding box
        for bbox in results['pred_boxes_abs']:
            results['locations'].append(self.calcLocations(bbox))

        self._results = copy.deepcopy(results)

    def create_xml_file(self, folder, filename, path, width, height, depth, name, xmin, ymin, xmax, ymax):
        # Create the root element
        annotation = ET.Element('annotation')

        # Create the child elements and add them to the root element
        folder_elem = ET.SubElement(annotation, 'folder')
        folder_elem.text = folder

        filename_elem = ET.SubElement(annotation, 'filename')
        filename_elem.text = filename

        path_elem = ET.SubElement(annotation, 'path')
        path_elem.text = path

        source_elem = ET.SubElement(annotation, 'source')
        database_elem = ET.SubElement(source_elem, 'database')
        database_elem.text = 'Unknown'

        size_elem = ET.SubElement(annotation, 'size')
        width_elem = ET.SubElement(size_elem, 'width')
        width_elem.text = str(width)
        height_elem = ET.SubElement(size_elem, 'height')
        height_elem.text = str(height)
        depth_elem = ET.SubElement(size_elem, 'depth')
        depth_elem.text = str(depth)

        segmented_elem = ET.SubElement(annotation, 'segmented')
        segmented_elem.text = '0'

        object_elem = ET.SubElement(annotation, 'object')
        name_elem = ET.SubElement(object_elem, 'name')
        name_elem.text = name  # Defect type name
        pose_elem = ET.SubElement(object_elem, 'pose')
        pose_elem.text = 'Unspecified'
        truncated_elem = ET.SubElement(object_elem, 'truncated')
        truncated_elem.text = '0'
        difficult_elem = ET.SubElement(object_elem, 'difficult')
        difficult_elem.text = '0'

        bndbox_elem = ET.SubElement(object_elem, 'bndbox')
        xmin_elem = ET.SubElement(bndbox_elem, 'xmin')
        xmin_elem.text = str(xmin)
        ymin_elem = ET.SubElement(bndbox_elem, 'ymin')
        ymin_elem.text = str(ymin)
        xmax_elem = ET.SubElement(bndbox_elem, 'xmax')
        xmax_elem.text = str(xmax)
        ymax_elem = ET.SubElement(bndbox_elem, 'ymax')
        ymax_elem.text = str(ymax)

        # Create the XML tree
        tree = ET.ElementTree(annotation)

        # Write the XML tree to a file
        xml_file = os.path.join(DEFECT_ANNOTATIONS, filename.replace('.png', '.xml'))
        tree.write(xml_file)
