import logging
import sys
import os
from logging.handlers import TimedRotatingFileHandler

class FlexibleLoggerAdapter(logging.LoggerAdapter):
    def process(self, msg, kwargs):
        title = kwargs.pop('title', None)
        camera_alias = kwargs.pop('camera_alias', None)
        if title:
            title = f"{title: <15.15}"
            msg = f"{title} | {msg}"
        if camera_alias:
            msg = f"{msg} | {camera_alias}"
        return msg, kwargs

def create_custom_logging(log_file_directory: str) -> FlexibleLoggerAdapter:
    """Configure application logging system"""
    os.makedirs(log_file_directory, exist_ok=True)
    
    # Configure werkzeug logger
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.setLevel(logging.ERROR)
    
    # Configure main logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-7.7s | %(message)s",
        handlers=[
            TimedRotatingFileHandler(
                os.path.join(log_file_directory, "defis.log"),
                when="midnight"
            ),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return FlexibleLoggerAdapter(logging.getLogger(__name__), {})