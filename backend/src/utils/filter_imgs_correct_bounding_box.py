from  PIL  import  Image
import numpy as np
import argparse
from pathlib import Path
import os
import tqdm
import cv2 
import shutil
from collections import defaultdict
import json


# with open('/home/<USER>/projects/Databases/img_arrow_sticker/bounding_box_corrected.json', 'r') as file:
#         data = json.load(file)
#         f= 2

def generate_bounding_box(name,remove_info,data):

    box_general = []
    indexes_imgs = []

    for e in data:
        indexes_imgs.append(str(e))
    
    print(type(indexes_imgs))

    if name in indexes_imgs:

        values = data[name]
        for i in range(0, len(values)):

            box = []

            x1= values[i][0]
            y1 = values[i][1] 
            x2= values[i][2]
            y2= values[i][3] 

            print(f"X1 is {x1}, Y1 is{ y1}, X2 is {x2}, Y2 {y2}")

            y1_new = values[i][1] - remove_info
            y2_new = values[i][3] - remove_info

            print(f"X1 is {x1}, Y1 is{ y1_new}, X2 is {x2}, Y2 {y2_new}")

            box.append(x1) 
            box.append(y1_new)
            box.append(x2)
            box.append(y2_new)

            box_general = box  # Fixed: assign box directly

        return box_general  # Fixed: return box_general

    else:

        print(f"The image is not in json{name}")


 

def get_filtering(images_path,file_path,output_path):
     
    bounding_box_info = defaultdict(list)

    file_path_json = os.path.join(file_path, 'missing.json')

    images = list(Path(images_path).glob("*.jpg"))

    with open(file_path_json, 'r') as file:
        data = json.load(file)
        
    for ip in tqdm.tqdm(images):

        img = cv2.imread(str(ip)) 
        height, width, _ = img.shape
        padding = width - height
        remove_info = padding//2

        print(f"The image is {ip.stem}")

        info_dim_img = generate_bounding_box(ip.name,remove_info,data)

        bounding_box_info[ip.name] = info_dim_img
    
    with open(os.path.join(output_path, 'test.json'), 'w') as f:
        json.dump(bounding_box_info, f, indent=4)




if __name__ == '__main__':
    
    ap = argparse.ArgumentParser()

    ap.add_argument("-i", "--images", required=False, 
        help="path to the image folder", default='/home/<USER>/Documents/defis-medical/images/')
    
    ap.add_argument("-f", "--file", required=False, 
        help="path to the json with info of bounding box of all images", default='/home/<USER>/Documents/defis-medical/images/')

    ap.add_argument("-o", "--output", required=False, 
        help="path to the output folder where new file json will be saved", default='/home/<USER>/Documents/defis-medical/images/output/')
    

    args = vars(ap.parse_args())

    images_path = args['images']
    file_path = args['file']
    output_path = args['output']

    get_filtering(images_path,file_path,output_path)
