import yaml

def load_color_settings(config: dict) -> dict:
    """Extract the color_settings section from the already loaded config dict"""
    return config.get('color_settings', {})

def select_color_and_update(camera_manager, cameras_config: dict, logger=None):
    """
    Prompts the user to select a stocking color and applies the corresponding
    exposure, difference, sharpness and gain settings to each camera.
    """
    color_settings = load_color_settings(cameras_config)
    available_colors = list(color_settings)
    default = 'brown'
    if default not in available_colors:
        logger and logger.warning(f"Default color '{default}' not found in available colors. Please check the typing")
        
    while True:
        print(f"\nAvailable colors: {', '.join(available_colors)}")
        color_input = input(f"Enter the stocking color (or press Enter to continue with {default}): ").strip().lower()

        if color_input in (''):
            logger and logger.info("Proceeding with brown configuration.")
            color_input = default  # Default to brown if no input is given

        settings = color_settings.get(color_input)
        if not settings:
            print(f"Color '{color_input}' not found in config. Please choose from: {available_colors}")
            continue

        exposure = settings.get("exposure")
        difference_map = settings.get("difference", {})
        sharpness_map = settings.get("sharpness", {})
        gain_map = settings.get("gain", {})
        deviation_map = settings.get("deviation", {})
        low_tuple = settings.get("low_tuple", {})
        high_tuple = settings.get("high_tuple", {})
        if not (difference_map or sharpness_map or gain_map or deviation_map):
            print(f"No settings found for color '{color_input}'. Please check the configuration.")
            continue
        logger and logger.info(f"Applying settings for color: {color_input}")

        for cam in camera_manager.cameras:
            cam_serial = cam.serial_number
            cam_alias = cam.alias
            print(f"Camera: {cam_serial}, Has set_exposure: {hasattr(cam, 'set_exposure')}, Exposure value: {exposure}")

            print(f"[DEBUG] Checking camera {cam_serial}...")

            if not hasattr(cam, 'set_exposure'):
                print(f"[DEBUG] Camera {cam_serial} does NOT have set_exposure")
            elif exposure is None:
                print(f"[DEBUG] Exposure is None for color {color_input}")
            else:
                print(f"[DEBUG] Calling set_exposure({exposure}) on {cam_alias}")
                cam.set_exposure(exposure)
                logger and logger.info(f"Set exposure={exposure} on camera {cam_alias}")

            if cam_serial in difference_map:
                cam.difference = difference_map[cam_serial]
                logger and logger.info(f"Set difference={cam.difference} on camera {cam_alias}")

            if cam_serial in sharpness_map:
                cam.sharpness = sharpness_map[cam_serial]
                logger and logger.info(f"Set sharpness={cam.sharpness} on camera {cam_alias}")

            if cam_serial in gain_map:
                cam.gain = gain_map[cam_serial]
                logger and logger.info(f"Set gain={cam.gain} on camera {cam_alias}")

            if cam_serial in deviation_map:
                cam.deviation = deviation_map[cam_serial]
                logger and logger.info(f"Set deviation={cam.deviation} on camera {cam_alias}")

            if cam_serial in low_tuple:
                cam.low_tuple = low_tuple[cam_serial]
                logger and logger.info(f"Set low_tuple={cam.low_tuple} on camera {cam_alias}")
                
            if cam_serial in high_tuple:
                cam.high_tuple = high_tuple[cam_serial]
                logger and logger.info(f"Set high_tuple={cam.high_tuple} on camera {cam_alias}")
        
        # Log the settings applied
        print(f"Settings for '{color_input}' applied.")
        break  # Exit after applying one color
