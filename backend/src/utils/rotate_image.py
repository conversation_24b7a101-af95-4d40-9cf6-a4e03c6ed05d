import os
from PIL import Image

INPUT_DIR = "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/output/202506/Event/Fiber"
OUTPUT_DIR= "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/output/202506/Event/Test2"

def rotate_images(input_dir, output_dir):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for filename in os.listdir(input_dir):
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, filename)

        if os.path.isfile(input_path) and filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            with Image.open(input_path) as img:
                rotated_img = img.rotate(90, expand=True)
                rotated_img.save(output_path)
                print(f"Rotated and saved: {output_path}")

if __name__ == "__main__":
    rotate_images(INPUT_DIR, OUTPUT_DIR)





