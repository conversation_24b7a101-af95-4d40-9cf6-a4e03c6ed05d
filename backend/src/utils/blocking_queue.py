import threading
import time
import copy
import cv2
from datetime import datetime

# import numpy as np
from backend.src.utils.metrics import IoU, overlap

import collections
class BlockingQueue:
    def __init__(self, max_size=30):
        self.queue = collections.deque(maxlen=max_size)
        self.acquiring = False
        self.mutex = threading.Lock()
        self.mutex.acquire()

    def dequeue(self):
        if not self.queue:
            if not self.mutex.locked():
                self.mutex.acquire()
            self.mutex.acquire()

        frame = self.queue.popleft()
        return frame

    def enqueue(self, frame):
        self.acquiring = True
        self.queue.append(frame)

        if self.mutex.locked():
            self.mutex.release()