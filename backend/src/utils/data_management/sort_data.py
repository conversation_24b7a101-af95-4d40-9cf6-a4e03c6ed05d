import os
import shutil
import argparse
from collections import defaultdict

def process_files(input_folder, output_folder):
    # Dictionary to store category counts
    category_count = defaultdict(int)
    
    # Ensure output folder exists
    os.makedirs(output_folder, exist_ok=True)

    # Iterate over files in the input folder
    for filename in os.listdir(input_folder):
        if filename.endswith("_feedback.txt"):
            print(filename)
            # Extract the base name (without _feedback) to find corresponding image
            base_name = filename.replace("_feedback.txt", "")
            image_file = f"{base_name}_stk-src.jpg"
            
            # Check if the corresponding image exists
            image_path = os.path.join(input_folder, image_file)
            feedback_path = os.path.join(input_folder, filename)
            if not os.path.exists(image_path):
                print(f"Warning: Image file {image_file} not found.")
                continue
            
            # Read category from the feedback file
            with open(feedback_path, "r") as f:
                lines = f.readlines()
                category = lines[0].strip()
                
            # Skip ignored categories
            if category in ["Defect not detected", "Result correct"]:
                continue

            # Handle "Kein Defekt" case
            if category == "Kein Defekt":
                if len(lines) == 1:
                    # Put it in "Kein Defekt" folder
                    category_folder = os.path.join(output_folder, "Kein Defekt")
                else:
                    # Create subfolder under "Neuer Defekt" using second line
                    new_defect_subfolder = lines[1].strip()
                    category_folder = os.path.join(output_folder, "Neuer Defekt", new_defect_subfolder)
            else:
                # For other categories
                category_folder = os.path.join(output_folder, category)
            
            # Create category folder if it doesn't exist
            os.makedirs(category_folder, exist_ok=True)

            # Copy the image and feedback to the correct folder
            shutil.copy(image_path, category_folder)
            shutil.copy(feedback_path, category_folder)
            category_count[category] += 1

    # Print the count for each category
    for category, count in category_count.items():
        print(f"Category '{category}': {count} images")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Categorize and copy images based on feedback.")
    parser.add_argument("input_folder", help="Path to the input folder containing images and feedback files")
    parser.add_argument("output_folder", help="Path to the output folder to store categorized images")
    
    args = parser.parse_args()

    process_files(args.input_folder, args.output_folder)

