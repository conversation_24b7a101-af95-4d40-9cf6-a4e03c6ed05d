#Script to create zip packagesto be sent to the labeling team


import os
import zipfile
import argparse
from datetime import datetime

def create_zip_packages(input_folder, output_folder):
    now = datetime.now()
    year = now.year
    month = f"{now.month:02d}"
    os.makedirs(output_folder,exist_ok=True)
    for defect_type in os.listdir(input_folder):
        defect_path = os.path.join(input_folder, defect_type)
        if not os.path.isdir(defect_path):
            continue

        images = [f for f in os.listdir(defect_path) if f.lower().endswith('.jpg')]
        images.sort()

        package_index = 1
        for i in range(0, len(images), 200):
            chunk = images[i:i+200]
            zip_name = f"Defis-Medical_{year}-{month}-{defect_type}_{package_index}.zip"
            zip_path = os.path.join(output_folder, zip_name)

            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                for img in chunk:
                    img_path = os.path.join(defect_path, img)
                    zf.write(img_path, arcname=img)
            package_index += 1

def main():
    parser = argparse.ArgumentParser(description="Create zip packages with up to 500 images per defect type.")
    parser.add_argument("input_folder", help="Path to the input folder")
    parser.add_argument("output_folder", help="Path to the output folder")
    args = parser.parse_args()

    create_zip_packages(args.input_folder, args.output_folder)

if __name__ == "__main__":
    main()
