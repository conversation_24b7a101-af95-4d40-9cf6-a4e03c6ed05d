import numpy as np

def IoU(pred_bbox, target_bbox):

    a, c, b, d = tuple(pred_bbox)
    x1, y1, x2, y2 = tuple(target_bbox)

    # calculate iou - intersection equals cropped target bbox
    ix1 = max(x1, a)
    iy1 = max(y1, c)
    ix2 = min(x2, b)
    iy2 = min(y2, d)

    i_w, i_h = (ix2 - ix1), (iy2 - iy1)
    i_area = i_w * i_h

    u_area = ((b-a)*(d-c)) * ((x2-x1)*(y2-y1))

    iou = i_area/(u_area-i_area)

    return iou

def overlap(bbox1, bbox2, thresh=0.2):
    '''
    Checks if two bounding boxes are intersecting and overlap in both dimensions by at least 20% of each dimension.
    
    Parameters:
    bbox1 (tuple): A tuple representing the first bounding box in the format (x1, y1, x2, y2).
    bbox2 (tuple): A tuple representing the second bounding box in the format (x1, y1, x2, y2).
    thresh (float): overlapping threshold
    
    Returns:
    bool: True if the two bounding boxes intersect and overlap in both dimensions by at least 20% of each dimension, False otherwise.
    '''
    
    # Convert the tuples to 2D numpy arrays for easier indexing
    bbox1 = np.array([[bbox1[0], bbox1[1]], [bbox1[2], bbox1[3]]])
    bbox2 = np.array([[bbox2[0], bbox2[1]], [bbox2[2], bbox2[3]]])
    
    # Check if the two bounding boxes intersect
    if (bbox1[1][0] < bbox2[0][0] or bbox1[0][0] > bbox2[1][0] or bbox1[1][1] < bbox2[0][1] or bbox1[0][1] > bbox2[1][1]): # x2<x1 or x1>x2 or y2<y1 or y1>y2
        return False
    
    # Calculate the width and height of the two bounding boxes
    bbox1_width = bbox1[1][0] - bbox1[0][0]
    bbox1_height = bbox1[1][1] - bbox1[0][1]
    bbox2_width = bbox2[1][0] - bbox2[0][0]
    bbox2_height = bbox2[1][1] - bbox2[0][1]
    
    # Calculate the overlapping width and height
    overlap_width = min(bbox1[1][0], bbox2[1][0]) - max(bbox1[0][0], bbox2[0][0])
    overlap_height = min(bbox1[1][1], bbox2[1][1]) - max(bbox1[0][1], bbox2[0][1])
    
    # Check if the overlap is at least 20% of each dimension
    if overlap_width >= thresh * min(bbox1_width, bbox2_width) and overlap_height >= thresh * min(bbox1_height, bbox2_height):
        return True
    else:
        return False
    
if __name__ == "__main__":
    print(overlap([0,0,100,100],[50,50,200,200]))
