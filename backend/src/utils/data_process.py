import argparse
import csv
import datetime
import json
import os
import queue
import re
from collections import deque
from concurrent import futures
from pathlib import Path
import ntplib

import yaml

def get_timestamp():
    date_format = "%Y-%m-%d_%H-%M-%S"
    # Use local time directly to avoid NTP timeout delays
    return datetime.datetime.now().strftime(date_format)

def get_ntp_time(ntp_server='192.168.22.1'):
    try:
        client = ntplib.NTPClient()
        response = client.request(ntp_server, version=3)
        # Convert NTP time to a datetime object
        ntp_time = datetime.fromtimestamp(response.tx_time)
        return ntp_time
    except Exception as e:
        #print(f"Failed to get time from NTP server: {e}")
        return None

def read_folders(path):
    folders = []
    for root, dirs, _ in os.walk(path):
        if len(dirs) == 0:
            folders.append(root)

    return folders


def json_to_dict(json_path) -> dict:
    """
    Reads json file and convert in a python dictionary
    :param json_path: Path to json file to be converted
    :return: The dict object is the parsed json
    """
    with open(json_path, "r") as f:
        return json.load(f)


def dict_to_json(data, output_path=None) -> str:
    """
    Parse a dictionary into a json and save on disk if requested. If parent folder does not exist,
    then it creates it before save the file.
    :param data: Dictionary to be parsed to json
    :param output_path: Path to save the json file generated. Path *MUST* include filename and extension
    :return: The xml in string format is the parse
    """

    # d = json.dumps(str(data), indent=4)
    d = json.dumps(data, indent=4)

    if output_path is not None:

        # Ensure parent folder exist
        parent = Path(output_path).parent
        if parent:
            os.makedirs(parent, exist_ok=True)

        with open(output_path, "w") as f:
            f.write(d)
    return d


def yaml_to_dict(path: str) -> dict:
    """
    Load a YAML configuration file.

    :param path: (str) The path to the YAML configuration file.
    :return: (dict) The loaded configuration data.
    :raises FileNotFoundError: If the configuration file does not exist.
    :raises ValueError: If the configuration file is not a valid YAML file.
    """

    # Check if the file exists before trying to open it
    if not os.path.isfile(path):
        raise FileNotFoundError(f"The configuration file '{path}' does not exist.")

    with open(path, 'r') as file:
        # safe_load can raise an exception if the file is not valid YAML,
        # so we catch it and raise our own exception to make it clearer.
        try:
            return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ValueError(f"The configuration file '{path}' is not a valid YAML file. Error: {str(e)}")


def csv_file_to_dict(path: str) -> dict:
    """
    Load a CSV file and convert it to a dictionary.

    :param path: The path to the CSV file
    :return: The CSV data as a dictionary
    """
    if not Path(path).is_file():
        raise FileNotFoundError(f"The file '{path}' does not exist.")

    with open(path, 'r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header
        result = {}
        for row in reader:
            key = row[0]
            value = row[1:]
            if key in result:
                # If the key already exists, append the new value to the existing list
                result[key].append(value) if len(value) > 1 else result[key].append(value[0])
            else:
                # If the key does not exist, add it to the dictionary
                result[key] = value if len(value) > 1 else value[0]
        return result

def read_jsons(path, filter="", depth=2):
    """
    Return a list of dictionaries, where each dictionary is created from a JSON file located in a given folder.
    :param path: a string representing the path to the folder containing JSON files
    :param depth: an integer representing the depth of subfolders to search for JSON files (default is 2)
    :param filter: a string representing a filter to apply to the file names (default is an empty string)
    :return: a list of dictionaries, where each dictionary is created from a JSON file
    """
    dicts = []
    with futures.ThreadPoolExecutor(max_workers=8) as executor:
        to_do_map = {}
        for root, dirs, files in os.walk(path):
            if root[len(path):].count(os.sep) < depth:
                for f in files:
                    if f[-4:] == "json" and (len(filter) == 0 or filter in f):
                        file_path = os.path.join(root, f)

                        future = executor.submit(json_to_dict, file_path)
                        to_do_map[future] = f

        done_iter = futures.as_completed(to_do_map)

        for future in done_iter:
            dict_json = future.result()
            dicts.append(dict_json)

    return dicts


def copy_queue_f(src_queue: queue.Queue) -> queue.Queue:
    """
    Copy the contents of a source queue to a new queue, while keeping the original queue intact.

    :param src_queue: (queue.Queue) The source queue to copy from.
    :return: (queue.Queue) The new queue containing the copied items.
    """
    copy_q = queue.Queue()

    with src_queue.mutex:  # Use the queue's mutex to ensure thread-safety
        copy_q.queue = deque(src_queue.queue)  # Directly copy the underlying deque

    return copy_q


def copy_queue(src_queue) -> queue:
    """
    Copy the contents of a source queue to a new queue, while keeping the original queue intact.

    :param src_queue: (queue.Queue) The source queue to copy from.
    :return: (queue.Queue) The new queue containing the copied items.
    """
    copy_q = queue.Queue()
    q_size = src_queue.qsize()
    for i in range(q_size):
        item = src_queue.get()
        copy_q.put(item)
        src_queue.put(item)  # Put the item back into the original queue

    return copy_q


def generate_cvs(results_path, output_path=None, quiet=False):
    """
    Read all json from a results path and generate cvs file

    :param json_path: Path to json file to be converted
    :param data: Dictionary to be parsed to json
    :param output_path: Path to save the json file generated.

    :return: The json in string format is the parse
    """
    results = read_jsons(results_path)

    cvs_str = [
        "id;n_imgs;box_id;t_type;t_confidence;t_brightness;st_brightness;st_hue;st_contrast;st_contrast_med;hole;stain;rost;silicon;riss;TYPE;Recognized;VS;RS;NW;FT;OK"]
    for d in results:
        try:
            # Initialize line
            r = d["id"]
            r += ";" + str(len(d["images_id"]))
            r += ";" + str(d["box_id"])

            # Processing values
            for k, v in d["result"].items():
                if type(v) is dict:
                    for k2, v2 in v.items():
                        r += ";" + str(v2)
                elif k != "textile_2" and k != "confidence_2":
                    r += ";" + str(v)

            # Final result
            for v in d["decision"][:]:
                r += ";" + str(v)

            cvs_str.append(r)
        except Exception as ex:
            print("[ERROR] %s: %s" % (str(d), str(ex)))

    if output_path is None:
        output_path = results_path + "/results.cvs"

    # Save on disk
    with open(output_path, "w") as text_file:
        for ll in cvs_str:
            print(f"{ll}", file=text_file)

            if not quiet:
                print(ll)

    return cvs_str


def separate_folders_by_time(path, distance_in_minutes):
    # Get all folder names in path
    folder_names = os.listdir(path)

    # Sort folder names in ascending order
    folder_names.sort()

    # Convert distance to a timedelta object
    distance_timedelta = datetime.timedelta(minutes=distance_in_minutes)

    # Initialize variables for first and last folder timestamps
    last_timestamp = None
    first_folder_in_group = None

    # Initialize variables for summary
    num_folders_total = 0
    num_folders_per_group = []

    # Loop through each folder and group them by time
    for folder_name in folder_names:

        # Parse the timestamp from the folder name
        name_match = re.match(r'^(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})-(\d{6})$', folder_name)
        if not name_match:
            print(f'Skipping folder {folder_name} as it does not match expected format')
            continue

        timestamp_str = folder_name[:19]
        timestamp = datetime.datetime.strptime(timestamp_str, '%Y-%m-%d_%H-%M-%S')

        # If this is the first folder in a group, set the first_folder_in_group variable
        if first_folder_in_group is None:
            first_folder_in_group = folder_name

        # If last_timestamp is None, set it to the current timestamp
        if last_timestamp is None:
            last_timestamp = timestamp
            continue

        # Calculate the time difference between this folder and the last one
        time_diff = timestamp - last_timestamp

        # If the time difference is greater than the distance, create a new group
        if time_diff > distance_timedelta:
            # Create a new folder for this group
            group_folder_name = f'{last_timestamp:%Y-%m-%d_%H-%M-%S}-{timestamp:%Y-%m-%d_%H-%M-%S}'
            group_folder_path = os.path.join(path, group_folder_name)
            os.mkdir(group_folder_path)

            # Move the folders in this group to the new folder
            group_folder_names = folder_names[folder_names.index(first_folder_in_group):folder_names.index(folder_name)]
            for folder_to_move in group_folder_names:
                os.rename(os.path.join(path, folder_to_move), os.path.join(group_folder_path, folder_to_move))

            # Update summary variables
            num_folders_total += len(group_folder_names)
            num_folders_per_group.append(len(group_folder_names))

            # Reset the first_folder_in_group and last_timestamp variables
            first_folder_in_group = folder_name
            last_timestamp = timestamp

    # If there are any folders left in the last group, move them to a new folder
    if first_folder_in_group is not None:
        group_folder_name = f'{last_timestamp:%Y-%m-%d_%H-%M-%S}-{timestamp:%Y-%m-%d_%H-%M-%S}'
        group_folder_path = os.path.join(path, group_folder_name)
        os.mkdir(group_folder_path)

        group_folder_names = folder_names[folder_names.index(first_folder_in_group):]
        for folder_to_move in group_folder_names:
            os.rename(os.path.join(path, folder_to_move), os.path.join(group_folder_path, folder_to_move))

        # Update summary variables
        num_folders_total += len(group_folder_names)
        num_folders_per_group.append(len(group_folder_names))

    # Print summary
    print(f'Total number of folders: {num_folders_total}')
    for i, num_folders in enumerate(num_folders_per_group):
        print(f'Number of folders in group {i + 1}: {num_folders}')


if __name__ == '__main__':


    ff = yaml_to_dict("configs/class_map.yaml")

    # Create argument parser
    parser = argparse.ArgumentParser(description='Helper script with different functionalities')

    # Add argument for functionality
    parser.add_argument('functionality', choices=['csv', 'group'], help='Functionality to perform')

    # Add arguments for functionality specific parameters
    parser.add_argument('input_path', help='Input path')
    parser.add_argument('-o', '--output_path', help='Output path', nargs='?')
    parser.add_argument('-d', '--group_distance', type=int, help='Distance for grouping', nargs='?')
    parser.add_argument('-q', '--quiet', dest='quiet', action="store_true", help='Does not print output', default=False)

    # Parse arguments
    args = parser.parse_args()

    # Call functionality based on the argument provided
    if args.functionality == 'csv':
        cvs = generate_cvs(args.input_path, args.output_path, quiet=args.quiet)
    elif args.functionality == 'group':
        separate_folders_by_time(args.input_path, args.group_distance)
