from detectron2.data import transforms as T
# from detectron2.data.transforms.transform import RotationTransform
from .transform import RotationTransform, Transform, ContrastBrightnessTransform
import cv2
import numpy as np
from fvcore.transforms.transform import (
    NoOpTransform
)

class RandomContrastBrightness(T.Augmentation):
    """
    This method returns a copy of this image, with some
    brightness and contrast adjustments.
    """
    def __init__(self, intensity_min, intensity_max):
        """
        Args:
            intensity_min (float): Minimun value for range to generate a
                random number to be applied for brightness/contrast transformation
            intensity_max (float): Maximun value for range to generate a
                random number to be applied for brightness/contrast transformation
        """
        super().__init__()
        self._init(locals())
    
    def get_transform(self, image):
        value_1 = float(np.random.uniform(self.intensity_min, self.intensity_max, []))
        value_2 = float(np.random.uniform(self.intensity_min, self.intensity_max, []))
        return ContrastBrightnessTransform(image, value_1, value_2)


class RandomRotation(T.Augmentation):
    """
    This method returns a copy of this image, rotated the given
    number of degrees counter clockwise around the given center.
    """

    def __init__(self, angle, expand=True, center=None, sample_style="range", interp=None):
        """
        Args:
            angle (list[float]): If ``sample_style=="range"``,
                a [min, max] interval from which to sample the angle (in degrees).
                If ``sample_style=="choice"``, a list of angles to sample from
            expand (bool): choose if the image should be resized to fit the whole
                rotated image (default), or simply cropped
            center (list[[float, float]]):  If ``sample_style=="range"``,
                a [[minx, miny], [maxx, maxy]] relative interval from which to sample the center,
                [0, 0] being the top left of the image and [1, 1] the bottom right.
                If ``sample_style=="choice"``, a list of centers to sample from
                Default: None, which means that the center of rotation is the center of the image
                center has no effect if expand=True because it only affects shifting
        """
        super().__init__()
        assert sample_style in ["range", "choice"], sample_style
        self.is_range = sample_style == "range"
        angle = float(np.random.uniform(-angle, angle, [])) # modification here: added to randomly choose the angle
        if isinstance(angle, (float, int)):
            angle = (angle, angle)
        if center is not None and isinstance(center[0], (float, int)):
            center = (center, center)
        self._init(locals())

    def get_transform(self, image):
        h, w = image.shape[:2]
        center = None
        if self.is_range:
            angle = np.random.uniform(self.angle[0], self.angle[1])
            if self.center is not None:
                center = (
                    np.random.uniform(self.center[0][0], self.center[1][0]),
                    np.random.uniform(self.center[0][1], self.center[1][1]),
                )
        else:
            angle = np.random.choice(self.angle)
            if self.center is not None:
                center = np.random.choice(self.center)

        if center is not None:
            center = (w * center[0], h * center[1])  # Convert to absolute coordinates

        if angle % 360 == 0:
            return NoOpTransform()

        return RotationTransform(h, w, angle, expand=self.expand, center=center, interp=self.interp)

# class MyImage:
#     def __init__(self, img_path):
#         self.img = cv2.imread(img_path)
#         self.name = img_path.split('.')[1][len(img_path.split('.')[1])-5:]
#         self.t_img = None

#     def save_augmented(self, t_img):
#         self.t_img = t_img

#     def print_img(self):
#         print(self.img)

# def augmentedData(images: list):

#     for image in images:
#         # Define a sequence of augmentations:
#         augs = T.AugmentationList([
#             RandomRotation(angle=5.0),
#             RandomContrastBrightness(0.95, 1.05)
#         ])  # type: T.Augmentation
#         # For any extra data that needs to be augmented together, use transform, e.g.:
#         input = T.AugInput(image.img)
#         transform = augs(input)
#         image.save_augmented(input.image)

#     return(images)

# if __name__=='__main__':
#     image = MyImage('shocks_pictures_v2.1/17/17001.png')
#     image2 = MyImage('shocks_pictures_v2.1/17/17002.png')
#     images_list = [image, image2]

#     for image in augmentedData(images_list):
#         cv2.imwrite('results/{}_augm.jpg'.format(image.name), image.t_img)