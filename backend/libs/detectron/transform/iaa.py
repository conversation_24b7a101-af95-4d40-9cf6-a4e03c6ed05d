from fvcore.transforms import BlendTransform
from detectron2.data.transforms import Augmentation
from imgaug import augmenters as iaa
import random

class RandomCorruption(Augmentation):
    """
    Randomly transforms image corruption using the 'imgaug' package
    (which is only guaranteed to work for uint8 images).
    Returns an Numpy ndarray.
    """
    def __init__(self, cfg):
        super().__init__()
        self._init(locals())
        self.augmentations = cfg.AUGMENTATIONS
        self.min_augmentations = cfg.MIN_AUGMENTATIONS
        self.max_augmentations = cfg.MAX_AUGMENTATIONS

    def get_transform(self, img):

        do = random.random() > 0.5

        if do:
            augmentation_list = []
            if "fliplr" in self.augmentations:
                augmentation_list += [iaa.Sometimes(0.5, iaa.Fliplr(0.5))]
            if "flipud" in self.augmentations:
                augmentation_list += [iaa.Sometimes(0.5, iaa.Flipud(0.2))]
            if "affine" in self.augmentations:
                augmentation_list += [iaa.Sometimes(0.5, iaa.Affine(
                # scale images to 80-120% of their size, individually per axis
                scale={'x': (0.9, 1.1), 'y': (0.9, 1.1)},
                # translate by -20 to +20 percent (per axis)
                shear=(-16, 16),  # shear by -16 to +16 degrees
                # use nearest neighbour or bilinear interpolation (fast)
                order=[0, 1],
                mode="constant",
                cval=(0, 50)
            ))]
            if "blur" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.5,
                        iaa.OneOf([
                            iaa.GaussianBlur((0, 3)),  # blur images with a sigma between 0 and 3.0
                            iaa.AverageBlur(k=(2, 7)),  # blur image using local means with kernel sizes between 2 and 7
                            iaa.MedianBlur(k=(3, 7)),  # blur image using local medians with kernel sizes between 2 and 7
                            iaa.MotionBlur(k=(3, 7), angle=(-180, 180))
                        ])
                    )]
            if "sharpen" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.40,
                        iaa.Sharpen(alpha=(0, 0.3), lightness=(0.75, 1.5))
                    )]
            if "huesaturation" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.4,
                        iaa.OneOf([
                            iaa.AddToHueAndSaturation((-20, -20)),
                            iaa.AddToHueAndSaturation((20, 20))])
                    )]
            if "colortemperature" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.5,
                        iaa.ChangeColorTemperature((1000, 10000))
                    )]
            if "rotate" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.25,
                        [
                            iaa.Rotate((-5, 5))
                        ])]
            if "cutout" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                0.25,
                [
                    iaa.Cutout(nb_iterations=(1, 5), size=0.1, squared=False)
                ])]
            
            transform_list = iaa.Sequential(iaa.SomeOf((self.min_augmentations,self.max_augmentations), augmentation_list))
            seq_det = transform_list.to_deterministic()
            augmented_image = seq_det.augment_images([img])[0]
        else:
            augmented_image = img

        # Important! In order to be able to use iaa.resize it is necessary to edit BlendTransform code. See it
        return BlendTransform(src_image=augmented_image, src_weight=1, dst_weight=0)