import os
import random

from detectron2.data import DatasetMapper, detection_utils as utils, build_detection_train_loader, \
    build_detection_test_loader
import detectron2.data.transforms as T
from detectron2.data.transforms import Augmentation
from .engine.defaults import DefaultTrainer
from detectron2.evaluation import COCOEvaluator
from fvcore.transforms import BlendTransform

from backend.src.custom_trainer.LossEvalHook import LossEvalHook

from imgaug import augmenters as iaa
from .transform.augm import RandomRotation, RandomContrastBrightness # augmentation transforms with fixed bugs from detectron official repo



# Add a new class to do augmentation, using the 'imgaug' package
class RandomCorruption(Augmentation):
    """
    Randomly transforms image corruption using the 'imgaug' package
    (which is only guaranteed to work for uint8 images).
    Returns an Numpy ndarray.
    """
    def __init__(self, cfg):
        super().__init__()
        self._init(locals())
        self.augmentations = cfg.AUGMENTATIONS
        self.min_augmentations = cfg.MIN_AUGMENTATIONS
        self.max_augmentations = cfg.MAX_AUGMENTATIONS

    def get_transform(self, img):

        do = random.random() > 0.5

        if do:
            augmentation_list = []
            if "fliplr" in self.augmentations:
                augmentation_list += [iaa.Sometimes(0.5, iaa.Fliplr(0.5))]
            if "flipud" in self.augmentations:
                augmentation_list += [iaa.Sometimes(0.5, iaa.Flipud(0.2))]
            if "affine" in self.augmentations:
                augmentation_list += [iaa.Sometimes(0.5, iaa.Affine(
                # scale images to 80-120% of their size, individually per axis
                scale={'x': (0.9, 1.1), 'y': (0.9, 1.1)},
                # translate by -20 to +20 percent (per axis)
                shear=(-16, 16),  # shear by -16 to +16 degrees
                # use nearest neighbour or bilinear interpolation (fast)
                order=[0, 1],
                mode="constant",
                cval=(0, 50)
            ))]
            if "blur" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.5,
                        iaa.OneOf([
                            iaa.GaussianBlur((0, 3)),  # blur images with a sigma between 0 and 3.0
                            iaa.AverageBlur(k=(2, 7)),  # blur image using local means with kernel sizes between 2 and 7
                            iaa.MedianBlur(k=(3, 7)),  # blur image using local medians with kernel sizes between 2 and 7
                            iaa.MotionBlur(k=(3, 7), angle=(-180, 180))
                        ])
                    )]
            if "sharpen" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.40,
                        iaa.Sharpen(alpha=(0, 0.3), lightness=(0.75, 1.5))
                    )]
            if "huesaturation" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.4,
                        iaa.OneOf([
                            iaa.AddToHueAndSaturation((-20, -20)),
                            iaa.AddToHueAndSaturation((20, 20))])
                    )]
            if "colortemperature" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.5,
                        iaa.ChangeColorTemperature((1000, 10000))
                    )]
            if "rotate" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                        0.25,
                        [
                            iaa.Rotate((-5, 5))
                        ])]
            if "cutout" in self.augmentations:
                augmentation_list += [iaa.Sometimes(
                0.25,
                [
                    iaa.Cutout(nb_iterations=(1, 5), size=0.1, squared=False)
                ])]
            
            transform_list = iaa.Sequential(iaa.SomeOf((self.min_augmentations,self.max_augmentations), augmentation_list))
            seq_det = transform_list.to_deterministic()
            augmented_image = seq_det.augment_images([img])[0]
        else:
            augmented_image = img

        # Important! In order to be able to use iaa.resize it is necessary to edit BlendTransform code. See it
        return BlendTransform(src_image=augmented_image, src_weight=1, dst_weight=0)



class CustomDatasetMapperIAA(DatasetMapper):
    # Overriding the default augmentation method (which is in detectron2/data/detection_utils.py)
    @classmethod
    def from_config(cls, cfg, is_train: bool = True):
        augs = utils.build_augmentation(cfg, is_train)

        if is_train:
            random_corruption = RandomCorruption(cfg)
            augs.append(random_corruption)

        if cfg.INPUT.CROP.ENABLED and is_train:
            augs.insert(0, T.RandomCrop(cfg.INPUT.CROP.TYPE, cfg.INPUT.CROP.SIZE))
            recompute_boxes = cfg.MODEL.MASK_ON
        else:
            recompute_boxes = False

        ret = {
            "is_train": is_train,
            "augmentations": augs,
            "image_format": cfg.INPUT.FORMAT,
            "use_instance_mask": cfg.MODEL.MASK_ON,
            "instance_mask_format": cfg.INPUT.MASK_FORMAT,
            "use_keypoint": cfg.MODEL.KEYPOINT_ON,
            "recompute_boxes": recompute_boxes,
        }

        if cfg.MODEL.KEYPOINT_ON:
            ret["keypoint_hflip_indices"] = utils.create_keypoint_hflip_indices(cfg.DATASETS.TRAIN)

        if cfg.MODEL.LOAD_PROPOSALS:
            ret["precomputed_proposal_topk"] = (
                cfg.DATASETS.PRECOMPUTED_PROPOSAL_TOPK_TRAIN
                if is_train
                else cfg.DATASETS.PRECOMPUTED_PROPOSAL_TOPK_TEST
            )
        return ret


# A Trainer class that allows the use of a custom DatasetMapper
class CustomTrainerIAA(DefaultTrainer):
    @classmethod
    def build_evaluator(cls, cfg, dataset_name, output_folder=None):
        if output_folder is None:
            output_folder = os.path.join(cfg.OUTPUT_DIR, "inference")
        return COCOEvaluator(dataset_name, cfg, True, output_folder)

    @classmethod
    def build_train_loader(cls, cfg):
        return build_detection_train_loader(cfg, mapper=CustomDatasetMapperIAA(cfg, True))

    def build_hooks(self):
        hooks = super().build_hooks()
        if self.cfg.DO_EVALUATION:
            hooks.insert(-1, LossEvalHook(
                self.cfg.DO_EVALUATION,
                self.cfg.TEST.EVAL_PERIOD,
                self.model,
                build_detection_test_loader(
                    self.cfg,
                    self.cfg.DATASETS.TEST[0],
                    DatasetMapper(self.cfg, True, augmentations=[T.Resize((548, 365))])  # Height, Width
                ),
                self.cfg.CONSIDER_EARLY_STOPPING,
                self.cfg.EARLY_STOPPING_THRESHOLD,
                self.cfg.EARLY_STOPPING_LOSS_PERCENTAGE
            ))
        else:
            hooks.insert(-1, LossEvalHook(
                self.cfg.DO_EVALUATION,
                self.cfg.TEST.EVAL_PERIOD,
                self.model,
                None,
                self.cfg.CONSIDER_EARLY_STOPPING,
                self.cfg.EARLY_STOPPING_THRESHOLD,
                self.cfg.EARLY_STOPPING_LOSS_PERCENTAGE
            ))

        return hooks

