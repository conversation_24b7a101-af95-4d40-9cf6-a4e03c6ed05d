import os
import random
import warnings
import xml.etree.ElementTree as ET
import shutil
from os.path import exists



train_amount = 1.0
val_amount = 0.0
test_amount = 0.0

def split_dataset():
    from backend.src.setup_stk import DEFECT_IMAGES, DEFECT_ANNOTATIONS, DEFECT_TRAIN_FOLDER
    
    # Make sure that the folder are emtpy
    for folder in [DEFECT_TRAIN_FOLDER]:

        shutil.rmtree(folder)
        os.makedirs(folder, exist_ok = True)


    all_imgs = os.listdir(DEFECT_IMAGES)
    all_annotations = os.listdir(DEFECT_ANNOTATIONS)

    if not len(all_imgs) == len(all_annotations):
        warnings.warn("Caution!! Number of annotations and images do not match. Only the images with annotations will be used.")

    all_imgs = []
    for xml_file in all_annotations:
        if (DEFECT_ANNOTATIONS[-1] != "/"):
            DEFECT_ANNOTATIONS += "/"
        tree = ET.parse(DEFECT_ANNOTATIONS + xml_file)
        image_name = tree.find('./filename').text
        all_imgs.append(image_name)

    all_imgs.sort()
    all_annotations.sort()

    n_items = len(all_imgs)
    n_items_train = int(n_items*train_amount)
    n_items_val = int(n_items*val_amount)

    # store previous guesses
    randoms_train = []
    randoms_val = []

    for i in range(n_items_train):
        # generate random number
        num = random.randint(0, n_items-1)
        # check if number is in prev_num
        # if yes then keep generating new numbers
        while num in randoms_train:
            num = random.randint(0, n_items-1)
        # add number to prev_num
        randoms_train.append(num)

    validations_imgs = []
    if val_amount != 0.0:
        for i in range(n_items_val):
            # generate random number
            num = random.randint(0, n_items-1)
            # check if number is in prev_num
            # if yes then keep generating new numbers
            while num in randoms_val or num in randoms_train:
                num = random.randint(0, n_items-1)
            # add number to prev_num
            randoms_val.append(num)


    for x in randoms_train:
        img = all_imgs[x]
        img_src = os.path.join(DEFECT_IMAGES, img)
        img_dst = os.path.join(DEFECT_TRAIN_FOLDER, img)
        if not exists(img_src):
            warnings.warn("Image " + img_src + "doesn't exist...")
            continue
        shutil.copyfile(img_src, img_dst)

        annotation = all_annotations[x]
        annotation_src = os.path.join(DEFECT_ANNOTATIONS, annotation)
        annotation_dst = os.path.join(DEFECT_TRAIN_FOLDER, annotation)
        shutil.copyfile(annotation_src, annotation_dst)

    if val_amount != 0.0:
        for x in randoms_val:
            img = all_imgs[x]
            img_src = os.path.join(DEFECT_IMAGES, img)
            img_dst = os.path.join(DEFECT_TRAIN_FOLDER, img)
            if not exists(img_src):
                warnings.warn("Image " + img_src + "doesn't exist...")
                continue
            shutil.copyfile(img_src, img_dst)

            annotation = all_annotations[x]
            annotation_src = os.path.join(DEFECT_ANNOTATIONS, annotation)
            annotation_dst = os.path.join(DEFECT_TRAIN_FOLDER, annotation)
            shutil.copyfile(annotation_src, annotation_dst)

    for x in range(0, n_items-1):
        if x not in randoms_train and x not in randoms_val:
            img = all_imgs[x]
            img_src = os.path.join(DEFECT_IMAGES, img)
            img_dst = os.path.join(DEFECT_TRAIN_FOLDER, img)
            if not exists(img_src):
                warnings.warn("Image " + img_src + " doesn't exist...")
                continue
            shutil.copyfile(img_src, img_dst)

            annotation = all_annotations[x]
            annotation_src = os.path.join(DEFECT_ANNOTATIONS, annotation)
            annotation_dst = os.path.join(DEFECT_TRAIN_FOLDER, annotation)
            shutil.copyfile(annotation_src, annotation_dst)