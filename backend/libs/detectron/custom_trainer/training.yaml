#### Specifies the pretrained d2 model to use (see available models at detectron2 modelzoo)
config_model_zoo: "faster_rcnn_X_101_32x8d_FPN_3x.yaml"

#### Specifies a checkpoint to resume from, leave empty to train from scratch
resume_checkpoint: ""

#### ABSOLUTE path to the dataset containing one of the following options:
####  1) All individual annotation files of the dataset (only valid if split_ratio is specified)
####  2) 3 sub-folders where the dataset is already split dataset_path/train dataset_path/test dataset_path/val
####  3) The coco files of the dataset (train_coco.json, test_coco.json, val_coco.json)
dataset_path: "/home/<USER>/projects/defis/backend/bucket/defects"

coco_file: "backend/bucket/model/coco_train.json"

#### ABSOLUTE path where images are stored
img_prefix: "/home/<USER>/projects/defis/backend/bucket/defects/Images"

dataset_name: "defects"

#### Specifies the ratio [train, test, val] to split the samples presents in 'dataset_path'.
#### For example, an split_ratio == [0.8, 0.2, 0] will use 80% of the images for training and 20% for testing
#### If split_ratio == [] no split will me made and:
#### it will try to load the coco files 'dataset_path'/train_coco.json, 'dataset_path'/test_coco.json and 'dataset_path'/val_coco.json
#### if they are not present, then it will try to load the folders 'dataset_path'/train, 'dataset_path'/test and 'dataset_path'/test
#### and create the corresponding coco files
split_ratio: []

#### When 'split_ratio' != [], this ABSOLUTE path specifies where to store the split that will be done
split_folder: ""

#### Absolute path to the output directory
output_dir: "/home/<USER>/projects/defis/backend/bucket/model"

#### Specifies the name of the classes that will be registered
class_names: ["anomaly, stain, hole, spot, oil"]

#### Specifies the number of iterations for training process
iterations: 1000

#### True or False to specify whether evaluation will be carried out during training process or not
do_evaluation: True

#### Early stopping threshold
early_stopping: 0.1

#### Specifies how frequently the evaluation process will be carried out (in number of iterations)
#### Only considered when do_evaluation = True
evaluation_period: 100

#### Specifies the number of gpu that will be used for training
## DO NOT FORGET USE CUDA_VISIBLE_DEVICES.
gpu_to_use: 1

#### Specifies how frequently a checkpoint of the model will be created (in number of iterations)
checkpoint_period: 100

#### Species the amount of images used in each batch
images_per_batch: 16

#### Species the min and max size used for training. If -1, default D2 size will be used. If 0, original image size will be used
min_size: -1
max_size: -1

#### Augmentation settings. Choose between: ["fliplr", "flipud", "blur", "sharpen", "huesaturation", "colortemperature", "rotate", "cutout", "affine"]
do_augmentation: false
augmentations: ["fliplr", "flipud", "colortemperature", "rotate", "huesaturation", "blur"]
min_augmentations: 0
max_augmentations: 2

#### Sliding window settings
sliding_window: false
window_size: 400
window_overlapping: 0.5

### Learning rate
learning_rate: 0.00025
lr_steps: [15000,25000,35000,50000,70000]
lr_gamma: 0.00005

