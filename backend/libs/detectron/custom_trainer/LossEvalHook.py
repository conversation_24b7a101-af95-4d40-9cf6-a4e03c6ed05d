import numpy as np
from .engine.hooks import HookBase
from detectron2.utils.logger import log_every_n_seconds
import detectron2.utils.comm as comm
import torch
import time
import datetime
import logging
import sys


class LossEvalHook(HookBase):

    def __init__(self, do_evaluation, eval_period, model, data_loader,
                 consider_early_stopping, early_stopping_threshold, early_stopping_loss_percentage):
        self._model = model
        self._do_evaluation = do_evaluation
        self._period = eval_period
        self._data_loader = data_loader
        self._last_losses = []
        self._consider_early_stopping = consider_early_stopping
        self._early_stopping_threshold = early_stopping_threshold
        self._early_stopping_loss_percentage = early_stopping_loss_percentage
        self._best_validation_loss_value = sys.maxsize

    def _do_loss_eval(self):
        # Copying inference_on_dataset from evaluator.py
        total = len(self._data_loader)
        num_warmup = min(5, total - 1)

        start_time = time.perf_counter()
        total_compute_time = 0
        losses = []
        for idx, inputs in enumerate(self._data_loader):
            if idx == num_warmup:
                start_time = time.perf_counter()
                total_compute_time = 0
            start_compute_time = time.perf_counter()
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            total_compute_time += time.perf_counter() - start_compute_time
            iters_after_start = idx + 1 - num_warmup * int(idx >= num_warmup)
            seconds_per_img = total_compute_time / iters_after_start
            if idx >= num_warmup * 2 or seconds_per_img > 5:
                total_seconds_per_img = (time.perf_counter() - start_time) / iters_after_start
                eta = datetime.timedelta(seconds=int(total_seconds_per_img * (total - idx - 1)))
                log_every_n_seconds(
                    logging.INFO,
                    "Loss on Validation  done {}/{}. {:.4f} s / img. ETA={}".format(
                        idx + 1, total, seconds_per_img, str(eta)
                    ),
                    n=5,
                )
            loss_batch = self._get_loss(inputs)
            losses.append(loss_batch)
        mean_loss = np.mean(losses)
        self.trainer.storage.put_scalar('validation_loss', mean_loss)
        comm.synchronize()

        return losses

    def _get_loss(self, data):
        # How loss is calculated on train_loop
        metrics_dict = self._model(data)
        metrics_dict = {
            k: v.detach().cpu().item() if isinstance(v, torch.Tensor) else float(v)
            for k, v in metrics_dict.items()
        }
        total_losses_reduced = sum(loss for loss in metrics_dict.values())
        return total_losses_reduced

    def after_step(self):
        next_iter = self.trainer.iter + 1
        is_final = next_iter == self.trainer.max_iter
        if (self._do_evaluation and is_final) or (self._do_evaluation and self._period > 0 and next_iter % self._period == 0):
            losses = self._do_loss_eval()
            mean_loss = np.mean(losses)

            # If this is the lowest loss value, then save the model
            if mean_loss < self._best_validation_loss_value:
                model_name = "best_model"
                self.trainer.checkpointer.save(model_name)
                self._best_validation_loss_value = mean_loss
                print("New best model at iter: " + str(next_iter))

            # If early stopping is activated, then check if it is necessary to stop the training
            if self._consider_early_stopping:
                if len(self._last_losses) < self._early_stopping_threshold:
                    self._last_losses.append(mean_loss)
                else:
                    activate_early_stop = True
                    bad_but_not_enough = True
                    for _loss in self._last_losses:
                        if mean_loss < _loss + _loss * self._early_stopping_loss_percentage:
                            activate_early_stop = False
                        if mean_loss < _loss:
                            bad_but_not_enough = False
                    if activate_early_stop:
                        model_name = "model_early_stop_at_iter_"+str(next_iter)
                        self.trainer.checkpointer.save(model_name)
                        raise Exception("Stopping training at iteration " + str(next_iter) +
                                        " because early stopping has been activated\n" +
                                        "Actual average loss: " + str(mean_loss) + "\n" +
                                        "Last " + str(self._early_stopping_threshold) + " average losses: " +
                                        str(self._last_losses))
                    else:
                        if not bad_but_not_enough:
                            self._last_losses.pop(0)
                            self._last_losses.append(mean_loss)

        self.trainer.storage.put_scalars(timetest=12)
