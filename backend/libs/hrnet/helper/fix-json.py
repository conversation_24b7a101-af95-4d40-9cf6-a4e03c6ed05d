"""
# File: fix-json.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:
    When we use the script labelme2voc.py in order to create de PNG color masks from the JSON files, the masks have to be in the correct order.
    If the last label in the JSON is for example jacket, the masks correspondig to the jacket will hide the mask corresponding to the reflex stripes and damages.
    Therefore, in the JSON file, the order of "shapes" have to be the following:
    1. jacket, t-shirt, trousers or coveralls.
    2. reflex normal, reflex stripe or reflex prismen.
    3. damages.
    
    When we use autolabel, or simply if we manually label in an incorrect order, the PNG color masks necessaries to train wont show all the information.
    This script reads the JSON files and arrange the atributtes in the correct way.
"""

import json
import os

def sort_reflex_damaged(A):
    """
    A has to be a list composed of dictionaries.
    This function sets the dictionaries correspondig to reflex stripes after the clothes ones. Each dictionary is composed of "label", "points", ...
    """
    for i in range(0,len(A)):
        if(A[i]['label'] == 'damaged'):
            aux = A[i]
            j = i + 1
            continu = True
            while ((j < len(A)) and (continu == True)):
                if(A[j]['label'] != 'damaged'):
                    A[i] = A[j]
                    A[j] = aux
                    continu = False

                j = j + 1

    return A


def sort_cloth(A):
    """
    A has to be a list composed of dictionaries.
    This function sets in the first places of the list the dictionaries corresponding to clothes. Each dictionary is composed of "label", "points", ...
    """
    for i in range(0, len(A)):
        label = A[i]['label']
        if ((label != 'hose') or (label != 'jacke') or (label != 't-shirt') or (label != 'latzhose')):
            aux = A[i]
            j = i + 1
            continu = True
            while((j<len(A)) and (continu == True)):
                label = A[j]['label']
                if ((label == 'hose') or (label == 'jacke') or (label == 't-shirt') or (label == 'latzhose')):
                    A[i] = A[j]
                    A[j] = aux
                    continu = False
                
                j = j + 1

    return A

if __name__ == "__main__":
    path = 'stripes/no-damage/'
    output_path = 'output/'

    for ann_path in os.listdir(path):
        if ann_path[-4:] == "json" :
            with open(path + ann_path,'r') as jsonfile:
                anno = json.load(jsonfile)#this is a dictionary

            anno_shapes = anno['shapes']#This is a list composed of dictionaries.

            anno['shapes'] = sort_cloth(anno_shapes)
            anno['shapes'] = sort_reflex_damaged(anno_shapes)

            json_file = output_path + ann_path
            with open(json_file, 'w') as outfile:
                json.dump(anno, outfile, indent=4)

