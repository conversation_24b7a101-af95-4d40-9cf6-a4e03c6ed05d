import os
import argparse
import random
import shutil
import glob


def main():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("input_dir", help="path to mask directory")
    parser.add_argument("out_dir", help="path to mask directory")
    args = parser.parse_args()

    # iterate through all images
    if not os.path.exists(args.out_dir):
        os.mkdir(args.out_dir)


    source = args.input_dir
    dest_train = os.path.join(args.out_dir, "train/")
    dest_val = os.path.join(args.out_dir, "val/")
    dest_test = os.path.join(args.out_dir, "test/")

    train_images = os.path.join(dest_train, "images/")
    train_seg = os.path.join(dest_train, "seg/")
    val_images = os.path.join(dest_val, "images/")
    val_seg = os.path.join(dest_val, "seg/")
    test_images = os.path.join(dest_test, "images/")
    test_seg = os.path.join(dest_test, "seg/")

    files = glob.glob(os.path.join(args.input_dir + "*.png"))
    if not os.path.exists(dest_train):
        os.mkdir(dest_train)
    if not os.path.exists(dest_val):
        os.mkdir(dest_val)
    if not os.path.exists(dest_test):
        os.mkdir(dest_test)
    if not os.path.exists(train_images):
        os.mkdir(train_images)
    if not os.path.exists(train_seg):
        os.mkdir(train_seg)
    if not os.path.exists(val_images):
        os.mkdir(val_images)
    if not os.path.exists(val_seg):
        os.mkdir(val_seg)
    if not os.path.exists(test_images):
        os.mkdir(test_images)
    if not os.path.exists(test_seg):
        os.mkdir(test_seg)
    # files = os.listdir(source)

    #for i in range(random.randint(1,10)):
     #   print(i)
    #  random.shuffle(files)

    print(len(files))
    test = files[(int(len(files)*0.85)):]
    val = files[(int(len(files)*0.7)):(int(len(files)*0.85))]
    train = files[:(int(len(files)*0.7))]

    for i in train:
        i = os.path.basename(i)
        img_name = i.split(".")[0] + ".jpg"
        shutil.copyfile(os.path.join(source, i), os.path.join(train_seg + i))
        shutil.copyfile(os.path.join(source, img_name), os.path.join(train_images + img_name))

    for j in val:
        j = os.path.basename(j)
        img_name = j.split(".")[0] + ".jpg"
        shutil.copyfile(os.path.join(source, j), os.path.join(val_seg + j))
        shutil.copyfile(os.path.join(source, img_name), os.path.join(val_images + img_name))

    for k in test:
        k = os.path.basename(k)
        img_name = k.split(".")[0] + ".jpg"
        shutil.copyfile(os.path.join(source, k), os.path.join(test_seg + k))
        shutil.copyfile(os.path.join(source, img_name), os.path.join(test_images + img_name))


if __name__ == '__main__':
    main()
    # validate_label(args.path_save)
