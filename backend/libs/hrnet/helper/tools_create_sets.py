"""
# File: tools_create_sets.py
# Author: Desion GmbH.
# Copyright: Desion GmbH
# Description:
    This script includes some tools to arrange and clean a dataset.
"""

import argparse
import os
import shutil
import numpy as np


def read_files(path, extension=".jpg", depth=1):
    """
        Read images of dataset.
    """
    assert depth > 0, "Nothing to do!"

    images = []
    for root, dirs, files in os.walk(path):
        if root[len(path):].count(os.sep) < depth:
            for f in files:
                if os.path.splitext(f)[1] == extension:
                    images.append(os.path.join(root, f))

    #Returns a list with the path of each image.
    return images



def split_files_dataset(images_dir, annos_dir, images_output, annos_output, r_train):#r_train is the ratio that we use to train.
    """
    This function puts randomly 1-r_train pictures and annos in output_dir/image (/annos)
    """
    full_paths = read_files(images_dir, ".png", 1)#path of all images
    paths = [os.path.basename(path)[:-4] for path in full_paths]##remove ".jpg"
    l_paths = len(paths)#number of imgs we have in the folder.

    assert len(paths) > 0, "No images found in %s." % dataset_dir

    l_train = int(l_paths * r_train)#number of images we are gonna train with

    np.random.shuffle(paths)#paths randomly mixed

    for i in range (l_paths):
        if i > l_train:
            shutil.move(images_dir + paths[i] + ".png", images_output)  # move paths[i]
            shutil.move(annos_dir + paths[i] + ".png", annos_output)  # move paths[i]


def split_dataset(images_dir, r_train, output_dir):#r_train is the ratio that we use to train.
    """
    This function writes in a txt file (trainval.txt) r_train names of images and in another txt file (test.txt) 1-r_train
    """
    full_paths = read_files(images_dir, ".jpg", 1)#path of all images
    paths = [os.path.basename(path)[:-4] for path in full_paths]#remove ".jpg"
    l_paths = len(paths)#number of imgs we have in the folder.

    assert len(paths) > 0, "No images found in %s." % dataset_dir

    l_train = int(l_paths * r_train)##number of images we are gonna train with

    np.random.shuffle(paths)#paths randomly mixed

    with open(os.path.join(output_dir, "trainval.txt"), "w+") as file_train:
        with open(os.path.join(output_dir, "test.txt"), "w+") as file_test:
            for i in range(l_paths):
                if i < l_train:#If i is lower than the number of images we are gonna train with, the image is for training. If not, it is for testing.
                    file_train.write(paths[i] + "\n")
                else:
                    file_test.write(paths[i] + "\n")


def verify_images_annotations(dataset_dir):
    """
    This function removes the images which dont have annotation files and the annotation files which dont have images.
    Pay attention to the type of files and set it correctly. Images are usually jpg or png, and annos json or xml.
    """
    images = read_files(os.path.join(dataset_dir, "image/"), ".png")
    xmls = read_files(os.path.join(dataset_dir, "annos/"), ".json")

    filename_images = [os.path.basename(path)[:-4] for path in images]
    filename_xmls = [os.path.basename(path)[:-5] for path in xmls]

    [os.remove(images[index]) for index, image in enumerate(filename_images) if image not in filename_xmls]
    [os.remove(xmls[index]) for index, xml in enumerate(filename_xmls) if xml not in filename_images]


def main():

    #Always pay attention to whether the images are jpg or png ! Maybe you have to change it in the functions.
    
    directory="autolab/todas_juntas/train/"
    images_dir= directory + "images/"
    annos_dir= directory + "seg/"
      
    output_dir="autolab/todas_juntas/val/"
    images_output= output_dir + "images/"
    annos_output= output_dir + "seg/"
    r_train=0.9

    split_files_dataset(images_dir, annos_dir, images_output, annos_output, r_train)
    #split_dataset(images_dir,r_train,images_output)
    #verify_images_annotations(directory)



if __name__ == '__main__':

    main()
