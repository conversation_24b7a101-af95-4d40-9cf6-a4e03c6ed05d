import os
import cv2
import numpy as np
import pickle
import argparse


def get_args():
    parser = argparse.ArgumentParser(description='''Converter to convert images to label format for pytorch''')
    parser.add_argument('--path', default='../forest/data/segmentation/',
                        help='path to source images')
    parser.add_argument('--path_info', default='../flying_dataset/info.npy',
                        help='path to file with information of creation')
    parser.add_argument('--path_save', default='../flying_dataset/data/',
                        help='path folder to save converted images')
    return parser.parse_args()


args = get_args()


color2index = {
    (0,0,0): 0, #background
    (128, 0, 0): 1, #wood
    (0, 128, 0) : 2, #metal
    (0, 0, 128) : 3, #fabric
    (128, 0, 128) : 4, #leather
    (128,128,0) : 5, #mirror
    (0, 128, 128) : 6, #plastic
    (128, 128, 128,): 7, #glass
    (224,224,192): 0 #ignore
}

def rgb2mask(img):

    assert len(img.shape) == 3
    height, width, ch = img.shape
    assert ch == 3

    W = np.power(256, [[0],[1],[2]])

    img_id = img.dot(W).squeeze(-1)
    values = np.unique(img_id)

    mask = np.zeros(img_id.shape)

    for i, c in enumerate(values):
        try:
            mask[img_id==c] = color2index[tuple(img[img_id==c][0])]
        except:
            pass
    return mask


def convert(path, save_path):
    #save_path = os.path.join(save_path)
    print(save_path)
    if not os.path.exists(save_path):
        os.mkdir(save_path)
        print("Folder created! ", save_path)
    for file in os.listdir(path):
        if not file.endswith(".png"):
            continue
        im = cv2.imread(os.path.join(path, file))
        im = cv2.cvtColor(im, cv2.COLOR_BGR2RGB)
        new_im = rgb2mask(im)
        cv2.imwrite(os.path.join(save_path, file), new_im)


if __name__ == '__main__':
    convert(args.path, args.path_save)
    #validate_label(args.path_save)
