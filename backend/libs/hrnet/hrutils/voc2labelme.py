import numpy as np
import cv2
from imantics import Polygons, Mask
import json
import os
import argparse
from polylabel import polylabel

from runx.logx import logx
from config import cfg

def createJSON():
    input_dir = os.path.join(cfg.RESULT_DIR, 'pred/')
    out_dir = os.path.join(cfg.RESULT_DIR, 'label/')
    os.makedirs(out_dir, exist_ok=True)
    if cfg.DATASET.NAME == 'furniture':
        gray2label = {
            (128, 0, 0): 1,  # wood
            (0, 128, 0): 2,  # metal
            (0, 0, 128): 3,  # fabric
            (128, 0, 128): 4,  # leather
            (128, 128, 0): 5,  # mirror
            (0, 128, 128): 6,  # plastic
            (128, 128, 128,): 7,  # glass
            (0, 0, 0): 0  # background
            # (224,224,192): 8 #ignore
        }
    elif cfg.DATASET.NAME == 'wsp':
        gray2label = {
            (128, 0, 0): 4,  # damaged -red
            (0, 128, 0): 1,  # reflex_normal - green 
            (0, 0, 128): 2,  # reflex_stripe - blue 
            (128, 128, 0): 3, #reflex_prismen - yellow
            (128, 0, 128): 5, #hose
            (0, 128, 128): 6, #jacke
            (128, 128, 128): 7, #latzhose
            (224, 224, 192): 8, #t-shirt
            (0, 0, 0): 0  # background - black
            # (224,224,192): 8 #ignore
        }
    else:
        pass
    # iterate through all images
    directory = os.fsencode(input_dir)
    for idx, file in enumerate(os.listdir(directory)):
        filename = os.fsdecode(file)
        filepath = input_dir + filename
        data = {}  # data to be converted to json
        data['version'] = "4.5.6"
        data["shapes"] = []
        # load image
        if not filename.endswith(".png") or filename.endswith(".py"):
            continue
        img = cv2.imread(filepath)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        # split images in binary for all classes
        for lbl in gray2label:
            #print(lbl)
            if not lbl == (0,0,0):
                data = addlabeltodata(data, img, lbl)
            #break
        # convert to gray

        # img_arr = np.asarray(bgr)
        # print(bgr.dtype)

        data["imagePath"] = filename.split("_pred")[0] + ".png"
        data["imageData"] = None
        json_pth = out_dir + filename.split("_pred")[0] + ".json"
        with open(json_pth, 'w') as outfile:
            json.dump(data, outfile, indent=4)
        if idx % 20 == 0:
            logx.msg(f'generating labels[Iter: {idx + 1} / {len(os.listdir(directory))}]')


# def mask2bin(img):


def addlabeltodata(data, img, lbl):
    if cfg.DATASET.NAME == 'furniture':
        color2label = {
            (128, 0, 0): "wood",  # wood
            (0, 128, 0): "metal",  # metal
            (0, 0, 128): "fabric",  # fabric
            (128, 0, 128): "leather",  # leather
            (128, 128, 0): "mirror",  # mirror
            (0, 128, 128): "plastic",  # plastic
            (128, 128, 128,): "glass",  # glass
            (0, 0, 0): "background"  # background
            # (224,224,192): 8 #ignore
        }
    elif cfg.DATASET.NAME == 'wsp':#ESTO ESTA EN RGB
        color2label = {
            (0, 0, 128): "reflex_stripe",
            (0, 128, 0): "reflex_normal",
            (128, 0, 0): "damaged",
            (128, 128, 0): "reflex_prismen",
            (128, 0, 128): "hose", #hose
            (0, 128, 128): "jacke", #jacke
            (128, 128, 128): "latzhose", #latzhose
            (224, 224, 192): "t-shirt", #t-shirt
            (0, 0, 0): "background"  # background
            # (224,224,192): 8 #ignore
        }
    #create binary mask for given label
    assert len(img.shape) == 3
    height, width, ch = img.shape
    assert ch == 3
    mask = img.copy()

    lbl_mask = np.all(img == lbl, axis=-1)

    non_lbl_mask = np.any((img != lbl), axis=-1)
    other_lbl_mask = np.any((img != lbl), axis=-1)
    mask[lbl_mask] = [255, 255, 255]
    mask[non_lbl_mask] = [0, 0, 0]


    #mask = np.any(img != np.asarray(lbl), axis=-1)
    curr = data
    gray = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
    polygons = Mask(gray).polygons()
    point_arr = polygons.points
    for segment in point_arr:
        segment = segment.tolist()
        if len(segment) <= 3:
            continue
        #point = polylabel([segment])
        #print(point)
        #print(int(point[0]))
        #if (mask[int(point[1])][int(point[0])] != [0,0,0]).all():
        curr["shapes"].append({
            'label': color2label[lbl],
            'points': segment,
            "group_id": None,
            "shape_type": "polygon",
            "flags": {}
        })
    return curr


