"""
# File: hrnet.py
# Author: <PERSON>
# Copyright: Desion GmbH
# Description:

# Changelog:
    -.04.08.2021: Creation date
    - 21.09.2024: Optimized to make inferences up to 50% faster
"""
import os
from time import time

import cv2
import numpy
import numpy as np
import torch
# FIXME: needed to force cpu for GK
# torch.cuda.is_available = lambda : False
import torchvision.transforms
from PIL import Image
from matplotlib import pyplot as plt
from runx.logx import logx

logx.initialize(logdir="logs", global_rank=0)

from backend.libs.hrnet.network import get_model
from backend.libs.hrnet.loss.optimizer import restore_net
from backend.libs.hrnet.loss.utils import CrossEntropyLoss2d
from backend.libs.hrnet.hrutils.trnval_utils import flip_tensor, resize_tensor
import torchvision.transforms as standard_transforms
from backend.libs.hrnet import config as cfg

"""Next two functions should be in AI analyzer"""


def read_images(path, extension="jpg", filters=[], load=False, depth=1):
    assert depth > 0, "Nothing to do!"

    images = []
    for root, dirs, files in os.walk(path):
        if root[len(path):].count(os.sep) < depth:
            for f in files:
                if os.path.splitext(f)[1] == '.' + extension:
                    found = True
                    for flt in filters:
                        if flt not in f:
                            found = False
                            break

                    if found:
                        images.append(os.path.join(root, f))

    return images


class Hrnet:

    def __init__(self, model_path, palette, scales=[1.0], num_classes=10, gpu_id=0):

        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)

        # Enable CUDNN Benchmarking optimization
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
        cfg.update_dataset_cfg(num_classes, 255)
        # Initialize network
        # checkpoint = torch.load(model_path)
        checkpoint = torch.load(model_path, map_location={'cuda:0': 'cpu'})

        arch = 'network.ocrnet.HRNet_Mscale'
        criterion = CrossEntropyLoss2d(ignore_index=255)
        self._net = get_model(network=arch, num_classes=num_classes, criterion=None)
        self._net = torch.nn.DataParallel(self._net)
        self._net.eval()

        restore_net(self._net, checkpoint)

        # Sets the module in evaluation mode.
        # Define which color represents each class
        self._color_mapping = np.array(palette).reshape(-1, 3)

        # Transformations applied to the images
        mean_std = ([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        self._input_transform = standard_transforms.Compose([
            standard_transforms.ToTensor(),
            standard_transforms.Normalize(*mean_std)
        ])

        # This should be taken form config file
        self._flips = [0]
        self._scales = scales


    def evaluate(self, images):
        """
        Evaluate a single minibatch of images.
        :param images: List of image to be processed
        :returns: A list of mask. The index match with images aindex
        """
        torch.cuda.empty_cache()
        results = []

        for image in images:

            image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            image_tensor = self._input_transform(image)
            upsample = torch.nn.Upsample(scale_factor=1, align_corners=False, mode='bilinear')
            image_tensor = torch.unsqueeze(image_tensor, 0)
            image_tensor = upsample(image_tensor)

            # Convert the image into pytorch tensor image
            input_size = image_tensor.size(2), image_tensor.size(3)

            with torch.no_grad():
                output = 0.0
                for flip in self._flips:
                    for scale in self._scales:
                        if flip == 1:
                            inputs = flip_tensor([image_tensor], 3)
                        else:
                            inputs = image_tensor

                        infer_size = [round(sz * scale) for sz in input_size]

                        if scale != 1.0:
                            inputs = resize_tensor(inputs, infer_size)

                        inputs = {'images': inputs}

                        # Perform the image evaluation
                        output_dict = self._net(inputs)

                        _pred = output_dict['pred']

                        # resize tensor down to 1.0x scale in order to combine
                        # with other scales of prediction
                        if scale != 1.0:
                            _pred = resize_tensor(_pred, input_size)

                        if flip == 1:
                            output = output + flip_tensor(_pred, 3)
                        else:
                            output = output + _pred

            output = output / len(self._scales) / len(self._flips)

            # Assuming `output` is the raw output from HRNet
            output_data = torch.nn.functional.softmax(output, dim=1)

            # Get the predicted class for each pixel (argmax over the channels)
            predictions = torch.argmax(output_data, dim=1)

            # Get the colored map in tensor
            color_map = torch.tensor(self._color_mapping, device=predictions.device, dtype=torch.uint8)

            # Transform in a readable image
            colored_mask = color_map[predictions][0].cpu().numpy()

            results.append(colored_mask)

        return results, True


def display_plt(im_orig, im_mask):
    fig, (ax1, ax2) = plt.subplots(1, 2)
    ax1.imshow(im_orig)
    ax2.imshow(im_mask)
    plt.show()


def resize_image(img, scale_percent):
    """
    Resizes an image by a given scale percentage using OpenCV.
    :param img: The image to resize
    :param scale_percent: The percentage to scale the image by
    :return: The resized image
    """

    # Calculate the new dimensions of the image.
    width = int(img.shape[1] * scale_percent / 100)
    height = int(img.shape[0] * scale_percent / 100)

    # Resize the image.
    dim = (width, height)
    resized_img = cv2.resize(img, dim, interpolation=cv2.INTER_AREA)

    return resized_img


if __name__ == '__main__':

    logx.initialize(logdir="logs", global_rank=0)

    # model_path = "/home/<USER>/workspace/edu/dataset/wsp-pytorch/logs/train_wsp/ocrnet.HRNet_Mscale_petite-gharial_2021.05.02_18.50/best_checkpoint_ep72.pth"
    model_path = "model/20230116_wsp_615.pth"
    classes = ["Background", "reflex_normal", "damaged", "reflex_prismen", "reflex_stripe", "hose", "jacke", "latzhose",
               "t-shirt"]

    # Must be in BGR
    palette = [0, 0, 0,  # ----[0] black    -> Background
               0, 128, 0,  # - [1] green    -> Reflex Normal
               128, 0, 0,  # - [2] red      -> Damage
               192, 192, 0,  # [3] Gold     -> Reflex prismen
               0, 0, 128,  # - [4] Blue     -> Reflex stripes
               128, 0, 128,  # [5] Purple   -> Hosen
               128, 128, 128,  # [6] Gray     -> Jacke
               255, 0, 255,  # [7] Pink     -> Latzhose
               255, 128, 0]  # [8] Orange   -> T-Shirt
    zero_pad = 256 * 3 - len(palette)
    for i in range(zero_pad):
        palette.append(0)

    import src.data_handler as dh

    conf = dh.json_to_dict("configs/config.json")
    cf_hrnet = conf["analyser"]["stripes"]["model"]
    color_map = cf_hrnet["color_map"]

    palette = []
    for color, _ in color_map.values():
        palette += color

    hrnet = Hrnet(model_path=cf_hrnet["model_path"], palette=palette)

    folder = "data/wsp_to_process/stripes/"
    folder = "data/wsp_to_process/normal/"
    folder = "data/wsp_to_process/prismen/"
    folder = "data/wsp_to_process/prismen/"
    folder = "static/all/"
    paths = read_images(folder, extension="png")

    # cv2.namedWindow("wsp", cv2.WINDOW_NORMAL)
    # cv2.resizeWindow('image', 900, 900)
    for filename in paths:
        im = cv2.imread(filename)
        im_r = resize_image(im, 90)

        mask = hrnet.evaluate([im_r])

        display_plt(cv2.cvtColor(im_r, cv2.COLOR_BGR2RGB), mask[0])

        '''cv2.imshow("wsp", im)
        cv2.waitKey()
        cv2.imshow("wsp", mask[0])
        cv2.waitKey()'''
