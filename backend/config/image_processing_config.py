#!/usr/bin/env python3
"""
Image Processing Configuration for Medical Stocking Analysis System

This module contains configuration settings for image processing operations,
including downscaling for frontend visualization and async image saving.
"""

# =============================================================================
# FRONTEND IMAGE DOWNSCALING CONFIGURATION
# =============================================================================

# Scale factor for frontend visualization images (0.25 = 25% of original size)
# Recommended range: 0.20 - 0.30 (20-30% as requested)
FRONTEND_IMAGE_SCALE_FACTOR = 0.25

# Whether to save original full-resolution images asynchronously
# True: Save full-res images in background (recommended for performance)
# False: Save full-res images synchronously (original behavior)
SAVE_ORIGINAL_IMAGES_ASYNC = True

# =============================================================================
# IMAGE QUALITY SETTINGS
# =============================================================================

# JPEG quality for downscaled frontend images (1-100, higher = better quality)
FRONTEND_IMAGE_QUALITY = 85

# JPEG quality for full-resolution images (1-100, higher = better quality)
FULL_RESOLUTION_IMAGE_QUALITY = 95

# =============================================================================
# DOWNSCALING PRESETS
# =============================================================================

DOWNSCALE_PRESETS = {
    'ultra_fast': {
        'scale_factor': 0.15,  # 15% - very small, fastest loading
        'quality': 75,
        'description': 'Ultra-fast loading, lowest quality'
    },
    'fast': {
        'scale_factor': 0.20,  # 20% - small, fast loading
        'quality': 80,
        'description': 'Fast loading, good for real-time monitoring'
    },
    'balanced': {
        'scale_factor': 0.25,  # 25% - balanced size/quality (default)
        'quality': 85,
        'description': 'Balanced size and quality (recommended)'
    },
    'quality': {
        'scale_factor': 0.30,  # 30% - larger, better quality
        'quality': 90,
        'description': 'Higher quality, slower loading'
    },
    'high_quality': {
        'scale_factor': 0.40,  # 40% - high quality for detailed inspection
        'quality': 95,
        'description': 'High quality for detailed defect inspection'
    }
}

# =============================================================================
# ASYNC PROCESSING SETTINGS
# =============================================================================

# Maximum number of concurrent async image save operations
MAX_ASYNC_IMAGE_THREADS = 4

# Timeout for async image save operations (seconds)
ASYNC_IMAGE_SAVE_TIMEOUT = 30

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def apply_downscale_preset(preset_name: str):
    """
    Apply a downscaling preset configuration.
    
    Args:
        preset_name: Name of the preset ('ultra_fast', 'fast', 'balanced', 'quality', 'high_quality')
    
    Returns:
        dict: Configuration dictionary with scale_factor and quality
    
    Raises:
        ValueError: If preset_name is not found
    """
    if preset_name not in DOWNSCALE_PRESETS:
        available_presets = ', '.join(DOWNSCALE_PRESETS.keys())
        raise ValueError(f"Unknown preset '{preset_name}'. Available presets: {available_presets}")
    
    preset = DOWNSCALE_PRESETS[preset_name]
    
    # Update global configuration
    global FRONTEND_IMAGE_SCALE_FACTOR, FRONTEND_IMAGE_QUALITY
    FRONTEND_IMAGE_SCALE_FACTOR = preset['scale_factor']
    FRONTEND_IMAGE_QUALITY = preset['fast']
    
    print(f"Applied downscale preset '{preset_name}': {preset['description']}")
    print(f"Scale factor: {preset['scale_factor']} ({preset['scale_factor']*100:.0f}%)")
    print(f"Quality: {preset['quality']}")
    
    return preset

def get_current_config():
    """
    Get current image processing configuration.
    
    Returns:
        dict: Current configuration settings
    """
    return {
        'frontend_scale_factor': FRONTEND_IMAGE_SCALE_FACTOR,
        'frontend_quality': FRONTEND_IMAGE_QUALITY,
        'full_resolution_quality': FULL_RESOLUTION_IMAGE_QUALITY,
        'async_saving_enabled': SAVE_ORIGINAL_IMAGES_ASYNC,
        'max_async_threads': MAX_ASYNC_IMAGE_THREADS,
        'async_timeout': ASYNC_IMAGE_SAVE_TIMEOUT
    }

def calculate_size_reduction(original_width: int, original_height: int, scale_factor: float = None):
    """
    Calculate the size reduction for given dimensions and scale factor.
    
    Args:
        original_width: Original image width
        original_height: Original image height
        scale_factor: Scale factor (uses current config if None)
    
    Returns:
        dict: Size reduction information
    """
    if scale_factor is None:
        scale_factor = FRONTEND_IMAGE_SCALE_FACTOR
    
    new_width = int(original_width * scale_factor)
    new_height = int(original_height * scale_factor)
    
    original_pixels = original_width * original_height
    new_pixels = new_width * new_height
    
    size_reduction_ratio = new_pixels / original_pixels
    size_reduction_percent = (1 - size_reduction_ratio) * 100
    
    return {
        'original_size': (original_width, original_height),
        'new_size': (new_width, new_height),
        'original_pixels': original_pixels,
        'new_pixels': new_pixels,
        'scale_factor': scale_factor,
        'size_reduction_ratio': size_reduction_ratio,
        'size_reduction_percent': size_reduction_percent,
        'estimated_file_size_reduction': size_reduction_percent  # Approximate
    }

# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================

def validate_config():
    """
    Validate current configuration settings.
    
    Raises:
        ValueError: If configuration is invalid
    """
    if not (0.1 <= FRONTEND_IMAGE_SCALE_FACTOR <= 1.0):
        raise ValueError(f"FRONTEND_IMAGE_SCALE_FACTOR must be between 0.1 and 1.0, got {FRONTEND_IMAGE_SCALE_FACTOR}")
    
    if not (1 <= FRONTEND_IMAGE_QUALITY <= 100):
        raise ValueError(f"FRONTEND_IMAGE_QUALITY must be between 1 and 100, got {FRONTEND_IMAGE_QUALITY}")
    
    if not (1 <= FULL_RESOLUTION_IMAGE_QUALITY <= 100):
        raise ValueError(f"FULL_RESOLUTION_IMAGE_QUALITY must be between 1 and 100, got {FULL_RESOLUTION_IMAGE_QUALITY}")
    
    if not (1 <= MAX_ASYNC_IMAGE_THREADS <= 20):
        raise ValueError(f"MAX_ASYNC_IMAGE_THREADS must be between 1 and 20, got {MAX_ASYNC_IMAGE_THREADS}")
    
    if not (1 <= ASYNC_IMAGE_SAVE_TIMEOUT <= 300):
        raise ValueError(f"ASYNC_IMAGE_SAVE_TIMEOUT must be between 1 and 300 seconds, got {ASYNC_IMAGE_SAVE_TIMEOUT}")

# =============================================================================
# EXAMPLE USAGE
# =============================================================================

if __name__ == "__main__":
    print("Image Processing Configuration")
    print("=" * 50)
    
    # Show current configuration
    config = get_current_config()
    print("Current Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\nAvailable Presets:")
    for preset_name, preset_config in DOWNSCALE_PRESETS.items():
        print(f"  {preset_name}: {preset_config['description']}")
        print(f"    Scale: {preset_config['scale_factor']} ({preset_config['scale_factor']*100:.0f}%)")
        print(f"    Quality: {preset_config['quality']}")
    
    # Example size calculation
    print(f"\nSize Reduction Example (1920x1080 image):")
    size_info = calculate_size_reduction(1920, 1080)
    print(f"  Original: {size_info['original_size']} ({size_info['original_pixels']:,} pixels)")
    print(f"  Downscaled: {size_info['new_size']} ({size_info['new_pixels']:,} pixels)")
    print(f"  Size reduction: {size_info['size_reduction_percent']:.1f}%")
    
    # Validate configuration
    try:
        validate_config()
        print("\n Configuration is valid")
    except ValueError as e:
        print(f"\n Configuration error: {e}")
