#!/usr/bin/env python3
"""
Configuration file for servo optimization parameters.
Adjust these values to optimize servo response time.
"""

# =============================================================================
# SERVO TIMING OPTIMIZATION PARAMETERS
# =============================================================================

# Exit hysteresis multiplier - controls how long to wait after object passes
# Lower values = faster analysis start, but risk cutting off the object
# Higher values = safer but slower analysis start
# 
# Original: 10 (very safe, ~5 second delay)
# Optimized: 2 (faster, ~1 second delay)
# Aggressive: 1 (fastest, ~0.5 second delay, higher risk)
EXIT_HYSTERESIS_MULTIPLIER = 2

# Early analysis minimum lines - minimum image data needed to start analysis
# Lower values = earlier analysis start, but less image data
# Higher values = more image data, but later analysis start
#
# Conservative: FRAME_SIZE * 4 (800 lines)
# Balanced: FRAME_SIZE * 3 (600 lines) 
# Aggressive: FRAME_SIZE * 2 (400 lines)
EARLY_ANALYSIS_MIN_LINES_MULTIPLIER = 3

# Early analysis trigger threshold - how many lines below threshold before starting early analysis
# This ensures the object is likely finishing before we start analysis
#
# Conservative: FRAME_SIZE * 2 (400 lines)
# Balanced: FRAME_SIZE * 1 (200 lines)
# Aggressive: FRAME_SIZE * 0.5 (100 lines)
EARLY_ANALYSIS_TRIGGER_MULTIPLIER = 1

# =============================================================================
# PERFORMANCE MONITORING PARAMETERS
# =============================================================================

# Servo delay warning threshold (seconds)
# Log warnings when total delay exceeds this value
SERVO_DELAY_WARNING_THRESHOLD = 5.0

# Step delay warning threshold (seconds)
# Log warnings when any individual step takes longer than this
STEP_DELAY_WARNING_THRESHOLD = 2.0

# =============================================================================
# ANALYSIS OPTIMIZATION PARAMETERS
# =============================================================================

# Enable early analysis (parallel processing)
# True: Start analysis before event completely finishes (faster)
# False: Wait for complete event before analysis (safer)
ENABLE_EARLY_ANALYSIS = True

# Enable parallel analysis for both cameras
# True: Both cameras can analyze simultaneously
# False: Sequential analysis (safer for resource-limited systems)
ENABLE_PARALLEL_CAMERA_ANALYSIS = True

# AI Model optimization parameters
# Image resize factor for AI analysis (1.0 = original size, 0.5 = half size)
# Lower values = faster analysis but potentially lower accuracy
AI_IMAGE_RESIZE_FACTOR = 1.0

# Enable AI model caching/optimization
ENABLE_AI_OPTIMIZATION = True

# Skip mask extraction for speed (less accurate but faster)
SKIP_MASK_EXTRACTION = False

# Skip color extraction for speed
SKIP_COLOR_EXTRACTION = False

# Skip regular analysis if early analysis was completed
SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE = True

# =============================================================================
# HARDWARE-SPECIFIC PARAMETERS
# =============================================================================

# Servo command timeout (seconds)
# How long to wait for servo to respond to commands
SERVO_COMMAND_TIMEOUT = 0.2

# Serial communication delay (seconds)
# Delay after sending servo commands
SERVO_COMMUNICATION_DELAY = 0.1

# =============================================================================
# PRESET CONFIGURATIONS
# =============================================================================

PRESETS = {
    "conservative": {
        "EXIT_HYSTERESIS_MULTIPLIER": 5,
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 4,
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 2,
        "ENABLE_EARLY_ANALYSIS": False,
        "AI_IMAGE_RESIZE_FACTOR": 1.0,
        "ENABLE_AI_OPTIMIZATION": False,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": False,
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": False,
        "description": "Safe settings, slower but very reliable"
    },
    
    "balanced": {
        "EXIT_HYSTERESIS_MULTIPLIER": 2,
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 3,
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 1,
        "ENABLE_EARLY_ANALYSIS": True,
        "AI_IMAGE_RESIZE_FACTOR": 1.0,
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": False,
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": False,  # Keep both analyses for safety
        "description": "Good balance of speed and reliability"
    },
    
    "aggressive": {
        "EXIT_HYSTERESIS_MULTIPLIER": 0.5,  # Even more aggressive
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 1.5,  # Start earlier
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 0.2,  # Trigger sooner
        "ENABLE_EARLY_ANALYSIS": True,
        "AI_IMAGE_RESIZE_FACTOR": 0.8,
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": True,
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": False,  # Keep both for now
        "description": "Fastest settings, higher risk of cutting off objects"
    },
    
    "production": {
        "EXIT_HYSTERESIS_MULTIPLIER": 2,
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 3,
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 1,
        "ENABLE_EARLY_ANALYSIS": True,
        "AI_IMAGE_RESIZE_FACTOR": 0.9,
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": True,
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": False,  # Keep both for production safety
        "description": "Recommended settings for production use"
    },

    "ultra_aggressive": {
        "EXIT_HYSTERESIS_MULTIPLIER": 0.3,  # Extremely fast
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 1,  # Start as soon as possible
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 0.1,  # Almost immediate trigger
        "ENABLE_EARLY_ANALYSIS": True,
        "AI_IMAGE_RESIZE_FACTOR": 0.5,  # Half resolution for speed
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": True,  # Skip for maximum speed
        "SKIP_COLOR_EXTRACTION": True,  # Skip for maximum speed
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": True,  # No duplicate analysis
        "description": "Extremely fast settings - use only for testing/debugging"
    },

    "speed_optimized": {
        "EXIT_HYSTERESIS_MULTIPLIER": 1,
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 2,
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 0.5,
        "ENABLE_EARLY_ANALYSIS": True,
        "AI_IMAGE_RESIZE_FACTOR": 0.7,  # Reduced resolution for speed
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": True,  # Skip color extraction
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": True,
        "description": "Optimized for AI model speed while maintaining accuracy"
    },

    "no_duplicate_analysis": {
        "EXIT_HYSTERESIS_MULTIPLIER": 0.5,  # Fast event end
        "EARLY_ANALYSIS_MIN_LINES_MULTIPLIER": 4,
        "EARLY_ANALYSIS_TRIGGER_MULTIPLIER": 2,
        "ENABLE_EARLY_ANALYSIS": False,  # Disable partial analysis
        "AI_IMAGE_RESIZE_FACTOR": 0.8,
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": True,
        "SKIP_REGULAR_ANALYSIS_IF_EARLY_DONE": False,  # Always do full analysis
        "description": "Fast full analysis - no partial early analysis"
    },

    "immediate_full_analysis": {
        "EXIT_HYSTERESIS_MULTIPLIER": 0.3,  # Extremely fast event end
        "AI_IMAGE_RESIZE_FACTOR": 0.7,  # Slight AI optimization
        "ENABLE_AI_OPTIMIZATION": True,
        "SKIP_MASK_EXTRACTION": False,
        "SKIP_COLOR_EXTRACTION": True,
        "description": "Immediate full analysis on complete sock image"
    }
}

# =============================================================================
# CONFIGURATION FUNCTIONS
# =============================================================================

def apply_preset(preset_name: str):
    """Apply a preset configuration."""
    if preset_name not in PRESETS:
        raise ValueError(f"Unknown preset: {preset_name}. Available: {list(PRESETS.keys())}")
    
    preset = PRESETS[preset_name]
    globals().update({k: v for k, v in preset.items() if k != "description"})
    
    print(f"Applied preset '{preset_name}': {preset['description']}")
    print_current_config()

def print_current_config():
    """Print the current configuration."""
    print("\nCurrent Servo Optimization Configuration:")
    print("=" * 50)
    print(f"Exit Hysteresis Multiplier: {EXIT_HYSTERESIS_MULTIPLIER}")
    print(f"Early Analysis Min Lines Multiplier: {EARLY_ANALYSIS_MIN_LINES_MULTIPLIER}")
    print(f"Early Analysis Trigger Multiplier: {EARLY_ANALYSIS_TRIGGER_MULTIPLIER}")
    print(f"Enable Early Analysis: {ENABLE_EARLY_ANALYSIS}")
    print(f"Enable Parallel Camera Analysis: {ENABLE_PARALLEL_CAMERA_ANALYSIS}")
    print(f"Servo Delay Warning Threshold: {SERVO_DELAY_WARNING_THRESHOLD}s")
    print(f"Step Delay Warning Threshold: {STEP_DELAY_WARNING_THRESHOLD}s")

def get_optimization_summary():
    """Get a summary of expected performance improvements."""
    base_delay = 5.0  # Original delay in seconds
    
    # Calculate expected delay reduction
    hysteresis_reduction = (10 - EXIT_HYSTERESIS_MULTIPLIER) / 10 * 4.0  # ~4s from hysteresis
    early_analysis_reduction = 1.0 if ENABLE_EARLY_ANALYSIS else 0.0  # ~1s from early analysis
    
    total_reduction = hysteresis_reduction + early_analysis_reduction
    expected_delay = max(0.5, base_delay - total_reduction)
    
    print(f"\nExpected Performance Improvement:")
    print("=" * 40)
    print(f"Original delay: ~   {base_delay:.1f}s")
    print(f"Hysteresis reduction: -{hysteresis_reduction:.1f}s")
    print(f"Early analysis reduction: -{early_analysis_reduction:.1f}s")
    print(f"Expected new delay: ~{expected_delay:.1f}s")
    print(f"Total improvement: {total_reduction:.1f}s ({total_reduction/base_delay*100:.0f}%)")

if __name__ == "__main__":
    print("Servo Optimization Configuration")
    print("=" * 50)
    
    print("\nAvailable presets:")
    for name, preset in PRESETS.items():
        print(f"  {name}: {preset['description']}")
    
    print_current_config()
    get_optimization_summary()
    
    print(f"\nTo apply a preset, use:")
    print(f"from backend.config.servo_optimization_config import apply_preset")
    print(f"apply_preset('balanced')  # or 'conservative', 'aggressive', 'production'")
