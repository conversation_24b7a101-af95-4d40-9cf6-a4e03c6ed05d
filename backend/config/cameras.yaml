simulator:
  "Simulator1":
    alias: "Simulator 1 Top"
    images_path: "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/simulator_data"
    interval: 2
    difference: 240500
  "Simulator2":
    alias: "Simulator 2 Bottom"
    images_path: "/mnt/data/projects/defis-medical/user-interfaces/medical/public/static/simulator_data"
    interval: 2
    difference: 240500

line:
  "DA4274329":
    width: 4096
    height: 2048
    crop_left: 100
    crop_right: 100 
    type: "line" 
    alias: "Line Top" 
    difference: 240500
    camsetting_file: "backend/config/Line_Top_MV-CL042-91GC_DA4274329.mfs"

  "DA4274239":
    width: 4096
    height: 2048
    crop_left: 100
    crop_right: 100 
    type: "line"
    alias: "Line Bottom"
    difference: 240500
    camsetting_file: "backend/config/Line_Bottom_MV-CL042-91GC_DA4274239.mfs"



color_settings:

#configurated 
  "black": 
      "exposure": 300
      "difference": 
          "DA4274329": None
          "DA4274239": None
      "gain": 
          "DA4274329": 7.5
          "DA4274239": 7
      "sharpness": 
          "DA4274329": None
          "DA4274239": None
      "deviation": 
          "DA4274329": None
          "DA4274239": None
      "low_tuple": 
          "DA4274329": None     #[21,37,55]
          "DA4274239": None        #[17,16,14]  [21, 22, 17] 
      "high_tuple": 
          "DA4274329": None #[200,202,216]
          "DA4274239": None #[175, 147, 162]

  #configured with sharpness, could maybe be improved with color backgrounds
  "brown": 
      "exposure": 80
      "difference": 
          "DA4274329": None
          "DA4274239": None
      "gain": 
          "DA4274329": 10
          "DA4274239": 10
      "sharpness": 
          "DA4274329": 170
          "DA4274239": 150
      "deviation": 
          "DA4274329": None
          "DA4274239": None            
      "low_tuple": 
          "DA4274329": None
          "DA4274239": None
      "high_tuple": 
          "DA4274329": None
          "DA4274239": None

  #configured with sharpness, could maybe be improved with color backgrounds
  "dark_brown": 
      "exposure": 80
      "difference": 
          "DA4274329": None
          "DA4274239": None
      "gain": 
          "DA4274329": 10
          "DA4274239": 10
      "sharpness": 
          "DA4274329": 350
          "DA4274239": 350
      "deviation": 
          "DA4274329": None
          "DA4274239": None            
      "low_tuple": 
          "DA4274329": None
          "DA4274239": None
      "high_tuple": 
          "DA4274329": None
          "DA4274239": None


  #configured with sharpness, could maybe be improved with color backgrounds
  "orange": 
      "exposure": 80
      "difference": 
          "DA4274329": None
          "DA4274239": None
      "gain": 
          "DA4274329": 10
          "DA4274239": 10
      "sharpness": 
          "DA4274329": 170
          "DA4274239": 150
      "deviation": 
          "DA4274329": None
          "DA4274239": None         
      "low_tuple": 
          "DA4274329": None
          "DA4274239": None
      "high_tuple": 
          "DA4274329": None
          "DA4274239": None

  #configured with colors
  "white": 
      "exposure": 40
      "difference": 
          "DA4274329": None
          "DA4274239": None
      "gain": 
          "DA4274329": 8.5
          "DA4274239": 8.5
      "sharpness": 
          "DA4274329": None
          "DA4274239": None
      "deviation": 
          "DA4274329": 0.1
          "DA4274239": 0.1            
      "low_tuple": 
          "DA4274329": [0,0,0]
          "DA4274239": [0,0,0]
      "high_tuple": 
          "DA4274329": [60,60,60]
          "DA4274239": [60, 60,60]
