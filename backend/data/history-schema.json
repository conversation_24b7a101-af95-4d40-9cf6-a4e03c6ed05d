{"columns": [{"name": "Decision", "type": "string", "values": ["OK", "NOTOK", "MAN"]}, {"name": "ID", "type": "string"}, {"name": "Date", "type": "date", "format": "YYYY-MM-DD"}, {"name": "Hour", "type": "time", "format": "HH:mm:ss"}, {"name": "Product", "type": "string", "values": ["Stockings", "Socks", "Tights"]}, {"name": "Length leg (cm)", "type": "float"}, {"name": "Ankle width (cm)", "type": "float"}, {"name": "Panty length (cm)", "type": "float"}], "views": [{"name": "Default View", "columns": ["Decision", "ID", "Date", "Hour", "Product", "Length leg", "Ankle width", "Panty length"]}, {"name": "Compact View", "columns": ["Decision", "ID", "Date", "Product"]}]}