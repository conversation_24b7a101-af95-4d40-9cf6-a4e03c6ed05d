# Socket.IO Sock Events Documentation

## Overview

The medical stocking analysis system now emits real-time events via Socket.IO whenever sock detection and analysis events occur. This allows the user interface to react immediately to sock events for enhanced user experience.

## Events Emitted

### 1. `sock_event`

Emitted for general sock lifecycle events.

**Event Data Structure:**
```javascript
{
    sock_id: 165,                    // Unique sock identifier
    camera: "Line Top",              // Camera name ("Line Top" or "Line Bottom")
    event_type: "detected",          // Event type (see below)
    timestamp: **********.433,       // Unix timestamp
    formatted_time: "11:17:48.433"   // Human-readable time (HH:MM:SS.mmm)
}
```

**Event Types:**
- `detected` - Sock detected by camera
- `analysis_start` - AI analysis started
- `analysis_complete` - AI analysis completed
- `servo_decision` - Servo decision made

### 2. `servo_decision`

Emitted specifically for servo decisions with detailed information.

**Event Data Structure:**
```javascript
{
    sock_id: 165,                    // Unique sock identifier
    decision: "REJECT",              // "ACCEPT" or "REJECT"
    defects_found: true,             // Boolean indicating if defects were found
    timestamp: 1751534272.649,       // Unix timestamp
    formatted_time: "11:17:52.649"   // Human-readable time (HH:MM:SS.mmm)
}
```

## Frontend Implementation Example

### JavaScript/React Example

```javascript
import io from 'socket.io-client';

// Connect to the Socket.IO server
const socket = io('http://localhost:3006');

// Listen for sock events
socket.on('sock_event', (data) => {
    console.log('Sock event received:', data);
    
    switch(data.event_type) {
        case 'detected':
            console.log(`Sock ${data.sock_id} detected by ${data.camera} at ${data.formatted_time}`);
            // Update UI to show sock detection
            showSockDetection(data.sock_id, data.camera, data.formatted_time);
            break;
            
        case 'analysis_start':
            console.log(`Analysis started for sock ${data.sock_id} on ${data.camera}`);
            // Update UI to show analysis in progress
            showAnalysisProgress(data.sock_id, data.camera, 'started');
            break;
            
        case 'analysis_complete':
            console.log(`Analysis completed for sock ${data.sock_id} on ${data.camera}`);
            // Update UI to show analysis completed
            showAnalysisProgress(data.sock_id, data.camera, 'completed');
            break;
            
        case 'servo_decision':
            console.log(`Servo decision made for sock ${data.sock_id}`);
            // This will be followed by a more detailed servo_decision event
            break;
    }
});

// Listen for detailed servo decisions
socket.on('servo_decision', (data) => {
    console.log('Servo decision:', data);
    
    // Update UI with final decision
    showServoDecision(data.sock_id, data.decision, data.defects_found, data.formatted_time);
    
    // Show notification
    if (data.decision === 'REJECT') {
        showNotification(`Sock ${data.sock_id} REJECTED - Defects found`, 'error');
    } else {
        showNotification(`Sock ${data.sock_id} ACCEPTED - No defects`, 'success');
    }
});

// Example UI update functions
function showSockDetection(sockId, camera, time) {
    // Add to detection log
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<span class="time">${time}</span> - Sock ${sockId} detected by ${camera}`;
    logEntry.className = 'detection-log-entry';
    document.getElementById('detection-log').appendChild(logEntry);
    
    // Update status indicator
    const statusIndicator = document.getElementById(`${camera.toLowerCase().replace(' ', '-')}-status`);
    if (statusIndicator) {
        statusIndicator.className = 'status-active';
        statusIndicator.textContent = `Processing Sock ${sockId}`;
    }
}

function showAnalysisProgress(sockId, camera, status) {
    const progressElement = document.getElementById(`sock-${sockId}-progress`);
    if (progressElement) {
        progressElement.textContent = `${camera}: Analysis ${status}`;
        progressElement.className = status === 'started' ? 'analysis-in-progress' : 'analysis-complete';
    }
}

function showServoDecision(sockId, decision, defectsFound, time) {
    // Update decision display
    const decisionElement = document.getElementById('latest-decision');
    if (decisionElement) {
        decisionElement.innerHTML = `
            <div class="decision-${decision.toLowerCase()}">
                <h3>Sock ${sockId}: ${decision}</h3>
                <p>Time: ${time}</p>
                <p>Defects: ${defectsFound ? 'Yes' : 'No'}</p>
            </div>
        `;
    }
    
    // Update statistics
    updateStatistics(decision);
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add to notification area
    document.getElementById('notifications').appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function updateStatistics(decision) {
    // Update counters
    const totalElement = document.getElementById('total-processed');
    const acceptedElement = document.getElementById('total-accepted');
    const rejectedElement = document.getElementById('total-rejected');
    
    if (totalElement) {
        totalElement.textContent = parseInt(totalElement.textContent) + 1;
    }
    
    if (decision === 'ACCEPT' && acceptedElement) {
        acceptedElement.textContent = parseInt(acceptedElement.textContent) + 1;
    } else if (decision === 'REJECT' && rejectedElement) {
        rejectedElement.textContent = parseInt(rejectedElement.textContent) + 1;
    }
}
```

### Vue.js Example

```javascript
// In your Vue component
export default {
    data() {
        return {
            socket: null,
            sockEvents: [],
            currentSocks: {},
            statistics: {
                total: 0,
                accepted: 0,
                rejected: 0
            }
        }
    },
    
    mounted() {
        // Initialize Socket.IO connection
        this.socket = io('http://localhost:3006');
        
        // Listen for sock events
        this.socket.on('sock_event', this.handleSockEvent);
        this.socket.on('servo_decision', this.handleServoDecision);
    },
    
    beforeDestroy() {
        if (this.socket) {
            this.socket.disconnect();
        }
    },
    
    methods: {
        handleSockEvent(data) {
            // Add to events log
            this.sockEvents.unshift({
                ...data,
                id: Date.now() // Add unique ID for Vue key
            });
            
            // Keep only last 100 events
            if (this.sockEvents.length > 100) {
                this.sockEvents = this.sockEvents.slice(0, 100);
            }
            
            // Update current sock status
            if (!this.currentSocks[data.sock_id]) {
                this.currentSocks[data.sock_id] = {
                    id: data.sock_id,
                    topStatus: null,
                    bottomStatus: null,
                    decision: null
                };
            }
            
            const camera = data.camera.toLowerCase().includes('top') ? 'top' : 'bottom';
            this.currentSocks[data.sock_id][`${camera}Status`] = data.event_type;
        },
        
        handleServoDecision(data) {
            // Update sock decision
            if (this.currentSocks[data.sock_id]) {
                this.currentSocks[data.sock_id].decision = data.decision;
                this.currentSocks[data.sock_id].defectsFound = data.defects_found;
            }
            
            // Update statistics
            this.statistics.total++;
            if (data.decision === 'ACCEPT') {
                this.statistics.accepted++;
            } else {
                this.statistics.rejected++;
            }
            
            // Show toast notification
            this.$toast.show({
                message: `Sock ${data.sock_id}: ${data.decision}`,
                type: data.decision === 'ACCEPT' ? 'success' : 'error',
                duration: 3000
            });
        }
    }
}
```

## Event Timeline Example

For a typical sock processing cycle, you would receive events in this order:

```
1. sock_event: { sock_id: 165, camera: "Line Top", event_type: "detected" }
2. sock_event: { sock_id: 165, camera: "Line Top", event_type: "analysis_start" }
3. sock_event: { sock_id: 165, camera: "Line Top", event_type: "analysis_complete" }
4. sock_event: { sock_id: 165, camera: "Line Bottom", event_type: "detected" }
5. sock_event: { sock_id: 165, camera: "Line Bottom", event_type: "analysis_start" }
6. sock_event: { sock_id: 165, camera: "Line Bottom", event_type: "analysis_complete" }
7. sock_event: { sock_id: 165, camera: "SERVO", event_type: "servo_decision" }
8. servo_decision: { sock_id: 165, decision: "REJECT", defects_found: true }
```

## Use Cases

1. **Real-time Status Display**: Show which socks are currently being processed
2. **Progress Tracking**: Display analysis progress for each camera
3. **Live Statistics**: Update counters and charts in real-time
4. **Notifications**: Alert users of decisions and important events
5. **Debugging**: Log events for troubleshooting and analysis
6. **Performance Monitoring**: Track processing times and throughput

## Connection Details

- **Server URL**: `http://localhost:3006` (or your configured host/port)
- **Events**: `sock_event`, `servo_decision`
- **Connection**: Standard Socket.IO client connection
- **CORS**: Enabled for all origins (`*`)

## Error Handling

The system includes error handling for Socket.IO emissions. If an event fails to emit, it will be logged as a warning but won't interrupt the analysis process.

```javascript
// Example error handling in frontend
socket.on('connect_error', (error) => {
    console.error('Socket.IO connection error:', error);
    // Handle connection issues
});

socket.on('disconnect', (reason) => {
    console.warn('Socket.IO disconnected:', reason);
    // Handle disconnection
});
```
