import serial
import time

# Configuración del puerto serial
arduino = serial.Serial(port='/dev/ttyUSB1', baudrate=115200, timeout=0.1)

def send_command(command_id):
    """
    Envía un comando al ESP para controlar el servo.
    
    Args:
        command_id (str): ID del comando ('l' para mover a un lado, 'r' para mover al otro).
    """
    try:
        arduino.write(bytes(command_id, 'utf-8'))  # Enviar el ID como bytes
        time.sleep(0.1)  # Esperar una respuesta
        response = arduino.readline().decode('utf-8').strip()  # Leer la respuesta
        return response
    except Exception as e:
        print(f"Error al enviar comando: {e}")
        return None

if __name__ == "__main__":
    print("Control del servo iniciado. IDs disponibles:")
    print("m: Mover el servo al centro")
    print("l: Mover el servo a la derecha")
    print("r: Mover el servo a la izquierda")
    print("q: Salir")

    while True:
        command = input("Introduce el ID del comando: ").strip()
        if command.lower() == 'q':
            print("Saliendo del programa...")
            break
        elif command in ['m','l', 'r']:
            response = send_command(command)
            if response:
                print(f"Respuesta del ESP: {response}")
            else:
                print("No se recibió respuesta del ESP.")
        else:
            print("Comando no válido. Introduce '1', '2' o 'q'.")
