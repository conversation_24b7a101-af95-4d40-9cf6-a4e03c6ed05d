#include <SCServo.h>
SMS_STS st;

// Configuración de pines UART para el ESP32
#define S_RXD 18
#define S_TXD 19

// IDs y configuración del servo
byte ID = 1; // ID del servo
s16 Position = 0; // Posición inicial del servo
u16 Speed = 3400; // Velocidad del servo
byte ACC = 50; // Aceleración del servo

void setup() {
  // Configuración del puerto serial para comunicación con el ESP32
  Serial.begin(115200); // Comunicación con el PC
  Serial1.begin(1000000, SERIAL_8N1, S_RXD, S_TXD); // Comunicación con el servo
  st.pSerial = &Serial1;

  // Mensaje de inicio
  Serial.println("Servo control iniciado. Esperando comandos...");
}

void loop() {
  // Verificar si hay datos disponibles en el puerto serial
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n'); // Leer el comando recibido
    command.trim(); // Eliminar espacios en blanco

    if (command == "1") {
      // Mover el servo a la posición 3000
      Position = 3000;
      st.WritePosEx(ID, Position, Speed, ACC);
      Serial.println("Servo movido a la posición 3000.");
    } else if (command == "2") {
      // Mover el servo a la posición 100
      Position = 100;
      st.WritePosEx(ID, Position, Speed, ACC);
      Serial.println("Servo movido a la posición 100.");
    } else {
      // Comando no reconocido
      Serial.println("Comando no válido. Usa '1' o '2'.");
    }
  }
}
